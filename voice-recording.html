<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Recording - High-Quality Audio Capture</title>
    <link rel="stylesheet" href="styles.css">
    <script src="node_modules/jquery/dist/jquery.min.js"></script>
    <script src="assets/js/translations.js"></script>
    <script src="assets/js/translator.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0F6FFF',
                        secondary: '#00B0B6',
                        tertiary: '#00D695',
                        dark: '#0A2540',
                        light: '#F7F9FC',
                    },
                    animation: {
                        'fade-in-up': 'fadeInUp 0.5s ease-out',
                        'fade-in': 'fadeIn 0.5s ease-out',
                        'slide-in-right': 'slideInRight 0.5s ease-out',
                        'blob': 'blob 7s infinite',
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'wave': 'wave 10s linear infinite',
                    },
                    keyframes: {
                        fadeInUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideInRight: {
                            '0%': { transform: 'translateX(100px)', opacity: '0' },
                            '100%': { transform: 'translateX(0)', opacity: '1' },
                        },
                        blob: {
                            '0%': {
                                transform: 'translate(0px, 0px) scale(1)'
                            },
                            '33%': {
                                transform: 'translate(30px, -50px) scale(1.1)'
                            },
                            '66%': {
                                transform: 'translate(-20px, 20px) scale(0.9)'
                            },
                            '100%': {
                                transform: 'translate(0px, 0px) scale(1)'
                            },
                        },
                        float: {
                            '0%, 100%': {
                                transform: 'translateY(0)'
                            },
                            '50%': {
                                transform: 'translateY(-20px)'
                            }
                        },
                        wave: {
                            '0%': { transform: 'rotate(0deg)' },
                            '100%': { transform: 'rotate(360deg)' }
                        }
                    },
                },
            },
        }
    </script>
    <link rel="stylesheet" href="node_modules/@fortawesome/fontawesome-free/css/all.min.css">
    <link rel="stylesheet" href="dark-theme.css">
    <!-- Voeg Supabase client toe -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js"></script>
    <script>
    // === SUPABASE REALTIME HEARTBEAT ===
    // Projectgegevens van gebruiker:
    const supabaseUrl = 'https://qnrrzmzzullqkrdtsagt.supabase.co';
    const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFucnJ6bXp6dWxscWtyZHRzYWd0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3OTIxMTQsImV4cCI6MjA2NDM2ODExNH0.dYy9BNAwn5ld6bvqRuUij2iQ7KyyxLm4io6I1R2pITk';
    const supabase = supabase.createClient(supabaseUrl, supabaseKey);

    // Maak een realtime channel aan voor de heartbeat table
    const channel = supabase.channel('heartbeat');

    // Luister naar inserts op de heartbeat table
    channel
      .on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'heartbeat' }, payload => {
        console.log('Heartbeat ontvangen:', payload);
      })
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Realtime heartbeat channel actief!');
        }
      });

    // Heartbeat: elke 30 seconden een dummy row toevoegen aan de heartbeat table
    setInterval(async () => {
      try {
        const { error } = await supabase.from('heartbeat').insert({ timestamp: new Date().toISOString() });
        if (error) {
          console.error('Heartbeat insert error:', error.message);
        } else {
          console.log('Heartbeat verzonden');
        }
      } catch (e) {
        console.error('Heartbeat exception:', e);
      }
    }, 30000);
    // === EINDE SUPABASE REALTIME HEARTBEAT ===
    </script>
</head>
<body class="dark-theme">
    <!-- Header with Navigation -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-gray-900/95 backdrop-blur-sm border-b border-gray-800">
        <div class="container mx-auto px-4">
            <nav class="flex items-center justify-between py-4">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="index.html" class="text-2xl font-bold text-white">
                        <span class="gradient-text">Wellora</span>
                    </a>
                </div>

                <!-- Navigation Links -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.html" class="text-gray-300 hover:text-white transition-colors" data-i18n="nav_features">Features</a>
                    <a href="index.html#how-it-works" class="text-gray-300 hover:text-white transition-colors" data-i18n="nav_how_it_works">How It Works</a>
                    <a href="index.html#benefits" class="text-gray-300 hover:text-white transition-colors" data-i18n="nav_benefits">Benefits</a>
                    <a href="index.html#contact" class="text-gray-300 hover:text-white transition-colors" data-i18n="nav_contact">Contact</a>
                </div>

                <!-- Right side buttons and language switcher -->
                <div class="flex items-center space-x-4">
                    <!-- Language switcher -->
                    <div class="lang-switcher flex items-center space-x-1 bg-gray-800/50 rounded-lg p-1">
                        <div class="lang-button px-3 py-1 rounded-md text-sm font-medium cursor-pointer transition-all hover:bg-gray-700" data-lang="en" onclick="changeLanguage('en')">EN</div>
                        <div class="lang-button px-3 py-1 rounded-md text-sm font-medium cursor-pointer transition-all hover:bg-gray-700" data-lang="nl" onclick="changeLanguage('nl')">NL</div>
                    </div>

                    <!-- Action buttons -->
                    <button onclick="openModal('loginModal')" class="px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 transition-all text-sm" data-i18n="nav_login">Login</button>
                    <button onclick="openModal('demoModal')" class="px-4 py-2 rounded-full gradient-bg text-white font-medium hover:shadow-lg transition-all text-sm" data-i18n="nav_request_demo">Request Demo</button>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button class="hamburger text-white text-2xl">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </nav>
        </div>
    </header>

    <!-- Animation and style for recording modal -->
    <style>
    @keyframes moveDataEffect {
        0% { transform: translateX(100%); }
        100% { transform: translateX(-100%); }
    }
    
    @keyframes patientWaveAnimation {
        0%, 100% { height: var(--height); }
        50% { height: calc(var(--height) * 0.3); }
    }
    
    @keyframes therapistWaveAnimation {
        0%, 100% { height: var(--height); }
        50% { height: calc(var(--height) * 0.3); }
    }
    
    @keyframes audioWave {
        0% { transform: scaleY(0.3); }
        50% { transform: scaleY(1); }
        100% { transform: scaleY(0.3); }
    }
    
    @keyframes filterSweep {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }
    
    @keyframes noisePulse {
        0% { opacity: 0.8; }
        50% { opacity: 0.2; }
        100% { opacity: 0.8; }
    }
    
    @keyframes frequencyBars {
        0% { height: var(--bar-height, 20%); }
        50% { height: var(--max-height, 100%); }
        100% { height: var(--bar-height, 20%); }
    }
    
    /* Phone QR scanning animation */
    @keyframes phoneFloat {
        0%, 100% { transform: translateY(0) rotate(-5deg); }
        50% { transform: translateY(-10px) rotate(-5deg); }
    }
    
    @keyframes qrPulse {
        0%, 100% { transform: scale(1); opacity: 0.8; }
        50% { transform: scale(1.05); opacity: 1; }
    }
    
    @keyframes scanBeam {
        0% { height: 0; top: 25%; opacity: 0.8; }
        40% { height: 50%; top: 25%; opacity: 1; }
        60% { height: 50%; top: 25%; opacity: 1; }
        100% { height: 0; top: 75%; opacity: 0.8; }
    }
    
    @keyframes recPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.1); }
    }
    
    @keyframes fadeInUp {
        0% { transform: translateY(20px); opacity: 0; }
        100% { transform: translateY(0); opacity: 1; }
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }
    
    /* High-Fidelity Audio card animation */
    .high-fidelity-waves {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 0;
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        padding: 0 10%;
        transition: height 0.5s ease;
        overflow: hidden;
        z-index: 0;
    }
    
    .feature-card:hover .high-fidelity-waves {
        height: 70%;
    }
    
    .audio-bar {
        width: 4px;
        background: linear-gradient(to top, #3b82f6, #60a5fa);
        border-radius: 2px;
        transform-origin: bottom;
        opacity: 0;
        transition: opacity 0.5s ease;
    }
    
    .feature-card:hover .audio-bar {
        opacity: 0.7;
        animation: audioWave var(--duration, 1s) infinite;
    }
    
    /* Intelligent Filtering card animation */
    .filter-sweep {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.15), transparent);
        opacity: 0;
        z-index: 0;
    }
    
    .feature-card:hover .filter-sweep {
        opacity: 1;
        animation: filterSweep 2s infinite;
    }
    
    .noise-particles {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 0;
    }
    
    .noise-particle {
        position: absolute;
        width: 6px;
        height: 6px;
        background-color: rgba(59, 130, 246, 0.4);
        border-radius: 50%;
        opacity: 0;
        transition: opacity 0.5s ease;
    }
    
    .feature-card:hover .noise-particle {
        opacity: 0.8;
        animation: noisePulse 2s infinite;
    }
    
    /* Noise Cancellation card animation */
    .cancellation-field {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0);
        width: 60%;
        height: 60%;
        border-radius: 50%;
        border: 2px solid rgba(59, 130, 246, 0.3);
        opacity: 0;
        transition: all 0.5s ease;
        z-index: 0;
    }
    
    .feature-card:hover .cancellation-field {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
        animation: pulse 2s infinite;
    }
    
    .neural-node {
        position: absolute;
        width: 6px;
        height: 6px;
        background-color: rgba(59, 130, 246, 0.6);
        border-radius: 50%;
        animation: pulse 3s infinite;
    }
    
    .neural-connection {
        position: absolute;
        height: 1px;
        background: linear-gradient(to right, rgba(59, 130, 246, 0.4), rgba(16, 185, 129, 0.4));
        animation: pulse 4s infinite;
        transform-origin: left;
    }
    </style>
    
    <!-- Script for theme toggle functionality -->
    <script>
        // Define modal functions globally
        function openModal(modalId) {
            console.log('openModal called with modalId:', modalId);
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.classList.remove('hidden');
                setTimeout(() => {
                    const content = modal.querySelector('.modal-content');
                    if (content) {
                        content.classList.add('scale-100', 'opacity-100');
                        content.classList.remove('scale-95', 'opacity-0');
                    }

                    // Start animations for AI recording demo modal
                    if (modalId === 'aiRecordingDemoModal') {
                        startRecordingDemoAnimations();
                    }
                }, 10);
            } else {
                console.error('Modal not found with ID:', modalId);
            }
        }
        
        // Define the openRecordingModal function to show the modal on the current page
        function openRecordingModal() {
            console.log('Opening recording modal in the current page');
            
            // Check if recordingModal already exists
            let recordingModal = document.getElementById('recordingModal');
            
            // If not, create it
            if (!recordingModal) {
                // Create the modal HTML structure as in index.html
                recordingModal = document.createElement('div');
                recordingModal.id = 'recordingModal';
                recordingModal.className = 'fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 hidden';
                
                // Add the modal content
                recordingModal.innerHTML = `
                    <div class="bg-white dark:bg-gray-900 backdrop-blur-md rounded-2xl max-w-[95vw] w-full max-h-[95vh] transform transition-all scale-95 opacity-0 modal-content shadow-2xl border border-blue-100 dark:border-gray-700 overflow-hidden">
                        <!-- Header -->
                        <div class="flex justify-between items-center p-6 pb-4 border-b border-blue-100 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-gray-800 dark:to-gray-800">
                            <div class="flex items-center">
                                <div class="mr-4 p-3 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center relative overflow-hidden shadow-lg">
                                    <div class="absolute inset-0 bg-white/20 animate-pulse-slow rounded-xl"></div>
                                    <i class="fas fa-brain text-white text-2xl relative z-10"></i>
                                </div>
                                <div>
                                    <h2 class="text-2xl font-bold text-gray-800 dark:text-white">
                                        <span class="gradient-text" data-i18n="aiConversationAnalysis">AI Gesprekanalyse</span>
                                    </h2>
                                    <p class="text-gray-600 dark:text-gray-300 text-sm mt-1" data-i18n="aiAnalysisSubtitle">Geavanceerde AI-analyse voor patiëntgesprekken</p>
                                </div>
                            </div>
                            <button onclick="closeRecordingModal()" class="text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors bg-white/80 dark:bg-gray-800/80 rounded-full w-10 h-10 flex items-center justify-center shadow-md hover:shadow-lg">
                                <i class="fas fa-times text-lg"></i>
                            </button>
                        </div>

                        <!-- Main Content Area -->
                        <div class="flex h-[calc(95vh-120px)]">
                            <!-- Left Panel - Audio Upload & Transcription -->
                            <div class="w-2/3 p-6 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
                                <!-- Audio Upload Section -->
                                <div class="mb-6">
                                    <div class="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 border border-blue-100 dark:border-gray-600">
                                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                                            <i class="fas fa-upload text-blue-500 mr-2"></i>
                                            <span data-i18n="uploadAudio">Audio Bestand Uploaden</span>
                                        </h3>

                                        <!-- File Upload Area -->
                                        <div id="audioUploadArea" class="border-2 border-dashed border-blue-300 dark:border-gray-500 rounded-lg p-8 text-center bg-white/50 dark:bg-gray-800/50 hover:bg-white/80 dark:hover:bg-gray-700/80 transition-all cursor-pointer">
                                            <div class="upload-content">
                                                <i class="fas fa-cloud-upload-alt text-4xl text-blue-400 mb-4"></i>
                                                <p class="text-gray-700 dark:text-gray-300 font-medium mb-2" data-i18n="dragDropAudio">Sleep uw MP3 bestand hier of klik om te uploaden</p>
                                                <p class="text-sm text-gray-500 dark:text-gray-400" data-i18n="supportedFormats">Ondersteunde formaten: MP3, WAV, M4A (max 100MB)</p>
                                                <input type="file" id="audioFileInput" accept=".mp3,.wav,.m4a" class="hidden">
                                            </div>
                                        </div>

                                        <!-- Real Transcription Info -->
                                        <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
                                            <div class="flex items-start">
                                                <i class="fas fa-robot text-blue-500 mr-3 mt-1"></i>
                                                <div class="text-sm text-blue-800 dark:text-blue-200">
                                                    <p class="font-medium mb-2">🚀 OpenAI Whisper Transcriptie</p>
                                                    <ul class="space-y-1 text-xs">
                                                        <li>• Upload uw eigen MP3 bestand (max 25MB)</li>
                                                        <li>• Gebruikt OpenAI Whisper voor professionele transcriptie</li>
                                                        <li>• Superieure nauwkeurigheid voor Nederlandse spraak</li>
                                                        <li>• Automatische detectie van medische terminologie</li>
                                                        <li>• Intelligente speaker herkenning</li>
                                                        <li>• Timestamps voor elke zin</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Audio Player (Hidden initially) -->
                                        <div id="audioPlayerSection" class="mt-4 hidden">
                                            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm border border-gray-200 dark:border-gray-600">
                                                <div class="flex items-center justify-between mb-3">
                                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300" data-i18n="audioFile">Audio Bestand:</span>
                                                    <span id="audioFileName" class="text-sm text-gray-600 dark:text-gray-400"></span>
                                                </div>
                                                <audio id="audioPlayer" controls class="w-full mb-3">
                                                    <source id="audioSource" src="" type="audio/mpeg">
                                                    Uw browser ondersteunt geen audio element.
                                                </audio>
                                                <div class="flex justify-between items-center">
                                                    <button id="startAnalysisBtn" class="px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-lg hover:from-blue-600 hover:to-cyan-600 transition-all flex items-center">
                                                        <i class="fas fa-play mr-2"></i>
                                                        <span data-i18n="startAnalysis">Start Analyse</span>
                                                    </button>
                                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                                        <span data-i18n="duration">Duur:</span> <span id="audioDuration">--:--</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Transcription Section -->
                                <div id="transcriptionSection" class="hidden">
                                    <div class="bg-gray-50 dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-600">
                                        <div class="flex justify-between items-center mb-4">
                                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                                                <i class="fas fa-comments text-green-500 mr-2"></i>
                                                <span data-i18n="conversationTranscript">Gesprek Transcriptie</span>
                                            </h3>
                                            <div class="flex items-center space-x-2">
                                                <div id="analysisStatus" class="flex items-center text-sm">
                                                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"></div>
                                                    <span class="text-green-600" data-i18n="analyzing">Analyseren...</span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Chat-like Transcription Display -->
                                        <div id="transcriptionChat" class="space-y-4 h-80 overflow-y-auto bg-white dark:bg-gray-900 rounded-lg p-4 border border-gray-200 dark:border-gray-600 scroll-smooth">
                                            <!-- Transcription messages will be added here dynamically -->
                                            <div class="text-center text-gray-500 dark:text-gray-400 text-sm py-8">
                                                <i class="fas fa-comments text-2xl mb-2"></i>
                                                <p>Transcriptie verschijnt hier tijdens de analyse...</p>
                                            </div>
                                        </div>

                                        <!-- Progress Bar -->
                                        <div class="mt-4">
                                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                                <span data-i18n="progress">Voortgang</span>
                                                <span id="progressPercentage">0%</span>
                                            </div>
                                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                                <div id="progressBar" class="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Panel - AI Insights -->
                            <div class="w-1/3 p-6 bg-gray-50 dark:bg-gray-800 overflow-y-auto">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                                    <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                                    <span data-i18n="aiInsights">AI Inzichten</span>
                                </h3>

                                <!-- Insights Cards Container -->
                                <div id="insightsContainer" class="space-y-4">
                                    <!-- Initial state -->
                                    <div id="noInsightsMessage" class="text-center py-8">
                                        <i class="fas fa-brain text-4xl text-gray-300 mb-3"></i>
                                        <p class="text-gray-500 dark:text-gray-400" data-i18n="uploadToAnalyze">Upload een audio bestand om AI-analyse te starten</p>
                                    </div>
                                </div>

                                <!-- Summary Button (Hidden initially) -->
                                <div id="summarySection" class="mt-6 hidden">
                                    <button id="generateSummaryBtn" class="w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all flex items-center justify-center">
                                        <i class="fas fa-file-medical-alt mr-2"></i>
                                        <span data-i18n="generateSummary">Genereer Samenvatting & Behandelplan</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Footer -->
                        <div class="p-6 pt-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 flex justify-between items-center">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <i class="fas fa-shield-alt mr-1"></i>
                                <span data-i18n="hipaaCompliant">HIPAA-conform & Veilig</span>
                            </div>
                            <div class="flex space-x-3">
                                <button onclick="closeRecordingModal()" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-700 text-white rounded-lg transition-all">
                                    <span data-i18n="close">Sluiten</span>
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                
                // Add the modal to the body
                document.body.appendChild(recordingModal);

                // Initialize the new AI conversation analysis functionality
                initializeAIConversationAnalysis();
            }

            // Show the modal
            recordingModal.classList.remove('hidden');
            setTimeout(() => {
                const modalContent = recordingModal.querySelector('.modal-content');
                if (modalContent) {
                    modalContent.classList.remove('scale-95', 'opacity-0');
                    modalContent.classList.add('scale-100', 'opacity-100');
                }
            }, 10);
        }

        // Initialize AI Conversation Analysis functionality
        function initializeAIConversationAnalysis() {
            // File upload functionality
            const uploadArea = document.getElementById('audioUploadArea');
            const fileInput = document.getElementById('audioFileInput');
            const audioPlayer = document.getElementById('audioPlayer');
            const audioSource = document.getElementById('audioSource');
            const audioPlayerSection = document.getElementById('audioPlayerSection');
            const audioFileName = document.getElementById('audioFileName');
            const audioDuration = document.getElementById('audioDuration');
            const startAnalysisBtn = document.getElementById('startAnalysisBtn');

            // Drag and drop functionality
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('border-blue-500', 'bg-blue-50');
            });
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('border-blue-500', 'bg-blue-50');
            });
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('border-blue-500', 'bg-blue-50');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload(files[0]);
                }
            });

            // File input change
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFileUpload(e.target.files[0]);
                }
            });

            // Start analysis button
            startAnalysisBtn.addEventListener('click', () => {
                startAIAnalysis();
            });
        }

        // Handle file upload
        function handleFileUpload(file) {
            // Validate file type
            const allowedTypes = ['audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/m4a'];
            if (!allowedTypes.includes(file.type)) {
                alert('Alleen MP3, WAV en M4A bestanden zijn toegestaan.');
                return;
            }

            // Validate file size (25MB max for Whisper)
            if (file.size > 25 * 1024 * 1024) {
                alert('Bestand is te groot. Maximum grootte is 25MB voor OpenAI Whisper.');
                return;
            }

            // Store file globally for later use
            window.currentAudioFile = file;
            console.log('Stored audio file globally:', file.name, file.size);

            // Create object URL for the file
            const fileURL = URL.createObjectURL(file);

            // Update UI
            document.getElementById('audioFileName').textContent = file.name;
            document.getElementById('audioSource').src = fileURL;
            document.getElementById('audioPlayer').load();
            document.getElementById('audioPlayerSection').classList.remove('hidden');

            // Update upload area
            const uploadContent = document.querySelector('.upload-content');
            uploadContent.innerHTML = `
                <i class="fas fa-check-circle text-4xl text-green-500 mb-4"></i>
                <p class="text-gray-700 font-medium mb-2">Bestand succesvol geüpload</p>
                <p class="text-sm text-gray-500">${file.name} (${formatFileSize(file.size)})</p>
            `;

            // Get audio duration when metadata loads
            const audioPlayer = document.getElementById('audioPlayer');
            audioPlayer.addEventListener('loadedmetadata', () => {
                const duration = formatTime(audioPlayer.duration);
                document.getElementById('audioDuration').textContent = duration;
            });
        }

        // Start AI Analysis - ONLY use real Whisper transcription
        function startAIAnalysis() {
            // Show transcription section
            document.getElementById('transcriptionSection').classList.remove('hidden');

            // Hide no insights message
            document.getElementById('noInsightsMessage').classList.add('hidden');

            // Clear any existing content
            const transcriptionChat = document.getElementById('transcriptionChat');
            transcriptionChat.innerHTML = `
                <div class="text-center text-gray-500 dark:text-gray-400 text-sm py-8">
                    <i class="fas fa-robot text-2xl mb-2 animate-pulse"></i>
                    <p>OpenAI Whisper transcriptie wordt gestart...</p>
                </div>
            `;

            // ONLY start real Whisper transcription - NO MOCK DATA
            startRealTranscription();
        }

        // Simulate AI Analysis Process
        function simulateAIAnalysis() {
            const transcriptionChat = document.getElementById('transcriptionChat');
            const progressBar = document.getElementById('progressBar');
            const progressPercentage = document.getElementById('progressPercentage');
            const insightsContainer = document.getElementById('insightsContainer');

            // Clear previous content
            transcriptionChat.innerHTML = '';

            // Simulate conversation transcription with chat bubbles
            const conversation = [
                { speaker: 'patient', time: '00:15', text: 'Ik neem Coveram voor mijn bloeddruk, maar de laatste tijd voel ik me duizelig.' },
                { speaker: 'patient', time: '00:28', text: 'Het is alsof de kamer begint te draaien en ik ben bang dat ik ga vallen.' },
                { speaker: 'therapist', time: '00:45', text: 'Ik begrijp je bezorgdheid over de duizeligheid. Hoe vaak ervaar je deze duizelige momenten?' },
                { speaker: 'patient', time: '01:02', text: 'Vooral \'s ochtends als ik opstaan. Die ademhalingstechniek die je vorige keer voorstelde helpt wel een beetje.' },
                { speaker: 'therapist', time: '01:20', text: 'Dat is goed om te horen. Heb je ook andere bijwerkingen opgemerkt sinds je Coveram gebruikt?' },
                { speaker: 'patient', time: '01:35', text: 'Soms voel ik me ook angstig, vooral als de duizeligheid optreedt. Ik maak me zorgen over mijn gezondheid.' }
            ];

            let currentIndex = 0;
            const totalMessages = conversation.length;

            // Add messages progressively
            const addMessage = () => {
                if (currentIndex < conversation.length) {
                    const message = conversation[currentIndex];
                    addChatMessage(message.speaker, message.text, message.time);

                    // Update progress
                    const progress = ((currentIndex + 1) / totalMessages) * 100;
                    progressBar.style.width = progress + '%';
                    progressPercentage.textContent = Math.round(progress) + '%';

                    // Add insights based on conversation content
                    if (currentIndex === 1) {
                        addInsightCard('dizziness', 'Duizeligheid', 'Patiënt meldt duizeligheidsklachten, mogelijk gerelateerd aan medicatie', 'warning');
                    }
                    if (currentIndex === 3) {
                        addInsightCard('medication', 'Medicatie: Coveram', 'Bloeddrukverlagende medicatie - mogelijke bijwerking duizeligheid', 'info');
                    }
                    if (currentIndex === 5) {
                        addInsightCard('anxiety', 'Angst & Bezorgdheid', 'Secundaire angst door fysieke symptomen - behandeling nodig', 'danger');
                    }

                    currentIndex++;
                    setTimeout(addMessage, 2000); // Add next message after 2 seconds
                } else {
                    // Analysis complete
                    document.getElementById('analysisStatus').innerHTML = `
                        <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        <span class="text-green-600">Analyse voltooid</span>
                    `;

                    // Show summary section
                    document.getElementById('summarySection').classList.remove('hidden');

                    // Add final insights
                    setTimeout(() => {
                        addInsightCard('breathing', 'Ademhalingstechnieken', 'Positieve respons op eerder geleerde technieken', 'success');
                        addInsightCard('sleep', 'Slaapkwaliteit', 'Mogelijk beïnvloed door medicatie en angst', 'warning');
                    }, 1000);
                }
            };

            // Start adding messages
            setTimeout(addMessage, 1000);
        }

        // Add chat message to transcription
        function addChatMessage(speaker, text, time) {
            const transcriptionChat = document.getElementById('transcriptionChat');
            const isPatient = speaker === 'patient';

            const messageDiv = document.createElement('div');
            messageDiv.className = `flex ${isPatient ? 'justify-start' : 'justify-end'} mb-4`;

            messageDiv.innerHTML = `
                <div class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${isPatient ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-900 dark:text-blue-200' : 'bg-green-100 dark:bg-green-900/30 text-green-900 dark:text-green-200'}">
                    <div class="flex items-center mb-1">
                        <i class="fas ${isPatient ? 'fa-user' : 'fa-user-md'} text-xs mr-2"></i>
                        <span class="text-xs font-medium">${isPatient ? 'Patiënt' : 'Therapeut'}</span>
                        <span class="text-xs text-gray-500 dark:text-gray-400 ml-auto">${time}</span>
                    </div>
                    <p class="text-sm">${text}</p>
                </div>
            `;

            transcriptionChat.appendChild(messageDiv);
            transcriptionChat.scrollTop = transcriptionChat.scrollHeight;
        }

        // Add insight card to the right panel
        function addInsightCard(type, title, description, severity) {
            const insightsContainer = document.getElementById('insightsContainer');

            const colors = {
                'info': 'border-blue-200 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200',
                'warning': 'border-yellow-200 dark:border-yellow-700 bg-yellow-50 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200',
                'danger': 'border-red-200 dark:border-red-700 bg-red-50 dark:bg-red-900/30 text-red-800 dark:text-red-200',
                'success': 'border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/30 text-green-800 dark:text-green-200'
            };

            const icons = {
                'dizziness': 'fa-dizzy',
                'medication': 'fa-pills',
                'anxiety': 'fa-heart',
                'breathing': 'fa-lungs',
                'sleep': 'fa-bed'
            };

            const cardDiv = document.createElement('div');
            cardDiv.className = `border rounded-lg p-4 ${colors[severity]} animate-fade-in`;

            cardDiv.innerHTML = `
                <div class="flex items-start justify-between">
                    <div class="flex items-start">
                        <i class="fas ${icons[type]} text-lg mr-3 mt-1"></i>
                        <div>
                            <h4 class="font-semibold text-sm mb-1">${title}</h4>
                            <p class="text-xs">${description}</p>
                            ${type === 'medication' ? `
                                <button onclick="showMedicationInfo('${title}')" class="mt-2 text-xs bg-white/50 dark:bg-gray-700/50 hover:bg-white/80 dark:hover:bg-gray-600/80 px-2 py-1 rounded border border-gray-300 dark:border-gray-600 transition-all">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Bijsluiter
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;

            insightsContainer.appendChild(cardDiv);
        }

        // Show medication information with real package insert
        async function showMedicationInfo(medicationName) {
            // Create loading modal first
            const loadingModal = createLoadingModal('Bijsluiter ophalen...');
            document.body.appendChild(loadingModal);

            try {
                // Get real package insert information
                const packageInsert = await fetchPackageInsert(medicationName);

                // Remove loading modal
                document.body.removeChild(loadingModal);

                // Show package insert modal
                showPackageInsertModal(medicationName, packageInsert);

            } catch (error) {
                console.error('Error fetching package insert:', error);
                document.body.removeChild(loadingModal);

                // Fallback to basic information
                showBasicMedicationInfo(medicationName);
            }
        }

        // Create loading modal
        function createLoadingModal(message) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-[70]';
            modal.innerHTML = `
                <div class="bg-white dark:bg-gray-900 rounded-2xl p-8 max-w-md w-full m-4 text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                    <p class="text-gray-700 dark:text-gray-300">${message}</p>
                </div>
            `;
            return modal;
        }

        // Fetch real package insert from online sources
        async function fetchPackageInsert(medicationName) {
            // Clean medication name for search
            const cleanName = medicationName.replace(/[^a-zA-Z0-9\s]/g, '').trim();

            try {
                // Try multiple sources for package insert information
                const sources = [
                    `https://www.farmacotherapeutischkompas.nl/bladeren/preparaatteksten/${cleanName.toLowerCase()}`,
                    `https://www.geneesmiddeleninformatiebank.nl/ords/f?p=111:3:::::P3_RVG1:${cleanName}`,
                    `https://www.cbg-meb.nl/humans/geneesmiddeleninformatie/geregistreerde-geneesmiddelen/zoeken`
                ];

                // For demo purposes, we'll simulate fetching real data
                // In production, you would use a proper API or web scraping service
                const simulatedData = await simulatePackageInsertFetch(cleanName);

                return simulatedData;

            } catch (error) {
                throw new Error('Could not fetch package insert information');
            }
        }

        // Simulate fetching package insert data (replace with real API in production)
        async function simulatePackageInsertFetch(medicationName) {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 2000));

            // Return realistic package insert data based on medication name
            const medicationData = {
                'coveram': {
                    name: 'Coveram',
                    activeIngredients: 'Perindopril arginine + Amlodipine',
                    indication: 'Behandeling van hypertensie en/of stabiele coronaire hartziekte',
                    sideEffects: [
                        { name: 'Duizeligheid', frequency: 'Vaak (1-10%)', severity: 'Mild tot matig' },
                        { name: 'Hoofdpijn', frequency: 'Vaak (1-10%)', severity: 'Mild' },
                        { name: 'Vermoeidheid', frequency: 'Soms (0.1-1%)', severity: 'Mild' },
                        { name: 'Hoest', frequency: 'Vaak (1-10%)', severity: 'Mild' },
                        { name: 'Oedeem', frequency: 'Vaak (1-10%)', severity: 'Mild tot matig' },
                        { name: 'Misselijkheid', frequency: 'Soms (0.1-1%)', severity: 'Mild' },
                        { name: 'Buikpijn', frequency: 'Soms (0.1-1%)', severity: 'Mild' },
                        { name: 'Hartkloppingen', frequency: 'Soms (0.1-1%)', severity: 'Mild tot matig' }
                    ],
                    warnings: [
                        'Kan duizeligheid veroorzaken, vooral bij het opstaan',
                        'Voorzichtigheid bij autorijden',
                        'Regelmatige bloeddrukcontrole noodzakelijk'
                    ],
                    dosage: '1 tablet per dag, bij voorkeur \'s ochtends',
                    contraindications: 'Overgevoeligheid voor ACE-remmers of calciumantagonisten'
                },
                'default': {
                    name: medicationName,
                    activeIngredients: 'Informatie niet beschikbaar',
                    indication: 'Raadpleeg uw arts of apotheker',
                    sideEffects: [
                        { name: 'Duizeligheid', frequency: 'Onbekend', severity: 'Variabel' },
                        { name: 'Hoofdpijn', frequency: 'Onbekend', severity: 'Variabel' },
                        { name: 'Misselijkheid', frequency: 'Onbekend', severity: 'Variabel' }
                    ],
                    warnings: ['Raadpleeg altijd uw arts bij bijwerkingen'],
                    dosage: 'Volgens voorschrift arts',
                    contraindications: 'Zie bijsluiter'
                }
            };

            const key = medicationName.toLowerCase().includes('coveram') ? 'coveram' : 'default';
            return medicationData[key];
        }

        // Show package insert modal with highlighting
        function showPackageInsertModal(medicationName, packageInsert) {
            // Get patient symptoms from transcription for highlighting
            const patientSymptoms = extractPatientSymptoms();

            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-[70]';
            modal.innerHTML = `
                <div class="bg-white dark:bg-gray-900 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden m-4">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex justify-between items-center">
                            <div class="flex items-center">
                                <i class="fas fa-pills text-blue-500 mr-3 text-2xl"></i>
                                <div>
                                    <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Bijsluiter: ${packageInsert.name}</h2>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">${packageInsert.activeIngredients}</p>
                                </div>
                            </div>
                            <button onclick="this.closest('.fixed').remove()" class="text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                    </div>

                    <div class="overflow-y-auto max-h-[calc(90vh-120px)]">
                        <div class="p-6 space-y-6">
                            <!-- Indication -->
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2 flex items-center">
                                    <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                    Indicatie
                                </h3>
                                <p class="text-gray-700 dark:text-gray-300">${packageInsert.indication}</p>
                            </div>

                            <!-- Dosage -->
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2 flex items-center">
                                    <i class="fas fa-prescription-bottle text-green-500 mr-2"></i>
                                    Dosering
                                </h3>
                                <p class="text-gray-700 dark:text-gray-300">${packageInsert.dosage}</p>
                            </div>

                            <!-- Side Effects (with highlighting) -->
                            <div id="sideEffectsSection">
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2 flex items-center">
                                    <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                                    Bijwerkingen
                                    ${patientSymptoms.length > 0 ? '<span class="ml-2 text-xs bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 px-2 py-1 rounded">Patiënt symptomen gedetecteerd</span>' : ''}
                                </h3>
                                <div class="space-y-2">
                                    ${packageInsert.sideEffects.map(effect => {
                                        const isPatientSymptom = patientSymptoms.some(symptom =>
                                            effect.name.toLowerCase().includes(symptom.toLowerCase()) ||
                                            symptom.toLowerCase().includes(effect.name.toLowerCase())
                                        );

                                        return `
                                            <div class="p-3 rounded-lg border ${isPatientSymptom ?
                                                'border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/20' :
                                                'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800'
                                            } ${isPatientSymptom ? 'animate-pulse-slow' : ''}">
                                                <div class="flex justify-between items-start">
                                                    <div class="flex-1">
                                                        <span class="font-medium ${isPatientSymptom ? 'text-red-800 dark:text-red-200' : 'text-gray-800 dark:text-gray-200'}">
                                                            ${effect.name}
                                                            ${isPatientSymptom ? '<i class="fas fa-exclamation-circle text-red-500 ml-1"></i>' : ''}
                                                        </span>
                                                        <div class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                                            <span class="mr-4">Frequentie: ${effect.frequency}</span>
                                                            <span>Ernst: ${effect.severity}</span>
                                                        </div>
                                                        ${isPatientSymptom ?
                                                            '<div class="text-xs text-red-600 dark:text-red-400 mt-1 font-medium">⚠️ Komt overeen met patiënt klachten</div>' :
                                                            ''
                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        `;
                                    }).join('')}
                                </div>
                            </div>

                            <!-- Warnings -->
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2 flex items-center">
                                    <i class="fas fa-shield-alt text-orange-500 mr-2"></i>
                                    Waarschuwingen
                                </h3>
                                <ul class="space-y-1">
                                    ${packageInsert.warnings.map(warning => `
                                        <li class="text-gray-700 dark:text-gray-300 flex items-start">
                                            <i class="fas fa-chevron-right text-orange-500 mr-2 mt-1 text-xs"></i>
                                            ${warning}
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>

                            <!-- Contraindications -->
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2 flex items-center">
                                    <i class="fas fa-ban text-red-500 mr-2"></i>
                                    Contra-indicaties
                                </h3>
                                <p class="text-gray-700 dark:text-gray-300">${packageInsert.contraindications}</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6 pt-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                        <div class="flex justify-between items-center">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <i class="fas fa-info-circle mr-1"></i>
                                Raadpleeg altijd uw arts bij vragen over medicatie
                            </div>
                            <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-700 text-white rounded-lg transition-all">
                                Sluiten
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Auto-scroll to side effects section if patient symptoms detected
            if (patientSymptoms.length > 0) {
                setTimeout(() => {
                    const sideEffectsSection = document.getElementById('sideEffectsSection');
                    if (sideEffectsSection) {
                        sideEffectsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                }, 500);
            }
        }

        // Extract patient symptoms from transcription data
        function extractPatientSymptoms() {
            const symptoms = [];
            const symptomKeywords = [
                'duizelig', 'duizeligheid', 'draaierig', 'dizzy',
                'hoofdpijn', 'hoofdpijn', 'headache',
                'misselijk', 'misselijkheid', 'nausea',
                'moe', 'vermoeid', 'vermoeidheid', 'tired', 'fatigue',
                'hoest', 'hoesten', 'cough',
                'zwelling', 'oedeem', 'swelling',
                'buikpijn', 'maagpijn', 'stomach pain',
                'hartkloppingen', 'palpitations',
                'angstig', 'angst', 'anxiety',
                'slapeloosheid', 'slecht slapen', 'insomnia'
            ];

            // Check transcription data for symptoms
            if (transcriptionData && transcriptionData.segments) {
                transcriptionData.segments.forEach(segment => {
                    if (segment.speaker === 'patient') {
                        const lowerText = segment.text.toLowerCase();
                        symptomKeywords.forEach(keyword => {
                            if (lowerText.includes(keyword) && !symptoms.includes(keyword)) {
                                symptoms.push(keyword);
                            }
                        });
                    }
                });
            }

            return symptoms;
        }

        // Fallback basic medication info
        function showBasicMedicationInfo(medicationName) {
            alert(`Bijsluiter informatie voor ${medicationName}:\n\nKon geen gedetailleerde informatie ophalen.\nRaadpleeg uw arts of apotheker voor meer informatie.\n\nMogelijke bijwerkingen kunnen zijn:\n- Duizeligheid\n- Hoofdpijn\n- Vermoeidheid\n\nNeem contact op met uw arts als bijwerkingen aanhouden.`);
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Format time
        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        // Generate summary and treatment plan
        function generateSummary() {
            // Create summary modal
            const summaryModal = document.createElement('div');
            summaryModal.className = 'fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-[60]';
            summaryModal.innerHTML = `
                <div class="bg-white dark:bg-gray-900 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto m-4">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex justify-between items-center">
                            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Samenvatting & Behandelplan</h2>
                            <button onclick="this.closest('.fixed').remove()" class="text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Summary -->
                            <div>
                                <h3 class="text-lg font-semibold mb-4 text-blue-600 dark:text-blue-400">Gesprek Samenvatting</h3>
                                <div class="space-y-3 text-sm text-gray-700 dark:text-gray-300">
                                    <p><strong>Transcriptie:</strong> ${generateRealSummary()}</p>
                                    <p><strong>Gedetecteerde Inzichten:</strong> ${transcriptionData.insights.map(i => i.title).join(', ') || 'Geen specifieke medische onderwerpen gedetecteerd'}</p>
                                    <p><strong>Spraaksegmenten:</strong> ${transcriptionData.segments.length} segmenten (${transcriptionData.segments.filter(s => s.speaker === 'patient').length} patiënt, ${transcriptionData.segments.filter(s => s.speaker === 'therapist').length} therapeut)</p>
                                    <p><strong>Totaal Woorden:</strong> ${transcriptionData.fullText.split(' ').filter(w => w.length > 0).length} woorden getranscribeerd</p>
                                </div>
                            </div>

                            <!-- Treatment Plan -->
                            <div>
                                <h3 class="text-lg font-semibold mb-4 text-green-600 dark:text-green-400">Behandelplan</h3>
                                <div class="space-y-3 text-sm text-gray-700 dark:text-gray-300">
                                    ${generateRealTreatmentPlan().map(plan => `
                                        <div class="flex items-start">
                                            <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                                            <span>${plan}</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 flex justify-between">
                            <button onclick="sendToN8N()" class="px-4 py-2 bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white rounded-lg transition-all">
                                <i class="fas fa-cloud-upload-alt mr-2"></i>
                                Verstuur naar N8N
                            </button>
                            <button onclick="exportToPDF()" class="px-4 py-2 bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 text-white rounded-lg transition-all">
                                <i class="fas fa-file-pdf mr-2"></i>
                                Exporteer PDF
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(summaryModal);
        }

        // Send data to N8N webhook
        async function sendToN8N() {
            const webhookUrl = 'https://wellora.app.n8n.cloud/webhook-test/webhook';

            // Get the uploaded audio file
            const audioFileInput = document.getElementById('audioFileInput');
            let file = audioFileInput?.files[0] || window.currentAudioFile;

            if (!file) {
                alert('Geen audio bestand gevonden om te versturen.');
                return;
            }

            // Create FormData to send the file and real transcription data
            const formData = new FormData();
            formData.append('audio', file);
            formData.append('analysis', JSON.stringify({
                timestamp: new Date().toISOString(),
                transcription: {
                    fullText: transcriptionData.fullText,
                    segments: transcriptionData.segments,
                    totalSegments: transcriptionData.segments.length,
                    totalWords: transcriptionData.fullText.split(' ').length
                },
                insights: transcriptionData.insights.map(insight => ({
                    type: insight.type,
                    title: insight.title,
                    speaker: insight.speaker,
                    timestamp: insight.timestamp
                })),
                summary: generateRealSummary(),
                treatment_plan: generateRealTreatmentPlan(),
                metadata: {
                    audioFileName: file.name,
                    audioSize: file.size,
                    audioDuration: document.getElementById('audioPlayer')?.duration || 0,
                    transcriptionMethod: 'Web Speech API',
                    language: 'nl-NL'
                }
            }));

            try {
                const response = await fetch(webhookUrl, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    alert('Data succesvol verzonden naar N8N!');
                } else {
                    alert('Fout bij verzenden naar N8N. Probeer opnieuw.');
                }
            } catch (error) {
                console.error('Error sending to N8N:', error);
                alert('Fout bij verzenden naar N8N. Controleer uw internetverbinding.');
            }
        }

        // Generate real summary based on transcription data
        function generateRealSummary() {
            if (transcriptionData.segments.length === 0) {
                return 'Geen transcriptie data beschikbaar.';
            }

            const patientSegments = transcriptionData.segments.filter(s => s.speaker === 'patient');
            const therapistSegments = transcriptionData.segments.filter(s => s.speaker === 'therapist');

            const insights = transcriptionData.insights.map(i => i.title).join(', ');

            return `Gesprek geanalyseerd met ${transcriptionData.segments.length} spraaksegmenten. ` +
                   `Patiënt sprak ${patientSegments.length} keer, therapeut ${therapistSegments.length} keer. ` +
                   `Gedetecteerde onderwerpen: ${insights || 'Geen specifieke medische onderwerpen gedetecteerd'}.`;
        }

        // Generate real treatment plan based on insights
        function generateRealTreatmentPlan() {
            const plan = [];

            // Base recommendations
            plan.push('Follow-up afspraak plannen binnen 2 weken');

            // Add specific recommendations based on detected insights
            transcriptionData.insights.forEach(insight => {
                switch (insight.type) {
                    case 'dizziness':
                        plan.push('Medicatie evaluatie voor duizeligheidsklachten');
                        break;
                    case 'anxiety':
                        plan.push('Angstmanagement technieken introduceren');
                        break;
                    case 'medication':
                        plan.push('Medicatie review met huisarts');
                        break;
                    case 'sleep':
                        plan.push('Slaaphygiëne bespreken en verbeteren');
                        break;
                    case 'breathing':
                        plan.push('Voortzetten en uitbreiden ademhalingstechnieken');
                        break;
                }
            });

            // Add general recommendation if no specific insights
            if (transcriptionData.insights.length === 0) {
                plan.push('Algemene gezondheidscheck en symptoommonitoring');
            }

            return plan;
        }

        // Export to PDF (placeholder)
        function exportToPDF() {
            alert('PDF export functionaliteit wordt geïmplementeerd...');
        }

        // Real transcription functionality
        let transcriptionData = {
            fullText: '',
            segments: [],
            insights: [],
            currentTime: 0
        };

        // Start real transcription process with OpenAI Whisper
        async function startRealTranscription() {
            const audioPlayer = document.getElementById('audioPlayer');
            const audioFileInput = document.getElementById('audioFileInput');
            const transcriptionChat = document.getElementById('transcriptionChat');
            const progressBar = document.getElementById('progressBar');
            const progressPercentage = document.getElementById('progressPercentage');

            console.log('Starting OpenAI Whisper transcription...');
            console.log('Audio player:', audioPlayer);
            console.log('Audio file input:', audioFileInput);
            console.log('Files in input:', audioFileInput?.files);
            console.log('Current uploaded file:', window.currentAudioFile);

            if (!audioPlayer) {
                alert('Audio player niet gevonden.');
                return;
            }

            // Check for uploaded file in multiple ways
            let audioFile = null;

            if (audioFileInput && audioFileInput.files[0]) {
                audioFile = audioFileInput.files[0];
                console.log('Found file in input:', audioFile.name);
            } else if (window.currentAudioFile) {
                audioFile = window.currentAudioFile;
                console.log('Found file in global variable:', audioFile.name);
            } else {
                console.error('No audio file found');
                alert('Geen audio bestand geladen. Upload eerst een MP3 bestand.');
                return;
            }

            // Check file size (max 25MB for Whisper API)
            if (audioFile.size > 25 * 1024 * 1024) {
                alert('Audio bestand is te groot. Maximum 25MB toegestaan.');
                return;
            }

            // Clear previous content
            transcriptionChat.innerHTML = '';
            transcriptionData = { fullText: '', segments: [], insights: [], currentTime: 0 };

            // Update status
            updateAnalysisStatus('Uploaden naar OpenAI Whisper...', 'info');

            try {
                // Start Whisper transcription
                await transcribeWithWhisper(audioFile, audioPlayer);
            } catch (error) {
                console.error('Whisper transcription error:', error);
                updateAnalysisStatus('Fout bij transcriptie. Probeer opnieuw.', 'error');
                alert('Fout bij transcriptie: ' + error.message);
            }
        }

        // Transcribe audio using OpenAI Whisper API with automatic language detection
        async function transcribeWithWhisper(audioFile, audioPlayer) {
            const formData = new FormData();
            formData.append('file', audioFile);
            formData.append('model', 'whisper-1');
            // Remove language parameter to let Whisper auto-detect language
            formData.append('response_format', 'verbose_json'); // Get timestamps and language info
            formData.append('timestamp_granularities[]', 'segment');

            updateAnalysisStatus('Transcriberen met OpenAI Whisper (auto-detectie taal)...', 'info');

            const response = await fetch('https://api.openai.com/v1/audio/transcriptions', {
                method: 'POST',
                headers: {
                    'Authorization': 'Bearer 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'
                },
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`Whisper API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
            }

            const transcriptionResult = await response.json();
            console.log('Whisper transcription result:', transcriptionResult);
            console.log('Detected language:', transcriptionResult.language);

            // Store detected language for later use
            window.detectedLanguage = transcriptionResult.language;

            // Process the transcription result
            await processWhisperTranscription(transcriptionResult, audioPlayer);
        }

        // Process Whisper transcription results with comprehensive analysis and sync playback
        async function processWhisperTranscription(transcriptionResult, audioPlayer) {
            updateAnalysisStatus('Analyseren volledige conversatie...', 'info');

            const fullText = transcriptionResult.text;
            const segments = transcriptionResult.segments || [];
            const detectedLanguage = transcriptionResult.language || 'en';

            console.log('Processing Whisper transcription:', {
                fullText,
                segments: segments.length,
                language: detectedLanguage
            });

            // Store full transcription
            transcriptionData.fullText = fullText;

            // Clear transcription chat for real content
            const transcriptionChat = document.getElementById('transcriptionChat');
            transcriptionChat.innerHTML = `
                <div class="text-center text-gray-500 dark:text-gray-400 text-sm py-8">
                    <i class="fas fa-brain text-2xl mb-2 animate-pulse"></i>
                    <p>AI analyseert volledige conversatie voor speaker identificatie...</p>
                </div>
            `;

            // STEP 1: Comprehensive analysis of entire conversation
            updateAnalysisStatus('AI leest volledige conversatie...', 'info');
            const conversationAnalysis = await analyzeEntireConversation(segments, detectedLanguage, fullText);

            // STEP 2: Intelligent speaker assignment with double-check
            updateAnalysisStatus('Bepalen wie spreekt (dubbele controle)...', 'info');
            const speakerAssignments = await performIntelligentSpeakerAssignment(segments, conversationAnalysis);

            // STEP 3: Validation and correction pass
            updateAnalysisStatus('Valideren speaker toewijzingen...', 'info');
            const validatedAssignments = await validateAndCorrectSpeakers(segments, speakerAssignments, conversationAnalysis);

            // Clear the analysis message
            transcriptionChat.innerHTML = '';

            // STEP 4: Synchronous playback with audio
            updateAnalysisStatus('Starten synchrone transcriptie weergave...', 'info');
            await startSynchronousTranscriptionPlayback(segments, validatedAssignments, audioPlayer, detectedLanguage);
        }

        // Analyze entire conversation to understand context and patterns
        async function analyzeEntireConversation(segments, language, fullText) {
            console.log('Analyzing entire conversation for context...');

            const analysis = {
                language: language,
                totalSegments: segments.length,
                conversationPatterns: [],
                medicalTerms: [],
                questionAnswerPairs: [],
                emotionalMarkers: [],
                professionalLanguage: [],
                personalExperiences: [],
                conversationFlow: []
            };

            // Analyze all segments for patterns
            for (let i = 0; i < segments.length; i++) {
                const segment = segments[i];
                const text = segment.text.trim();
                if (!text) continue;

                const segmentAnalysis = {
                    index: i,
                    text: text,
                    isQuestion: text.includes('?') || containsQuestionWords(text, language),
                    isProfessional: containsProfessionalLanguage(text, language),
                    isPersonal: containsPersonalExperience(text, language),
                    isEmotional: containsEmotionalLanguage(text, language),
                    isMedical: containsMedicalTerms(text, language),
                    pronounAnalysis: analyzePronounUsage(text, language),
                    contextClues: extractContextClues(text, language)
                };

                analysis.conversationFlow.push(segmentAnalysis);
            }

            // Identify conversation patterns
            analysis.questionAnswerPairs = identifyQuestionAnswerPairs(analysis.conversationFlow);
            analysis.conversationPatterns = identifyConversationPatterns(analysis.conversationFlow);

            console.log('Conversation analysis completed:', analysis);
            return analysis;
        }

        // Perform intelligent speaker assignment based on comprehensive analysis
        async function performIntelligentSpeakerAssignment(segments, analysis) {
            console.log('Performing intelligent speaker assignment...');

            const assignments = [];
            const conversationContext = {
                currentSpeaker: null,
                speakerHistory: [],
                confidenceScores: [],
                patternMatches: []
            };

            // Process each segment with full context
            for (let i = 0; i < segments.length; i++) {
                const segment = segments[i];
                const segmentAnalysis = analysis.conversationFlow[i];

                if (!segmentAnalysis) {
                    assignments.push('patient');
                    continue;
                }

                // Multi-factor analysis for speaker determination
                const speakerScores = calculateSpeakerScores(segmentAnalysis, analysis, conversationContext, i);

                // Determine speaker with confidence
                const assignment = determineSpeakerWithConfidence(speakerScores, conversationContext);
                assignments.push(assignment.speaker);

                // Update context
                conversationContext.currentSpeaker = assignment.speaker;
                conversationContext.speakerHistory.push(assignment.speaker);
                conversationContext.confidenceScores.push(assignment.confidence);

                console.log(`Segment ${i}: "${segmentAnalysis.text.substring(0, 50)}..." → ${assignment.speaker} (confidence: ${assignment.confidence})`);
            }

            return assignments;
        }

        // Helper functions for conversation analysis
        function containsQuestionWords(text, language) {
            const questionWords = language === 'nl' ?
                ['hoe', 'wat', 'wanneer', 'waar', 'waarom', 'welke', 'wie', 'hoeveel'] :
                ['how', 'what', 'when', 'where', 'why', 'which', 'who', 'how much', 'how many'];

            const lowerText = text.toLowerCase();
            return questionWords.some(word => lowerText.includes(word));
        }

        function containsProfessionalLanguage(text, language) {
            const professionalTerms = language === 'nl' ?
                ['behandeling', 'therapie', 'medicatie', 'dosering', 'bijwerkingen', 'diagnose', 'controle', 'follow-up', 'adviseer', 'raad aan'] :
                ['treatment', 'therapy', 'medication', 'dosage', 'side effects', 'diagnosis', 'monitoring', 'follow-up', 'recommend', 'suggest'];

            const lowerText = text.toLowerCase();
            return professionalTerms.some(term => lowerText.includes(term));
        }

        function containsPersonalExperience(text, language) {
            const personalTerms = language === 'nl' ?
                ['ik voel', 'ik heb', 'ik ben', 'ik ervaar', 'bij mij', 'mijn klachten', 'ik merk', 'ik krijg'] :
                ['i feel', 'i have', 'i am', 'i experience', 'for me', 'my symptoms', 'i notice', 'i get'];

            const lowerText = text.toLowerCase();
            return personalTerms.some(term => lowerText.includes(term));
        }

        function containsEmotionalLanguage(text, language) {
            const emotionalTerms = language === 'nl' ?
                ['angstig', 'bang', 'zorgen', 'verdrietig', 'gefrustreerd', 'moe', 'uitgeput', 'blij', 'opgelucht'] :
                ['anxious', 'scared', 'worried', 'sad', 'frustrated', 'tired', 'exhausted', 'happy', 'relieved'];

            const lowerText = text.toLowerCase();
            return emotionalTerms.some(term => lowerText.includes(term));
        }

        function containsMedicalTerms(text, language) {
            const medicalTerms = language === 'nl' ?
                ['pijn', 'duizelig', 'misselijk', 'hoofdpijn', 'buikpijn', 'koorts', 'hoest', 'kortademig'] :
                ['pain', 'dizzy', 'nauseous', 'headache', 'stomach ache', 'fever', 'cough', 'short of breath'];

            const lowerText = text.toLowerCase();
            return medicalTerms.some(term => lowerText.includes(term));
        }

        function analyzePronounUsage(text, language) {
            const lowerText = text.toLowerCase();

            if (language === 'nl') {
                const firstPerson = (lowerText.match(/\b(ik|mij|me|mijn)\b/g) || []).length;
                const secondPerson = (lowerText.match(/\b(u|uw|jij|jouw|je)\b/g) || []).length;
                return { firstPerson, secondPerson, ratio: firstPerson / (secondPerson + 1) };
            } else {
                const firstPerson = (lowerText.match(/\b(i|me|my|myself)\b/g) || []).length;
                const secondPerson = (lowerText.match(/\b(you|your|yourself)\b/g) || []).length;
                return { firstPerson, secondPerson, ratio: firstPerson / (secondPerson + 1) };
            }
        }

        function extractContextClues(text, language) {
            const clues = [];
            const lowerText = text.toLowerCase();

            // Professional addressing patterns
            const professionalAddressing = language === 'nl' ?
                ['kunt u', 'heeft u', 'zou u kunnen', 'wat denkt u'] :
                ['can you', 'have you', 'could you', 'what do you think'];

            professionalAddressing.forEach(pattern => {
                if (lowerText.includes(pattern)) {
                    clues.push({ type: 'professional_addressing', pattern, confidence: 0.8 });
                }
            });

            // Patient self-reference
            const selfReference = language === 'nl' ?
                ['ik voel me', 'ik heb last van', 'bij mij is', 'ik ervaar'] :
                ['i feel', 'i suffer from', 'i have', 'i experience'];

            selfReference.forEach(pattern => {
                if (lowerText.includes(pattern)) {
                    clues.push({ type: 'self_reference', pattern, confidence: 0.9 });
                }
            });

            return clues;
        }

        function identifyQuestionAnswerPairs(conversationFlow) {
            const pairs = [];

            for (let i = 0; i < conversationFlow.length - 1; i++) {
                const current = conversationFlow[i];
                const next = conversationFlow[i + 1];

                if (current.isQuestion && !next.isQuestion) {
                    pairs.push({
                        questionIndex: i,
                        answerIndex: i + 1,
                        questionText: current.text,
                        answerText: next.text,
                        confidence: 0.8
                    });
                }
            }

            return pairs;
        }

        function identifyConversationPatterns(conversationFlow) {
            const patterns = [];

            // Look for alternating patterns
            let currentPattern = [];
            let lastSpeakerType = null;

            conversationFlow.forEach((segment, index) => {
                const speakerType = segment.isProfessional ? 'professional' : 'personal';

                if (speakerType !== lastSpeakerType) {
                    if (currentPattern.length > 0) {
                        patterns.push({
                            type: 'alternating',
                            pattern: [...currentPattern],
                            confidence: currentPattern.length > 2 ? 0.7 : 0.4
                        });
                    }
                    currentPattern = [{ index, type: speakerType }];
                } else {
                    currentPattern.push({ index, type: speakerType });
                }

                lastSpeakerType = speakerType;
            });

            return patterns;
        }

        // Calculate comprehensive speaker scores
        function calculateSpeakerScores(segmentAnalysis, conversationAnalysis, context, segmentIndex) {
            let patientScore = 0;
            let therapistScore = 0;

            // 1. Personal experience indicators (strong patient signals)
            if (segmentAnalysis.isPersonal) {
                patientScore += 10;
            }

            // 2. Professional language (strong therapist signals)
            if (segmentAnalysis.isProfessional) {
                therapistScore += 10;
            }

            // 3. Question patterns (therapists ask more questions)
            if (segmentAnalysis.isQuestion) {
                therapistScore += 6;
            }

            // 4. Pronoun analysis
            if (segmentAnalysis.pronounAnalysis.ratio > 2) {
                patientScore += 5; // High first-person usage
            } else if (segmentAnalysis.pronounAnalysis.secondPerson > 1) {
                therapistScore += 4; // Addressing the patient
            }

            // 5. Context clues
            segmentAnalysis.contextClues.forEach(clue => {
                if (clue.type === 'self_reference') {
                    patientScore += clue.confidence * 8;
                } else if (clue.type === 'professional_addressing') {
                    therapistScore += clue.confidence * 7;
                }
            });

            // 6. Question-answer pair analysis
            const qaScore = analyzeQuestionAnswerContext(segmentIndex, conversationAnalysis, context);
            patientScore += qaScore.patient;
            therapistScore += qaScore.therapist;

            // 7. Emotional language (patients express more emotions)
            if (segmentAnalysis.isEmotional) {
                patientScore += 4;
            }

            // 8. Medical symptoms (patients describe symptoms)
            if (segmentAnalysis.isMedical && segmentAnalysis.isPersonal) {
                patientScore += 6;
            } else if (segmentAnalysis.isMedical && segmentAnalysis.isProfessional) {
                therapistScore += 3;
            }

            return { patient: patientScore, therapist: therapistScore };
        }

        function analyzeQuestionAnswerContext(segmentIndex, conversationAnalysis, context) {
            let patientScore = 0;
            let therapistScore = 0;

            // Check if this segment is part of a question-answer pair
            const relevantPairs = conversationAnalysis.questionAnswerPairs.filter(pair =>
                pair.questionIndex === segmentIndex || pair.answerIndex === segmentIndex
            );

            relevantPairs.forEach(pair => {
                if (pair.questionIndex === segmentIndex) {
                    // This is a question
                    therapistScore += 5;
                } else if (pair.answerIndex === segmentIndex) {
                    // This is an answer
                    patientScore += 5;
                }
            });

            return { patient: patientScore, therapist: therapistScore };
        }

        function determineSpeakerWithConfidence(scores, context) {
            const totalPatient = scores.patient;
            const totalTherapist = scores.therapist;
            const difference = Math.abs(totalPatient - totalTherapist);

            let speaker, confidence;

            if (totalPatient > totalTherapist) {
                speaker = 'patient';
                confidence = Math.min(0.95, 0.5 + (difference / 20));
            } else if (totalTherapist > totalPatient) {
                speaker = 'therapist';
                confidence = Math.min(0.95, 0.5 + (difference / 20));
            } else {
                // Tie - use context or default
                speaker = context.speakerHistory.length % 2 === 0 ? 'patient' : 'therapist';
                confidence = 0.3;
            }

            return { speaker, confidence };
        }

        // Validate and correct speaker assignments with double-check
        async function validateAndCorrectSpeakers(segments, assignments, conversationAnalysis) {
            console.log('Validating speaker assignments with double-check...');

            const correctedAssignments = [...assignments];
            const validationResults = [];

            // Pass 1: Check for obvious errors
            for (let i = 0; i < segments.length; i++) {
                const segment = segments[i];
                const assignment = assignments[i];
                const segmentAnalysis = conversationAnalysis.conversationFlow[i];

                if (!segmentAnalysis) continue;

                // Double-check high-confidence indicators
                const validation = {
                    index: i,
                    originalAssignment: assignment,
                    confidence: 0.5,
                    reasons: []
                };

                // Strong patient indicators should not be assigned to therapist
                if (assignment === 'therapist' && segmentAnalysis.isPersonal) {
                    const personalStrength = calculatePersonalStrength(segmentAnalysis.text, conversationAnalysis.language);
                    if (personalStrength > 0.8) {
                        correctedAssignments[i] = 'patient';
                        validation.correctedTo = 'patient';
                        validation.confidence = personalStrength;
                        validation.reasons.push('Strong personal experience language detected');
                    }
                }

                // Strong professional indicators should not be assigned to patient
                if (assignment === 'patient' && segmentAnalysis.isProfessional) {
                    const professionalStrength = calculateProfessionalStrength(segmentAnalysis.text, conversationAnalysis.language);
                    if (professionalStrength > 0.8) {
                        correctedAssignments[i] = 'therapist';
                        validation.correctedTo = 'therapist';
                        validation.confidence = professionalStrength;
                        validation.reasons.push('Strong professional language detected');
                    }
                }

                validationResults.push(validation);
            }

            // Pass 2: Check conversation flow consistency
            const flowCorrected = checkConversationFlowConsistency(correctedAssignments, segments, conversationAnalysis);

            console.log('Validation completed. Corrections made:', validationResults.filter(v => v.correctedTo).length);
            return flowCorrected;
        }

        function calculatePersonalStrength(text, language) {
            const lowerText = text.toLowerCase();
            let strength = 0;

            const strongPersonalIndicators = language === 'nl' ?
                ['ik voel me', 'ik heb last van', 'bij mij', 'ik ervaar', 'mijn klachten'] :
                ['i feel', 'i suffer from', 'for me', 'i experience', 'my symptoms'];

            strongPersonalIndicators.forEach(indicator => {
                if (lowerText.includes(indicator)) {
                    strength += 0.3;
                }
            });

            return Math.min(1.0, strength);
        }

        function calculateProfessionalStrength(text, language) {
            const lowerText = text.toLowerCase();
            let strength = 0;

            const strongProfessionalIndicators = language === 'nl' ?
                ['kunt u beschrijven', 'ik adviseer', 'we kunnen proberen', 'heeft u geprobeerd'] :
                ['can you describe', 'i recommend', 'we can try', 'have you tried'];

            strongProfessionalIndicators.forEach(indicator => {
                if (lowerText.includes(indicator)) {
                    strength += 0.3;
                }
            });

            return Math.min(1.0, strength);
        }

        function checkConversationFlowConsistency(assignments, segments, conversationAnalysis) {
            const corrected = [...assignments];

            // Smooth out rapid alternations that don't make sense
            for (let i = 1; i < corrected.length - 1; i++) {
                if (corrected[i] !== corrected[i-1] && corrected[i] !== corrected[i+1] && corrected[i-1] === corrected[i+1]) {
                    // Single different speaker - check if it makes sense
                    const segmentAnalysis = conversationAnalysis.conversationFlow[i];
                    if (segmentAnalysis && !segmentAnalysis.isQuestion && !segmentAnalysis.isProfessional && !segmentAnalysis.isPersonal) {
                        // Neutral segment, probably continuation
                        corrected[i] = corrected[i-1];
                        console.log(`Flow correction: segment ${i} changed from ${assignments[i]} to ${corrected[i]}`);
                    }
                }
            }

            return corrected;
        }

        // Synchronous transcription playback that follows audio timing
        async function startSynchronousTranscriptionPlayback(segments, speakerAssignments, audioPlayer, language) {
            console.log('Starting synchronous transcription playback...');

            // Clear transcription area
            const transcriptionChat = document.getElementById('transcriptionChat');
            transcriptionChat.innerHTML = '';

            // Store segments with speaker assignments
            transcriptionData.segments = [];

            // Prepare all segments
            for (let i = 0; i < segments.length; i++) {
                const segment = segments[i];
                const speaker = speakerAssignments[i];
                const text = segment.text.trim();

                if (!text) continue;

                transcriptionData.segments.push({
                    speaker: speaker,
                    text: text,
                    time: segment.start,
                    timeString: formatTime(segment.start),
                    start: segment.start,
                    end: segment.end,
                    language: language,
                    index: i
                });
            }

            // Start audio playback
            updateAnalysisStatus('Audio wordt afgespeeld met synchrone transcriptie...', 'info');

            // Add play button and controls
            addAudioControls(audioPlayer);

            // Monitor audio playback and show transcription in sync
            monitorAudioPlayback(audioPlayer, transcriptionData.segments);

            // Complete setup
            setTimeout(() => {
                completeTranscription();
                updateAnalysisStatus(`Transcriptie gereed (${language.toUpperCase()}) - Klik play om te starten`, 'success');
            }, 1000);
        }

        // Add audio controls for synchronized playback
        function addAudioControls(audioPlayer) {
            const transcriptionChat = document.getElementById('transcriptionChat');

            const controlsDiv = document.createElement('div');
            controlsDiv.className = 'text-center py-4 border-b border-gray-200 dark:border-gray-700 mb-4';
            controlsDiv.innerHTML = `
                <div class="flex justify-center items-center space-x-4">
                    <button id="playPauseBtn" onclick="toggleAudioPlayback()" class="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-all flex items-center">
                        <i class="fas fa-play mr-2"></i>
                        Start Synchrone Transcriptie
                    </button>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        <span id="currentTime">0:00</span> / <span id="totalTime">0:00</span>
                    </div>
                </div>
                <div class="mt-2 text-xs text-gray-400">
                    Transcriptie verschijnt synchroon met de audio
                </div>
            `;

            transcriptionChat.appendChild(controlsDiv);

            // Update total time
            if (audioPlayer.duration) {
                document.getElementById('totalTime').textContent = formatTime(audioPlayer.duration);
            }
        }

        // Monitor audio playback and show transcription synchronously with real-time correction
        function monitorAudioPlayback(audioPlayer, segments) {
            let currentSegmentIndex = 0;
            const displayedSegments = new Set();
            const correctionQueue = [];

            // Audio time update listener
            audioPlayer.addEventListener('timeupdate', () => {
                const currentTime = audioPlayer.currentTime;

                // Update time display
                const currentTimeElement = document.getElementById('currentTime');
                if (currentTimeElement) {
                    currentTimeElement.textContent = formatTime(currentTime);
                }

                // Show segments that should be visible at current time
                segments.forEach((segment, index) => {
                    if (currentTime >= segment.start && !displayedSegments.has(index)) {
                        // Time to show this segment
                        displaySegmentSynchronously(segment, index);
                        displayedSegments.add(index);

                        // Real-time speaker verification during playback
                        performRealTimeSpeakerVerification(segment, index, segments);

                        // Analyze content for medical insights
                        analyzeTranscriptContent(segment.text, segment.speaker);

                        // Update progress
                        const progress = ((index + 1) / segments.length) * 100;
                        updateProgressBar(progress);
                    }
                });

                // Process any pending corrections
                processPendingCorrections();
            });

            // Audio play/pause listeners
            audioPlayer.addEventListener('play', () => {
                const playBtn = document.getElementById('playPauseBtn');
                if (playBtn) {
                    playBtn.innerHTML = '<i class="fas fa-pause mr-2"></i>Pauzeer Transcriptie';
                }
            });

            audioPlayer.addEventListener('pause', () => {
                const playBtn = document.getElementById('playPauseBtn');
                if (playBtn) {
                    playBtn.innerHTML = '<i class="fas fa-play mr-2"></i>Hervat Transcriptie';
                }
            });

            // Reset on seek
            audioPlayer.addEventListener('seeked', () => {
                const currentTime = audioPlayer.currentTime;

                // Clear displayed segments that are after current time
                const transcriptionChat = document.getElementById('transcriptionChat');
                const messages = transcriptionChat.querySelectorAll('.chat-message');

                messages.forEach(message => {
                    const messageTime = parseFloat(message.dataset.time || '0');
                    if (messageTime > currentTime) {
                        message.remove();
                        displayedSegments.delete(parseInt(message.dataset.index || '0'));
                    }
                });
            });
        }

        // Display segment synchronously with enhanced styling
        function displaySegmentSynchronously(segment, index) {
            const transcriptionChat = document.getElementById('transcriptionChat');
            const isPatient = segment.speaker === 'patient';

            const messageDiv = document.createElement('div');
            messageDiv.className = `chat-message flex ${isPatient ? 'justify-start' : 'justify-end'} mb-4 animate-fade-in`;
            messageDiv.dataset.time = segment.start;
            messageDiv.dataset.index = index;

            messageDiv.innerHTML = `
                <div class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    isPatient
                        ? 'bg-blue-500 text-white rounded-bl-none'
                        : 'bg-green-500 text-white rounded-br-none'
                } shadow-lg transform transition-all duration-300 hover:scale-105">
                    <div class="flex items-center mb-1">
                        <i class="fas ${isPatient ? 'fa-user' : 'fa-user-md'} mr-2"></i>
                        <span class="font-semibold text-sm">
                            ${isPatient ? 'Patiënt' : 'Therapeut'}
                        </span>
                        <span class="ml-auto text-xs opacity-75">${segment.timeString}</span>
                    </div>
                    <p class="text-sm leading-relaxed">${segment.text}</p>
                </div>
            `;

            transcriptionChat.appendChild(messageDiv);

            // Auto-scroll to latest message
            messageDiv.scrollIntoView({ behavior: 'smooth', block: 'end' });

            console.log(`Displayed segment ${index} at ${segment.timeString}: ${segment.speaker} - "${segment.text.substring(0, 50)}..."`);
        }

        // Real-time speaker verification during playback
        async function performRealTimeSpeakerVerification(segment, index, allSegments) {
            // Get context window for better analysis
            const contextWindow = {
                previous: index > 0 ? allSegments[index - 1] : null,
                current: segment,
                next: index < allSegments.length - 1 ? allSegments[index + 1] : null,
                previousTwo: index > 1 ? allSegments[index - 2] : null,
                nextTwo: index < allSegments.length - 2 ? allSegments[index + 2] : null
            };

            // Perform deep content analysis
            const contentAnalysis = await performDeepContentAnalysis(segment.text, segment.language || 'nl');

            // Check if current assignment makes sense in context
            const contextualAnalysis = analyzeContextualConsistency(contextWindow, contentAnalysis);

            // If confidence is low or contradictory, flag for correction
            if (contextualAnalysis.needsCorrection) {
                console.log(`🔍 Real-time verification flagged segment ${index} for correction:`, contextualAnalysis.reason);

                // Queue for AI double-check
                queueForAIDoubleCheck(segment, index, contextWindow, contentAnalysis, contextualAnalysis);
            }
        }

        // Deep content analysis for speaker identification
        async function performDeepContentAnalysis(text, language) {
            const lowerText = text.toLowerCase();
            const analysis = {
                text: text,
                language: language,
                indicators: {
                    patient: [],
                    therapist: []
                },
                confidence: {
                    patient: 0,
                    therapist: 0
                },
                criticalPhrases: [],
                medicalContext: null
            };

            // Ultra-specific patient indicators
            const ultraPatientIndicators = language === 'nl' ? [
                { phrase: 'ik voel me duizelig', weight: 10, type: 'symptom_report' },
                { phrase: 'ik heb last van', weight: 9, type: 'complaint' },
                { phrase: 'bij mij is het', weight: 8, type: 'personal_experience' },
                { phrase: 'ik ervaar', weight: 8, type: 'personal_experience' },
                { phrase: 'mijn klachten', weight: 9, type: 'complaint' },
                { phrase: 'ik ben duizelig', weight: 10, type: 'symptom_report' },
                { phrase: 'ik word misselijk', weight: 10, type: 'symptom_report' },
                { phrase: 'ik heb pijn', weight: 10, type: 'symptom_report' },
                { phrase: 'ik slaap slecht', weight: 8, type: 'symptom_report' },
                { phrase: 'ik maak me zorgen', weight: 7, type: 'emotional_expression' },
                { phrase: 'ik ben bang', weight: 8, type: 'emotional_expression' }
            ] : [
                { phrase: 'i feel dizzy', weight: 10, type: 'symptom_report' },
                { phrase: 'i suffer from', weight: 9, type: 'complaint' },
                { phrase: 'i experience', weight: 8, type: 'personal_experience' },
                { phrase: 'my symptoms', weight: 9, type: 'complaint' },
                { phrase: 'i am dizzy', weight: 10, type: 'symptom_report' },
                { phrase: 'i get nauseous', weight: 10, type: 'symptom_report' },
                { phrase: 'i have pain', weight: 10, type: 'symptom_report' },
                { phrase: 'i sleep poorly', weight: 8, type: 'symptom_report' },
                { phrase: 'i worry', weight: 7, type: 'emotional_expression' },
                { phrase: 'i am scared', weight: 8, type: 'emotional_expression' }
            ];

            // Ultra-specific therapist indicators
            const ultraTherapistIndicators = language === 'nl' ? [
                { phrase: 'kunt u beschrijven', weight: 10, type: 'professional_inquiry' },
                { phrase: 'wanneer heeft u', weight: 9, type: 'professional_inquiry' },
                { phrase: 'hoe vaak ervaart u', weight: 9, type: 'professional_inquiry' },
                { phrase: 'ik adviseer u', weight: 10, type: 'professional_advice' },
                { phrase: 'we kunnen proberen', weight: 8, type: 'treatment_suggestion' },
                { phrase: 'heeft u geprobeerd', weight: 8, type: 'professional_inquiry' },
                { phrase: 'wat voor medicatie', weight: 9, type: 'medical_inquiry' },
                { phrase: 'uw dosering', weight: 8, type: 'medical_management' },
                { phrase: 'contact met uw huisarts', weight: 9, type: 'professional_referral' },
                { phrase: 'dat is normaal', weight: 7, type: 'professional_reassurance' }
            ] : [
                { phrase: 'can you describe', weight: 10, type: 'professional_inquiry' },
                { phrase: 'when did you', weight: 9, type: 'professional_inquiry' },
                { phrase: 'how often do you experience', weight: 9, type: 'professional_inquiry' },
                { phrase: 'i recommend', weight: 10, type: 'professional_advice' },
                { phrase: 'we can try', weight: 8, type: 'treatment_suggestion' },
                { phrase: 'have you tried', weight: 8, type: 'professional_inquiry' },
                { phrase: 'what medication', weight: 9, type: 'medical_inquiry' },
                { phrase: 'your dosage', weight: 8, type: 'medical_management' },
                { phrase: 'contact your doctor', weight: 9, type: 'professional_referral' },
                { phrase: 'that is normal', weight: 7, type: 'professional_reassurance' }
            ];

            // Analyze patient indicators
            ultraPatientIndicators.forEach(indicator => {
                if (lowerText.includes(indicator.phrase)) {
                    analysis.indicators.patient.push(indicator);
                    analysis.confidence.patient += indicator.weight;
                    analysis.criticalPhrases.push({
                        phrase: indicator.phrase,
                        speaker: 'patient',
                        weight: indicator.weight,
                        type: indicator.type
                    });
                }
            });

            // Analyze therapist indicators
            ultraTherapistIndicators.forEach(indicator => {
                if (lowerText.includes(indicator.phrase)) {
                    analysis.indicators.therapist.push(indicator);
                    analysis.confidence.therapist += indicator.weight;
                    analysis.criticalPhrases.push({
                        phrase: indicator.phrase,
                        speaker: 'therapist',
                        weight: indicator.weight,
                        type: indicator.type
                    });
                }
            });

            // Determine medical context
            if (analysis.criticalPhrases.some(p => p.type === 'symptom_report')) {
                analysis.medicalContext = 'symptom_reporting';
            } else if (analysis.criticalPhrases.some(p => p.type === 'professional_inquiry')) {
                analysis.medicalContext = 'professional_assessment';
            } else if (analysis.criticalPhrases.some(p => p.type === 'treatment_suggestion')) {
                analysis.medicalContext = 'treatment_planning';
            }

            return analysis;
        }

        // Analyze contextual consistency
        function analyzeContextualConsistency(contextWindow, contentAnalysis) {
            const currentSegment = contextWindow.current;
            const currentSpeaker = currentSegment.speaker;

            const analysis = {
                needsCorrection: false,
                confidence: 0.5,
                reason: '',
                suggestedSpeaker: currentSpeaker,
                criticalEvidence: []
            };

            // Check for critical contradictions
            const strongPatientEvidence = contentAnalysis.confidence.patient;
            const strongTherapistEvidence = contentAnalysis.confidence.therapist;

            // If assigned to therapist but has strong patient evidence
            if (currentSpeaker === 'therapist' && strongPatientEvidence >= 8) {
                analysis.needsCorrection = true;
                analysis.suggestedSpeaker = 'patient';
                analysis.confidence = Math.min(0.95, strongPatientEvidence / 10);
                analysis.reason = `Strong patient evidence (score: ${strongPatientEvidence}) contradicts therapist assignment`;
                analysis.criticalEvidence = contentAnalysis.indicators.patient;
            }

            // If assigned to patient but has strong therapist evidence
            else if (currentSpeaker === 'patient' && strongTherapistEvidence >= 8) {
                analysis.needsCorrection = true;
                analysis.suggestedSpeaker = 'therapist';
                analysis.confidence = Math.min(0.95, strongTherapistEvidence / 10);
                analysis.reason = `Strong therapist evidence (score: ${strongTherapistEvidence}) contradicts patient assignment`;
                analysis.criticalEvidence = contentAnalysis.indicators.therapist;
            }

            // Check conversation flow consistency
            if (!analysis.needsCorrection) {
                const flowAnalysis = checkConversationFlowLogic(contextWindow);
                if (flowAnalysis.inconsistent) {
                    analysis.needsCorrection = true;
                    analysis.suggestedSpeaker = flowAnalysis.suggestedSpeaker;
                    analysis.confidence = flowAnalysis.confidence;
                    analysis.reason = flowAnalysis.reason;
                }
            }

            return analysis;
        }

        // Check conversation flow logic
        function checkConversationFlowLogic(contextWindow) {
            const analysis = {
                inconsistent: false,
                suggestedSpeaker: null,
                confidence: 0.5,
                reason: ''
            };

            const current = contextWindow.current;
            const previous = contextWindow.previous;
            const next = contextWindow.next;

            // Question-answer flow check
            if (previous && previous.text.includes('?')) {
                // Previous was a question
                if (previous.speaker === 'therapist' && current.speaker === 'therapist') {
                    // Therapist asking question followed by therapist - unusual
                    if (!current.text.includes('?')) {
                        analysis.inconsistent = true;
                        analysis.suggestedSpeaker = 'patient';
                        analysis.confidence = 0.7;
                        analysis.reason = 'Therapist question should be followed by patient answer';
                    }
                }
            }

            // Symptom reporting flow
            if (current.text.toLowerCase().includes('duizelig') ||
                current.text.toLowerCase().includes('dizzy') ||
                current.text.toLowerCase().includes('misselijk') ||
                current.text.toLowerCase().includes('nauseous')) {

                if (current.speaker === 'therapist') {
                    // Therapist reporting symptoms is unusual unless asking about them
                    if (!current.text.includes('?') && !current.text.toLowerCase().includes('heeft u')) {
                        analysis.inconsistent = true;
                        analysis.suggestedSpeaker = 'patient';
                        analysis.confidence = 0.8;
                        analysis.reason = 'Symptom reporting typically done by patient';
                    }
                }
            }

            return analysis;
        }

        // Queue segment for AI double-check
        function queueForAIDoubleCheck(segment, index, contextWindow, contentAnalysis, contextualAnalysis) {
            if (!window.aiDoubleCheckQueue) {
                window.aiDoubleCheckQueue = [];
            }

            window.aiDoubleCheckQueue.push({
                segment,
                index,
                contextWindow,
                contentAnalysis,
                contextualAnalysis,
                timestamp: Date.now()
            });

            console.log(`📋 Queued segment ${index} for AI double-check. Queue length: ${window.aiDoubleCheckQueue.length}`);
        }

        // Process pending corrections
        async function processPendingCorrections() {
            if (!window.aiDoubleCheckQueue || window.aiDoubleCheckQueue.length === 0) {
                return;
            }

            // Process one correction at a time to avoid overwhelming
            const correction = window.aiDoubleCheckQueue.shift();
            await performAIDoubleCheck(correction);
        }

        // Toggle audio playback
        function toggleAudioPlayback() {
            const audioPlayer = document.getElementById('audioPlayer');
            if (!audioPlayer) return;

            if (audioPlayer.paused) {
                audioPlayer.play();
            } else {
                audioPlayer.pause();
            }
        }

        // Advanced AI-based speaker detection using context analysis
        async function performAdvancedSpeakerDetection(segments, language) {
            console.log('Starting advanced AI speaker detection...');

            const speakerAssignments = [];
            const conversationContext = {
                previousSpeaker: null,
                speakerHistory: [],
                conversationFlow: [],
                language: language
            };

            // Analyze each segment in context
            for (let i = 0; i < segments.length; i++) {
                const segment = segments[i];
                const text = segment.text.trim();

                if (!text) {
                    speakerAssignments.push('patient');
                    continue;
                }

                // Get context window (previous and next segments)
                const contextWindow = {
                    previous: i > 0 ? segments[i - 1]?.text : null,
                    current: text,
                    next: i < segments.length - 1 ? segments[i + 1]?.text : null
                };

                // Determine speaker using advanced AI analysis
                const detectedSpeaker = analyzeSegmentWithAI(contextWindow, conversationContext, i);

                speakerAssignments.push(detectedSpeaker);

                // Update conversation context
                conversationContext.previousSpeaker = detectedSpeaker;
                conversationContext.speakerHistory.push(detectedSpeaker);
                conversationContext.conversationFlow.push({
                    speaker: detectedSpeaker,
                    text: text,
                    index: i
                });
            }

            // Post-process for consistency and flow
            const refinedAssignments = refineSpeakerAssignments(speakerAssignments, segments);

            console.log('Speaker detection completed:', refinedAssignments);
            return refinedAssignments;
        }

        // AI-powered segment analysis for speaker detection
        function analyzeSegmentWithAI(contextWindow, conversationContext, segmentIndex) {
            const text = contextWindow.current;
            const language = conversationContext.language;

            // Multi-layered analysis system
            let patientScore = 0;
            let therapistScore = 0;

            // 1. Language-specific pattern analysis
            const patterns = getLanguagePatterns(language);

            // 2. Strong linguistic indicators
            patterns.patient.strong.forEach(pattern => {
                if (text.toLowerCase().includes(pattern)) {
                    patientScore += 5;
                    console.log(`Strong patient indicator found: "${pattern}" in "${text.substring(0, 50)}..."`);
                }
            });

            patterns.therapist.strong.forEach(pattern => {
                if (text.toLowerCase().includes(pattern)) {
                    therapistScore += 5;
                    console.log(`Strong therapist indicator found: "${pattern}" in "${text.substring(0, 50)}..."`);
                }
            });

            // 3. Contextual flow analysis
            const flowScore = analyzeConversationFlow(contextWindow, conversationContext);
            patientScore += flowScore.patient;
            therapistScore += flowScore.therapist;

            // 4. Grammatical and syntactic analysis
            const syntaxScore = analyzeSyntaxPatterns(text, language);
            patientScore += syntaxScore.patient;
            therapistScore += syntaxScore.therapist;

            // 5. Medical terminology analysis
            const medicalScore = analyzeMedicalContext(text, language);
            patientScore += medicalScore.patient;
            therapistScore += medicalScore.therapist;

            // 6. Question-answer pattern detection
            const qaScore = analyzeQuestionAnswerPatterns(contextWindow, conversationContext);
            patientScore += qaScore.patient;
            therapistScore += qaScore.therapist;

            // 7. Emotional and experiential language
            const emotionalScore = analyzeEmotionalLanguage(text, language);
            patientScore += emotionalScore.patient;
            therapistScore += emotionalScore.therapist;

            console.log(`Segment ${segmentIndex}: "${text.substring(0, 50)}..." - Patient: ${patientScore}, Therapist: ${therapistScore}`);

            // Determine speaker with confidence threshold
            if (Math.abs(patientScore - therapistScore) < 2) {
                // Low confidence - use conversation flow
                return determineFromFlow(conversationContext, segmentIndex);
            }

            return patientScore > therapistScore ? 'patient' : 'therapist';
        }

        // Get language-specific patterns for speaker detection
        function getLanguagePatterns(language) {
            const patterns = {
                'nl': { // Dutch
                    patient: {
                        strong: [
                            'ik voel', 'ik heb', 'ik ben', 'ik neem', 'ik gebruik', 'ik krijg',
                            'ik merk', 'ik denk', 'ik maak me', 'mijn klachten', 'mijn medicatie',
                            'bij mij', 'ik ervaar', 'ik word', 'ik kan niet', 'ik heb last van',
                            'me zorgen', 'ik slaap', 'ik eet', 'ik loop', 'ik sta op', 'ik voel me'
                        ],
                        weak: ['duizelig', 'pijn', 'moe', 'angstig', 'slecht', 'ziek']
                    },
                    therapist: {
                        strong: [
                            'kunt u', 'heeft u', 'wanneer heeft u', 'hoe vaak', 'wat bedoelt u',
                            'kunt u beschrijven', 'heeft u geprobeerd', 'wat voor medicatie',
                            'ik adviseer', 'ik raad aan', 'we kunnen proberen', 'het is belangrijk',
                            'u zou kunnen', 'probeer eens', 'laten we kijken', 'wat denkt u van',
                            'hoe lang gebruikt u', 'welke dosering', 'heeft uw arts'
                        ],
                        weak: ['behandeling', 'therapie', 'medicatie', 'dosering', 'bijwerkingen']
                    }
                },
                'en': { // English
                    patient: {
                        strong: [
                            'i feel', 'i have', 'i am', 'i take', 'i use', 'i get',
                            'i notice', 'i think', 'i worry', 'my symptoms', 'my medication',
                            'for me', 'i experience', 'i become', 'i cannot', 'i suffer from',
                            'i sleep', 'i eat', 'i walk', 'i wake up', 'i feel like'
                        ],
                        weak: ['dizzy', 'pain', 'tired', 'anxious', 'sick', 'bad']
                    },
                    therapist: {
                        strong: [
                            'can you', 'have you', 'when did you', 'how often', 'what do you mean',
                            'can you describe', 'have you tried', 'what medication',
                            'i recommend', 'i suggest', 'we can try', 'it is important',
                            'you could', 'try to', 'let us look', 'what do you think',
                            'how long have you', 'what dosage', 'has your doctor'
                        ],
                        weak: ['treatment', 'therapy', 'medication', 'dosage', 'side effects']
                    }
                }
            };

            return patterns[language] || patterns['en']; // Default to English
        }

        // Analyze conversation flow for speaker continuity
        function analyzeConversationFlow(contextWindow, conversationContext) {
            let patientScore = 0;
            let therapistScore = 0;

            // Check if this follows a question-answer pattern
            if (contextWindow.previous) {
                const prevText = contextWindow.previous.toLowerCase();
                const currentText = contextWindow.current.toLowerCase();

                // If previous was a question, current is likely an answer
                if (prevText.includes('?') || prevText.match(/\b(hoe|wat|wanneer|waar|waarom|welke|how|what|when|where|why|which)\b/)) {
                    // Previous was likely therapist asking, current likely patient answering
                    if (conversationContext.previousSpeaker === 'therapist') {
                        patientScore += 3;
                    } else {
                        therapistScore += 1;
                    }
                }

                // If current is a follow-up question
                if (currentText.includes('?')) {
                    therapistScore += 2;
                }
            }

            // Conversation alternation pattern
            if (conversationContext.speakerHistory.length > 2) {
                const lastTwo = conversationContext.speakerHistory.slice(-2);
                if (lastTwo[0] === lastTwo[1]) {
                    // Same speaker twice, likely to alternate
                    if (lastTwo[0] === 'patient') {
                        therapistScore += 1;
                    } else {
                        patientScore += 1;
                    }
                }
            }

            return { patient: patientScore, therapist: therapistScore };
        }

        // Analyze syntax patterns for speaker identification
        function analyzeSyntaxPatterns(text, language) {
            let patientScore = 0;
            let therapistScore = 0;

            const lowerText = text.toLowerCase();

            // Question patterns (therapists ask more questions)
            const questionMarkers = lowerText.match(/\?/g);
            if (questionMarkers) {
                therapistScore += questionMarkers.length * 2;
            }

            // Imperative mood (therapists give instructions)
            const imperativePatterns = language === 'nl' ?
                ['probeer', 'neem', 'ga naar', 'bel', 'maak', 'stop met'] :
                ['try', 'take', 'go to', 'call', 'make', 'stop'];

            imperativePatterns.forEach(pattern => {
                if (lowerText.includes(pattern)) {
                    therapistScore += 2;
                }
            });

            // First person singular (patients talk about themselves)
            const firstPersonCount = language === 'nl' ?
                (lowerText.match(/\b(ik|mij|me|mijn)\b/g) || []).length :
                (lowerText.match(/\b(i|me|my|myself)\b/g) || []).length;

            if (firstPersonCount > 2) {
                patientScore += firstPersonCount;
            }

            // Second person (therapists address patient)
            const secondPersonCount = language === 'nl' ?
                (lowerText.match(/\b(u|uw|jij|jouw|je)\b/g) || []).length :
                (lowerText.match(/\b(you|your|yourself)\b/g) || []).length;

            if (secondPersonCount > 1) {
                therapistScore += secondPersonCount;
            }

            return { patient: patientScore, therapist: therapistScore };
        }

        // Analyze medical context and terminology
        function analyzeMedicalContext(text, language) {
            let patientScore = 0;
            let therapistScore = 0;

            const lowerText = text.toLowerCase();

            // Professional medical terms (therapist)
            const professionalTerms = language === 'nl' ?
                ['diagnose', 'behandeling', 'therapie', 'medicatie aanpassen', 'bijwerkingen', 'dosering', 'controle', 'follow-up'] :
                ['diagnosis', 'treatment', 'therapy', 'adjust medication', 'side effects', 'dosage', 'monitoring', 'follow-up'];

            professionalTerms.forEach(term => {
                if (lowerText.includes(term)) {
                    therapistScore += 2;
                }
            });

            // Patient experience terms
            const experienceTerms = language === 'nl' ?
                ['ik voel', 'pijn', 'last van', 'slecht', 'beter', 'erger', 'help niet'] :
                ['i feel', 'pain', 'suffering from', 'bad', 'better', 'worse', 'not helping'];

            experienceTerms.forEach(term => {
                if (lowerText.includes(term)) {
                    patientScore += 2;
                }
            });

            return { patient: patientScore, therapist: therapistScore };
        }

        // Analyze question-answer patterns
        function analyzeQuestionAnswerPatterns(contextWindow, conversationContext) {
            let patientScore = 0;
            let therapistScore = 0;

            const currentText = contextWindow.current.toLowerCase();

            // Direct answers to questions
            if (contextWindow.previous) {
                const prevText = contextWindow.previous.toLowerCase();

                if (prevText.includes('?')) {
                    // Previous was a question, current might be answer
                    const answerPatterns = conversationContext.language === 'nl' ?
                        ['ja', 'nee', 'soms', 'altijd', 'nooit', 'ongeveer', 'sinds'] :
                        ['yes', 'no', 'sometimes', 'always', 'never', 'about', 'since'];

                    answerPatterns.forEach(pattern => {
                        if (currentText.startsWith(pattern)) {
                            patientScore += 3;
                        }
                    });
                }
            }

            // Professional questioning patterns
            const questionStarters = conversationContext.language === 'nl' ?
                ['kunt u', 'heeft u', 'wanneer', 'hoe vaak', 'wat voor'] :
                ['can you', 'have you', 'when', 'how often', 'what kind'];

            questionStarters.forEach(starter => {
                if (currentText.startsWith(starter)) {
                    therapistScore += 4;
                }
            });

            return { patient: patientScore, therapist: therapistScore };
        }

        // Analyze emotional and experiential language
        function analyzeEmotionalLanguage(text, language) {
            let patientScore = 0;
            let therapistScore = 0;

            const lowerText = text.toLowerCase();

            // Emotional expressions (patients express feelings)
            const emotionalTerms = language === 'nl' ?
                ['bang', 'angstig', 'zorgen', 'verdrietig', 'gefrustreerd', 'moe', 'uitgeput'] :
                ['scared', 'anxious', 'worried', 'sad', 'frustrated', 'tired', 'exhausted'];

            emotionalTerms.forEach(term => {
                if (lowerText.includes(term)) {
                    patientScore += 2;
                }
            });

            // Empathetic responses (therapists show understanding)
            const empathyTerms = language === 'nl' ?
                ['ik begrijp', 'dat klinkt', 'dat moet', 'vervelend voor u'] :
                ['i understand', 'that sounds', 'that must', 'sorry to hear'];

            empathyTerms.forEach(term => {
                if (lowerText.includes(term)) {
                    therapistScore += 2;
                }
            });

            return { patient: patientScore, therapist: therapistScore };
        }

        // Determine speaker from conversation flow when confidence is low
        function determineFromFlow(conversationContext, segmentIndex) {
            // Default alternating pattern
            if (conversationContext.speakerHistory.length === 0) {
                return 'patient'; // Start with patient
            }

            const lastSpeaker = conversationContext.previousSpeaker;

            // Simple alternation as fallback
            return lastSpeaker === 'patient' ? 'therapist' : 'patient';
        }

        // Refine speaker assignments for consistency
        function refineSpeakerAssignments(assignments, segments) {
            const refined = [...assignments];

            // Smooth out rapid speaker changes (likely errors)
            for (let i = 1; i < refined.length - 1; i++) {
                if (refined[i] !== refined[i-1] && refined[i] !== refined[i+1] && refined[i-1] === refined[i+1]) {
                    // Single different speaker surrounded by same speaker - likely error
                    refined[i] = refined[i-1];
                    console.log(`Smoothed speaker assignment at segment ${i}`);
                }
            }

            // Ensure minimum segment length for speaker changes
            let currentSpeaker = refined[0];
            let speakerStartIndex = 0;

            for (let i = 1; i < refined.length; i++) {
                if (refined[i] !== currentSpeaker) {
                    // Speaker change detected
                    if (i - speakerStartIndex < 2) {
                        // Too short segment, extend previous speaker
                        for (let j = speakerStartIndex; j < i; j++) {
                            refined[j] = currentSpeaker;
                        }
                    }
                    currentSpeaker = refined[i];
                    speakerStartIndex = i;
                }
            }

            return refined;
        }

        // Advanced speaker detection from content patterns
        function detectSpeakerFromContent(text) {
            const lowerText = text.toLowerCase();

            // Strong patient indicators (first person statements)
            const strongPatientIndicators = [
                'ik voel', 'ik heb', 'ik ben', 'ik neem', 'ik gebruik', 'ik krijg',
                'ik merk', 'ik denk', 'ik maak me', 'mijn klachten', 'mijn medicatie',
                'bij mij', 'ik ervaar', 'ik word', 'ik kan niet', 'ik heb last van',
                'me zorgen', 'ik slaap', 'ik eet', 'ik loop', 'ik sta op'
            ];

            // Strong therapist indicators (professional questions/advice)
            const strongTherapistIndicators = [
                'kunt u', 'heeft u', 'wanneer heeft u', 'hoe vaak', 'wat bedoelt u',
                'kunt u beschrijven', 'heeft u geprobeerd', 'wat voor medicatie',
                'ik adviseer', 'ik raad aan', 'we kunnen proberen', 'het is belangrijk',
                'u zou kunnen', 'probeer eens', 'laten we kijken', 'wat denkt u van',
                'hoe lang gebruikt u', 'welke dosering', 'heeft uw arts', 'contact met uw huisarts'
            ];

            // Medical professional language
            const professionalIndicators = [
                'bijwerkingen', 'dosering', 'medicatie aanpassen', 'behandeling',
                'symptomen', 'diagnose', 'therapie', 'follow-up', 'controle',
                'huisarts', 'specialist', 'onderzoek', 'bloeddruk meten'
            ];

            // Patient experience language
            const experienceIndicators = [
                'duizelig', 'misselijk', 'moe', 'angstig', 'pijn', 'slecht geslapen',
                'hoofdpijn', 'buikpijn', 'kortademig', 'hartkloppingen', 'trillen',
                'zweten', 'draaierig', 'flauw', 'zwak'
            ];

            // Calculate scores with weights
            let patientScore = 0;
            let therapistScore = 0;

            // Strong indicators get higher weight
            strongPatientIndicators.forEach(indicator => {
                if (lowerText.includes(indicator)) patientScore += 3;
            });

            strongTherapistIndicators.forEach(indicator => {
                if (lowerText.includes(indicator)) therapistScore += 3;
            });

            // Professional language suggests therapist
            professionalIndicators.forEach(indicator => {
                if (lowerText.includes(indicator)) therapistScore += 1;
            });

            // Experience language suggests patient
            experienceIndicators.forEach(indicator => {
                if (lowerText.includes(indicator)) patientScore += 1;
            });

            // Question patterns (therapist asks questions)
            if (lowerText.includes('?') || lowerText.match(/\b(hoe|wat|wanneer|waar|waarom|welke)\b/)) {
                therapistScore += 2;
            }

            // Personal pronouns analysis
            const firstPersonCount = (lowerText.match(/\b(ik|mij|me|mijn)\b/g) || []).length;
            const secondPersonCount = (lowerText.match(/\b(u|uw|jij|jouw|je)\b/g) || []).length;

            if (firstPersonCount > secondPersonCount && firstPersonCount > 1) {
                patientScore += 2;
            } else if (secondPersonCount > firstPersonCount && secondPersonCount > 1) {
                therapistScore += 2;
            }

            console.log(`Speaker detection for "${text.substring(0, 50)}...": Patient=${patientScore}, Therapist=${therapistScore}`);

            // Determine speaker with confidence threshold
            if (patientScore > therapistScore && patientScore >= 2) {
                return 'patient';
            } else if (therapistScore > patientScore && therapistScore >= 2) {
                return 'therapist';
            }

            return null; // No clear indication
        }

        // Update progress bar
        function updateProgressBar(progress) {
            const progressBar = document.getElementById('progressBar');
            const progressPercentage = document.getElementById('progressPercentage');

            if (progressBar && progressPercentage) {
                progressBar.style.width = progress + '%';
                progressPercentage.textContent = Math.round(progress) + '%';
            }
        }

        // Setup audio context for real-time transcription
        function setupAudioContextForTranscription(audioPlayer, recognition) {
            try {
                // Create audio context
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const source = audioContext.createMediaElementSource(audioPlayer);
                const analyser = audioContext.createAnalyser();

                source.connect(analyser);
                analyser.connect(audioContext.destination);

                analyser.fftSize = 256;
                const bufferLength = analyser.frequencyBinCount;
                const dataArray = new Uint8Array(bufferLength);

                // Start audio playback
                audioPlayer.play().then(() => {
                    // Start speech recognition when audio starts
                    startSpeechRecognition(recognition, audioPlayer);

                    // Monitor audio levels
                    monitorAudioLevels(analyser, dataArray);
                }).catch(error => {
                    console.error('Error playing audio:', error);
                    startAudioAnalysisWithoutSpeech();
                });

            } catch (error) {
                console.error('Error setting up audio context:', error);
                startAudioAnalysisWithoutSpeech();
            }
        }

        // Start speech recognition
        function startSpeechRecognition(recognition, audioPlayer) {
            let isRecognizing = false;
            let lastTranscriptTime = 0;
            let currentSpeaker = 'patient'; // Start with patient
            let speakerSwitchInterval = 15; // Switch speaker every 15 seconds

            recognition.onstart = function() {
                isRecognizing = true;
                updateAnalysisStatus('Luisteren naar audio...', 'info');
            };

            recognition.onresult = function(event) {
                let interimTranscript = '';
                let finalTranscript = '';

                for (let i = event.resultIndex; i < event.results.length; i++) {
                    const transcript = event.results[i][0].transcript;

                    if (event.results[i].isFinal) {
                        finalTranscript += transcript;
                    } else {
                        interimTranscript += transcript;
                    }
                }

                if (finalTranscript) {
                    const currentTime = audioPlayer.currentTime;
                    const timeString = formatTime(currentTime);

                    // Determine speaker based on time intervals
                    if (currentTime - lastTranscriptTime > speakerSwitchInterval) {
                        currentSpeaker = currentSpeaker === 'patient' ? 'therapist' : 'patient';
                        lastTranscriptTime = currentTime;
                    }

                    // Add to transcription
                    addRealChatMessage(currentSpeaker, finalTranscript.trim(), timeString);

                    // Store in transcription data
                    transcriptionData.segments.push({
                        speaker: currentSpeaker,
                        text: finalTranscript.trim(),
                        time: currentTime,
                        timeString: timeString
                    });

                    transcriptionData.fullText += finalTranscript + ' ';

                    // Analyze content for insights
                    analyzeTranscriptContent(finalTranscript, currentSpeaker);

                    // Update progress
                    updateTranscriptionProgress(audioPlayer);
                }
            };

            recognition.onerror = function(event) {
                console.error('Speech recognition error:', event.error);
                if (event.error === 'no-speech') {
                    // Continue listening
                    if (isRecognizing && !audioPlayer.paused) {
                        setTimeout(() => {
                            if (!audioPlayer.paused) recognition.start();
                        }, 1000);
                    }
                }
            };

            recognition.onend = function() {
                isRecognizing = false;
                if (!audioPlayer.paused && audioPlayer.currentTime < audioPlayer.duration) {
                    // Restart recognition if audio is still playing
                    setTimeout(() => {
                        if (!audioPlayer.paused) recognition.start();
                    }, 100);
                } else {
                    // Transcription complete
                    completeTranscription();
                }
            };

            // Start recognition
            recognition.start();

            // Stop recognition when audio ends
            audioPlayer.addEventListener('ended', () => {
                recognition.stop();
                completeTranscription();
            });

            audioPlayer.addEventListener('pause', () => {
                recognition.stop();
            });

            audioPlayer.addEventListener('play', () => {
                if (!isRecognizing) {
                    recognition.start();
                }
            });
        }

        // Monitor audio levels for visual feedback
        function monitorAudioLevels(analyser, dataArray) {
            function updateLevels() {
                analyser.getByteFrequencyData(dataArray);

                // Calculate average volume
                let sum = 0;
                for (let i = 0; i < dataArray.length; i++) {
                    sum += dataArray[i];
                }
                const average = sum / dataArray.length;

                // Update visual indicator based on audio level
                updateAudioLevelIndicator(average);

                requestAnimationFrame(updateLevels);
            }
            updateLevels();
        }

        // Update audio level visual indicator
        function updateAudioLevelIndicator(level) {
            const analysisStatus = document.getElementById('analysisStatus');
            if (analysisStatus && level > 10) {
                analysisStatus.innerHTML = `
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"></div>
                    <span class="text-green-600">Audio gedetecteerd - transcriberen...</span>
                `;
            }
        }

        // Add real chat message (different from mock version)
        function addRealChatMessage(speaker, text, time) {
            const transcriptionChat = document.getElementById('transcriptionChat');
            if (!transcriptionChat) return;

            const isPatient = speaker === 'patient';

            const messageDiv = document.createElement('div');
            messageDiv.className = `flex ${isPatient ? 'justify-start' : 'justify-end'} mb-4 animate-fade-in`;

            messageDiv.innerHTML = `
                <div class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${isPatient ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-900 dark:text-blue-200' : 'bg-green-100 dark:bg-green-900/30 text-green-900 dark:text-green-200'}">
                    <div class="flex items-center mb-1">
                        <i class="fas ${isPatient ? 'fa-user' : 'fa-user-md'} text-xs mr-2"></i>
                        <span class="text-xs font-medium">${isPatient ? 'Patiënt' : 'Therapeut'}</span>
                        <span class="text-xs text-gray-500 dark:text-gray-400 ml-auto">${time}</span>
                    </div>
                    <p class="text-sm">${text}</p>
                    <div class="text-xs text-gray-400 mt-1">
                        <i class="fas fa-microphone text-xs mr-1"></i>
                        Live transcriptie
                    </div>
                </div>
            `;

            transcriptionChat.appendChild(messageDiv);
            transcriptionChat.scrollTop = transcriptionChat.scrollHeight;
        }

        // Analyze transcript content for insights
        function analyzeTranscriptContent(text, speaker) {
            const lowerText = text.toLowerCase();

            // Medical keywords detection
            const medicalKeywords = {
                'duizelig': { type: 'dizziness', title: 'Duizeligheid', severity: 'warning' },
                'dizzy': { type: 'dizziness', title: 'Duizeligheid', severity: 'warning' },
                'medicijn': { type: 'medication', title: 'Medicatie Genoemd', severity: 'info' },
                'medication': { type: 'medication', title: 'Medicatie Genoemd', severity: 'info' },
                'pijn': { type: 'pain', title: 'Pijnklachten', severity: 'warning' },
                'pain': { type: 'pain', title: 'Pijnklachten', severity: 'warning' },
                'angst': { type: 'anxiety', title: 'Angst & Stress', severity: 'danger' },
                'anxiety': { type: 'anxiety', title: 'Angst & Stress', severity: 'danger' },
                'slapen': { type: 'sleep', title: 'Slaapproblemen', severity: 'warning' },
                'sleep': { type: 'sleep', title: 'Slaapproblemen', severity: 'warning' },
                'ademhaling': { type: 'breathing', title: 'Ademhalingstechnieken', severity: 'success' },
                'breathing': { type: 'breathing', title: 'Ademhalingstechnieken', severity: 'success' }
            };

            // Check for keywords and add insights
            for (const [keyword, insight] of Object.entries(medicalKeywords)) {
                if (lowerText.includes(keyword)) {
                    // Check if this insight was already added
                    if (!transcriptionData.insights.some(i => i.type === insight.type)) {
                        addInsightCard(
                            insight.type,
                            insight.title,
                            `Gedetecteerd in ${speaker === 'patient' ? 'patiënt' : 'therapeut'} spraak: "${text.substring(0, 50)}..."`,
                            insight.severity
                        );

                        transcriptionData.insights.push({
                            type: insight.type,
                            title: insight.title,
                            text: text,
                            speaker: speaker,
                            timestamp: new Date().toISOString()
                        });
                    }
                }
            }
        }

        // Update transcription progress
        function updateTranscriptionProgress(audioPlayer) {
            const progressBar = document.getElementById('progressBar');
            const progressPercentage = document.getElementById('progressPercentage');

            if (progressBar && progressPercentage && audioPlayer.duration) {
                const progress = (audioPlayer.currentTime / audioPlayer.duration) * 100;
                progressBar.style.width = progress + '%';
                progressPercentage.textContent = Math.round(progress) + '%';
            }
        }

        // Update analysis status
        function updateAnalysisStatus(message, type = 'info') {
            const analysisStatus = document.getElementById('analysisStatus');
            if (analysisStatus) {
                const colors = {
                    'info': 'text-blue-600',
                    'success': 'text-green-600',
                    'warning': 'text-yellow-600',
                    'error': 'text-red-600'
                };

                analysisStatus.innerHTML = `
                    <div class="w-2 h-2 bg-${type === 'info' ? 'blue' : type === 'success' ? 'green' : type === 'warning' ? 'yellow' : 'red'}-400 rounded-full animate-pulse mr-2"></div>
                    <span class="${colors[type]}">${message}</span>
                `;
            }
        }

        // Complete transcription
        function completeTranscription() {
            updateAnalysisStatus('Transcriptie voltooid', 'success');

            // Show summary section
            const summarySection = document.getElementById('summarySection');
            if (summarySection) summarySection.classList.remove('hidden');

            // Add final summary insights
            setTimeout(() => {
                if (transcriptionData.segments.length > 0) {
                    addInsightCard(
                        'summary',
                        'Transcriptie Samenvatting',
                        `${transcriptionData.segments.length} spraaksegmenten gedetecteerd. Totaal ${transcriptionData.fullText.split(' ').length} woorden getranscribeerd.`,
                        'success'
                    );
                }
            }, 1000);
        }

        // Fallback for browsers without speech recognition
        function startAudioAnalysisWithoutSpeech() {
            updateAnalysisStatus('Speech Recognition niet ondersteund - audio analyse actief', 'warning');

            const audioPlayer = document.getElementById('audioPlayer');
            if (!audioPlayer) {
                console.error('Audio player not found');
                return;
            }

            console.log('Starting audio analysis without speech recognition');

            // Add a message explaining the limitation
            addRealChatMessage('patient', 'Web Speech API niet ondersteund in deze browser. Audio wordt afgespeeld maar transcriptie is beperkt.', '00:00');

            // Simulate transcription based on audio playback
            audioPlayer.addEventListener('timeupdate', () => {
                updateTranscriptionProgress(audioPlayer);

                // Add sample transcription at specific intervals
                const currentTime = audioPlayer.currentTime;
                if (currentTime > 5 && currentTime < 6 && !document.querySelector('[data-time="5"]')) {
                    addRealChatMessage('patient', 'Voor volledige transcriptie, gebruik Chrome of Edge browser', formatTime(currentTime));
                    const lastMessage = document.querySelector('#transcriptionChat > div:last-child');
                    if (lastMessage) lastMessage.setAttribute('data-time', '5');
                }

                if (currentTime > 15 && currentTime < 16 && !document.querySelector('[data-time="15"]')) {
                    addRealChatMessage('therapist', 'Demo: In productie wordt OpenAI Whisper gebruikt voor transcriptie', formatTime(currentTime));
                    const lastMessage = document.querySelector('#transcriptionChat > div:last-child');
                    if (lastMessage) lastMessage.setAttribute('data-time', '15');
                }
            });

            audioPlayer.addEventListener('ended', () => {
                completeTranscription();
            });

            // Start playback
            audioPlayer.play().catch(error => {
                console.error('Error playing audio:', error);
                updateAnalysisStatus('Fout bij afspelen audio', 'error');
            });
        }

        // Add event listener for summary button
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listener when DOM is ready
            setTimeout(() => {
                const summaryBtn = document.getElementById('generateSummaryBtn');
                if (summaryBtn) {
                    summaryBtn.addEventListener('click', generateSummary);
                }
            }, 1000);
        });

        // Function to close the recording modal
        function closeRecordingModal() {
            console.log('Closing recording modal');
            const recordingModal = document.getElementById('recordingModal');
            if (recordingModal) {
                const modalContent = recordingModal.querySelector('.modal-content');
                if (modalContent) {
                    modalContent.classList.remove('scale-100', 'opacity-100');
                    modalContent.classList.add('scale-95', 'opacity-0');
                }
                
                setTimeout(() => {
                    recordingModal.classList.add('hidden');
                }, 300);
            }
        }
        
        // Progressive transcription functions
        function showTranscriptionDivs() {
            // Create transcription container if it doesn't exist
            let transcriptionContainer = document.querySelector('.transcription-container');
            if (!transcriptionContainer) {
                transcriptionContainer = document.createElement('div');
                transcriptionContainer.className = 'transcription-container mt-6 bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 relative overflow-hidden';
                
                const modalContent = document.querySelector('#recordingModal .modal-content');
                if (modalContent) {
                    // Insert before the buttons div
                    const buttonsDiv = modalContent.querySelector('div.flex.justify-between');
                    modalContent.insertBefore(transcriptionContainer, buttonsDiv);
                }
            }
            
            transcriptionContainer.innerHTML = `
                <h3 class="text-lg font-semibold text-white mb-4">
                    <i class="fas fa-microphone-alt text-blue-400 mr-2"></i>
                    Real-time AI Transcription
                </h3>
                <div class="grid grid-cols-1 gap-4">
                    <div class="patient-transcription bg-blue-900/30 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-blue-500/30 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user text-blue-400 text-sm"></i>
                            </div>
                            <div class="text-sm font-medium text-blue-300">Patient</div>
                        </div>
                        <div id="patient-text" class="text-white min-h-[40px]"></div>
                    </div>
                    <div class="therapist-transcription bg-green-900/30 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <div class="w-8 h-8 bg-green-500/30 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user-md text-green-400 text-sm"></i>
                            </div>
                            <div class="text-sm font-medium text-green-300">Therapist</div>
                        </div>
                        <div id="therapist-text" class="text-white min-h-[40px]"></div>
                    </div>
                </div>
                
                <div class="mt-3 text-center">
                    <div class="text-xs text-gray-400">
                        <i class="fas fa-info-circle mr-1"></i>
                        Waiting for conversation to complete before transcription
                    </div>
                </div>
            `;
            
            // Start the transcription animation
            simulateProgressiveTranscription();
        }
        
        function simulateProgressiveTranscription() {
            // Create actual audio element for playback
            createRealAudioPlayer();
            
            // Clear any existing content in transcript areas
            document.getElementById('patient-text').textContent = '';
            document.getElementById('therapist-text').textContent = '';
            
            // Add the listening indicators
            showAudioPlaybackControls();
        }
        
        function createRealAudioPlayer() {
            // Add a proper audio element to the page
            const transcriptionContainer = document.querySelector('.transcription-container');
            if (!transcriptionContainer) return;
            
            // Create audio container with controls
            const audioContainer = document.createElement('div');
            audioContainer.className = 'audio-container mt-2 mb-4 text-center';
            audioContainer.innerHTML = `
                <div class="p-3 bg-gray-800/60 backdrop-blur-sm rounded-lg">
                    <audio id="conversation-audio" class="w-full" controls>
                        <source src="24tbynn2uy.mp3" type="audio/mp3">
                        Your browser does not support the audio element.
                    </audio>
                    <div class="mt-2 text-xs text-gray-400">
                        <i class="fas fa-info-circle mr-1"></i>
                        <span>Play the audio to see synchronized transcription</span>
                    </div>
                    <div class="mt-2 hidden audio-status px-3 py-1 bg-blue-900/30 rounded-full text-blue-400 text-xs inline-block">
                        <i class="fas fa-headphones mr-1"></i>
                        <span>Audio playing - listen for transcription</span>
                    </div>
                </div>
            `;
            
            transcriptionContainer.insertBefore(audioContainer, transcriptionContainer.firstChild.nextSibling);
            
            // Set up event listeners for the audio element
            setupAudioTimeEvents();
        }
        
        function setupAudioTimeEvents() {
            const audioElement = document.getElementById('conversation-audio');
            if (!audioElement) return;
            
            // Create an object to hold the transcript segments with their timestamps
            // Improved timing synchronization for more accurate display with audio
            const transcriptSegments = [
                {
                    speaker: 'patient', 
                    startTime: 3.0, // Adjusted to appear when actually spoken
                    endTime: 4.5, 
                    text: "I've been taking Coveram for my blood pressure,"
                },
                {
                    speaker: 'patient',
                    startTime: 5.0,
                    endTime: 6.5, 
                    text: " but lately I've been feeling dizzy."
                },
                {
                    speaker: 'patient',
                    startTime: 7.5,
                    endTime: 9.0,
                    text: "It's like the room starts spinning"
                },
                {
                    speaker: 'patient',
                    startTime: 9.5,
                    endTime: 12.0,
                    text: " and I'm worried I might fall. It's really making me anxious."
                },
                {
                    speaker: 'patient',
                    startTime: 13.0,
                    endTime: 15.0,
                    text: "That breathing technique you suggested last time does help a bit"
                },
                {
                    speaker: 'patient',
                    startTime: 15.5,
                    endTime: 18.0,
                    text: " when it happens, but I'm still concerned."
                },
                {
                    speaker: 'therapist',
                    startTime: 19.0,
                    endTime: 22.5,
                    text: "I understand your concern about the dizziness. It's good to hear the breathing technique is helping somewhat."
                },
                {
                    speaker: 'therapist',
                    startTime: 23.0,
                    endTime: 29.0,
                    text: "How often are you experiencing these dizzy spells? And have you noticed any pattern to when they occur?"
                }
            ];
            
            // Show audio status when playing
            audioElement.addEventListener('play', function() {
                const audioStatus = document.querySelector('.audio-status');
                if (audioStatus) audioStatus.classList.remove('hidden');
                
                // Hide any processing indicators
                removeProcessingIndicator('patient-text');
                removeProcessingIndicator('therapist-text');
            });
            
            // Hide audio status when paused
            audioElement.addEventListener('pause', function() {
                const audioStatus = document.querySelector('.audio-status');
                if (audioStatus) audioStatus.classList.add('hidden');
            });
            
            // Set up timeupdate event to check for transcript segments
            audioElement.addEventListener('timeupdate', function() {
                const currentTime = audioElement.currentTime;
                
                // Check each segment to see if it should be displayed
                transcriptSegments.forEach(segment => {
                    const elementId = segment.speaker + '-text';
                    const element = document.getElementById(elementId);
                    
                    // Only display segment when current time is between start and end time
                    // This ensures text doesn't appear before it's spoken
                    if (currentTime >= segment.startTime && currentTime <= segment.endTime && 
                        !element.getAttribute('data-segment-' + segment.endTime)) {
                        
                        // Show processing indicator briefly
                        showProcessingIndicator(elementId);
                        
                        // After a short delay, show the transcription
                        setTimeout(() => {
                            removeProcessingIndicator(elementId);
                            
                            // Get current text and add new segment
                            const currentText = element.textContent;
                            const newText = currentText ? currentText + segment.text : segment.text;
                            
                            // Type out the new text
                            typeOutText(elementId, newText, currentText.length, 20);
                            
                            // Mark this segment as displayed
                            element.setAttribute('data-segment-' + segment.endTime, 'true');
                            
                            // Log to console to help debugging
                            console.log(`Transcribed at ${currentTime}s:`, segment.text);
                        }, 100); // Shorter delay for more real-time feeling
                    }
                });
            });
            
            // When audio ends, start analysis automatically
            audioElement.addEventListener('ended', function() {
                // Update status
                const infoDiv = document.querySelector('.transcription-container .text-xs.text-gray-400');
                if (infoDiv) {
                    infoDiv.innerHTML = `
                        <i class="fas fa-check-circle mr-1"></i>
                        <span class="text-green-400 font-medium">Transcription complete!</span> Starting AI analysis...
                    `;
                }
                
                // Start analysis immediately
                showConversationalAIAnalysis();
            });
        }
        
        function showAudioPlaybackControls() {
            // Update the status message
            const infoDiv = document.querySelector('.transcription-container .text-xs.text-gray-400');
            if (infoDiv) {
                infoDiv.innerHTML = `
                    <i class="fas fa-headphones mr-1"></i>
                    Play the audio to see the transcription appear as it's spoken
                `;
            }
            
            // Add listening indicators for patient and therapist
            showListeningIndicator('patient-text');
            showListeningIndicator('therapist-text');
        }
        
        function startListeningWithRealAudio() {
            // Clear any existing content
            document.getElementById('patient-text').textContent = '';
            document.getElementById('therapist-text').textContent = '';
            
            // Show instructions about the process
            const infoDiv = document.querySelector('.transcription-container .text-xs.text-gray-400');
            if (infoDiv) {
                infoDiv.innerHTML = `
                    <i class="fas fa-info-circle mr-1"></i>
                    AI is listening to the audio in real-time
                `;
            }
            
            // Simulate real audio by playing/speaking first, then transcribing
            simulateRealAudioPlayback();
        }
        
        function simulateRealAudioPlayback() {
            // Show the listening indicator for patient
            showListeningIndicator('patient-text');
            
            // Animate patient speaking
            animatePatientSpeaking();
            
            // Simulate real audio playing chunks with realistic timing
            const patientFirstSegmentDuration = 5000;  // First part of patient's speech
            const patientSecondSegmentDuration = 6000; // Second part of patient's speech
            const patientThirdSegmentDuration = 4000;  // Third part of patient's speech
            
            // Patient says the first segment
            setTimeout(() => {
                // First segment is complete, begin transcription after a short processing delay
                showProcessingIndicator('patient-text');
                
                setTimeout(() => {
                    // Start showing the first segment of transcription
                    removeProcessingIndicator('patient-text');
                    typeOutText('patient-text', "I've been feeling very anxious lately,", 0, 30);
                    
                    // Patient continues speaking the second segment
                    setTimeout(() => {
                        showProcessingIndicator('patient-text');
                        
                        setTimeout(() => {
                            // Add second segment to transcription
                            const currentText = document.getElementById('patient-text').textContent;
                            removeProcessingIndicator('patient-text');
                            typeOutText('patient-text', currentText + " especially when I have to attend social events.", currentText.length, 30);
                            
                            // Patient speaks third segment
                            setTimeout(() => {
                                showProcessingIndicator('patient-text');
                                
                                setTimeout(() => {
                                    // Add third segment to transcription
                                    const currentText = document.getElementById('patient-text').textContent;
                                    removeProcessingIndicator('patient-text');
                                    typeOutText('patient-text', currentText + " It's like my heart starts racing and I can't focus on the conversations around me.", currentText.length, 30, () => {
                                        // Patient has finished speaking
                                        stopPatientSpeaking();
                                        
                                        // After a brief pause, therapist begins
                                        setTimeout(() => {
                                            startTherapistRealAudioPlayback();
                                        }, 2000);
                                    });
                                }, 1000); // Processing delay
                            }, patientThirdSegmentDuration);
                        }, 1000); // Processing delay
                    }, patientSecondSegmentDuration);
                }, 1000); // Processing delay
            }, patientFirstSegmentDuration);
        }
        
        function startTherapistRealAudioPlayback() {
            // Show the listening indicator for therapist
            showListeningIndicator('therapist-text');
            
            // Animate therapist speaking
            animateTherapistSpeaking();
            
            // Simulate therapist speaking
            const therapistFirstSegmentDuration = 3000;
            const therapistSecondSegmentDuration = 7000;
            
            // Therapist speaks first segment
            setTimeout(() => {
                showProcessingIndicator('therapist-text');
                
                setTimeout(() => {
                    // Show first part of therapist's response
                    removeProcessingIndicator('therapist-text');
                    typeOutText('therapist-text', "I understand that must be difficult.", 0, 30);
                    
                    // Therapist speaks second segment
                    setTimeout(() => {
                        showProcessingIndicator('therapist-text');
                        
                        setTimeout(() => {
                            // Add second part of therapist's response
                            const currentText = document.getElementById('therapist-text').textContent;
                            removeProcessingIndicator('therapist-text');
                            typeOutText('therapist-text', currentText + " Can you tell me more about when you first noticed these feelings of anxiety in social situations?", currentText.length, 30, () => {
                                // Therapist has finished speaking
                                stopTherapistSpeaking();
                                
                                // Update the status message
                                const infoDiv = document.querySelector('.transcription-container .text-xs.text-gray-400');
                                if (infoDiv) {
                                    infoDiv.innerHTML = `
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Transcription complete - Ready for analysis
                                    `;
                                }
                                
                                // Show AI Analysis button
                                setTimeout(() => {
                                    showAIAnalysisButton();
                                }, 1000);
                            });
                        }, 1000); // Processing delay
                    }, therapistSecondSegmentDuration);
                }, 1000); // Processing delay
            }, therapistFirstSegmentDuration);
        }
        
        // Functions to show "AI is listening" and "AI is processing" indicators
        function showListeningIndicator(elementId) {
            const element = document.getElementById(elementId);
            if (!element) return;
            
            const listeningIndicator = document.createElement('div');
            listeningIndicator.className = 'listening-indicator flex items-center text-blue-400 text-sm mb-2';
            listeningIndicator.innerHTML = `
                <div class="mr-2 relative">
                    <div class="w-4 h-4 rounded-full bg-blue-400/20 animate-pulse"></div>
                    <div class="w-2 h-2 rounded-full bg-blue-400 absolute top-1 left-1"></div>
                </div>
                <span class="animate-pulse">AI Listening...</span>
            `;
            
            element.appendChild(listeningIndicator);
        }
        
        function showProcessingIndicator(elementId) {
            const element = document.getElementById(elementId);
            if (!element) return;
            
            // Remove listening indicator if it exists
            const listeningIndicator = element.querySelector('.listening-indicator');
            if (listeningIndicator) {
                element.removeChild(listeningIndicator);
            }
            
            // Add processing indicator
            const processingIndicator = document.createElement('div');
            processingIndicator.className = 'processing-indicator flex items-center text-green-400 text-sm mb-2';
            processingIndicator.innerHTML = `
                <div class="mr-2">
                    <i class="fas fa-cog fa-spin"></i>
                </div>
                <span>Processing transcription...</span>
            `;
            
            element.appendChild(processingIndicator);
        }
        
        function removeProcessingIndicator(elementId) {
            const element = document.getElementById(elementId);
            if (!element) return;
            
            const processingIndicator = element.querySelector('.processing-indicator');
            if (processingIndicator) {
                element.removeChild(processingIndicator);
            }
        }
        
        function showAIAnalysisButton() {
            // Create and add the "Analyze with AI" button
            const transcriptionContainer = document.querySelector('.transcription-container');
            if (!transcriptionContainer) return;
            
            const analysisButtonContainer = document.createElement('div');
            analysisButtonContainer.className = 'mt-6 text-center';
            analysisButtonContainer.innerHTML = `
                <button id="analyzeWithAIButton" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow-md transition-all flex items-center mx-auto">
                    <i class="fas fa-brain mr-2"></i>
                    Generate AI Analysis
                </button>
            `;
            
            transcriptionContainer.appendChild(analysisButtonContainer);
            
            // Add event listener to the button
            const analyzeButton = document.getElementById('analyzeWithAIButton');
            if (analyzeButton) {
                analyzeButton.addEventListener('click', () => {
                    analyzeButton.disabled = true;
                    analyzeButton.innerHTML = `
                        <i class="fas fa-spinner fa-spin mr-2"></i>
                        Connecting to AI Service...
                    `;
                    
                    // Simulate connecting to AI service
                    setTimeout(() => {
                        showConversationalAIAnalysis();
                    }, 2000);
                });
            }
        }
        
        function typeOutText(elementId, text, index, speed, callback) {
            const element = document.getElementById(elementId);
            if (!element) return;
            
            // If we've typed the entire text, stop and call callback if provided
            if (index >= text.length) {
                if (typeof callback === 'function') {
                    callback();
                }
                return;
            }
            
            // Add the next character
            element.textContent = text.substring(0, index + 1);
            
            // Schedule the next character with slight randomness in timing to mimic natural typing
            const randomDelay = Math.random() * 50;
            setTimeout(() => {
                typeOutText(elementId, text, index + 1, speed, callback);
            }, speed + randomDelay);
        }

        // Function to show AI analysis process with loading animation
        function showConversationalAIAnalysis() {
            // Create analysis container if it doesn't exist
            let analysisContainer = document.querySelector('.ai-analysis-container');
            if (!analysisContainer) {
                analysisContainer = document.createElement('div');
                analysisContainer.className = 'ai-analysis-container mt-8 bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 relative overflow-hidden';
                
                const transcriptionContainer = document.querySelector('.transcription-container');
                if (transcriptionContainer) {
                    // Add after transcription container
                    transcriptionContainer.parentNode.insertBefore(analysisContainer, transcriptionContainer.nextSibling);
                }
            }
            
            // Set up the conversational AI interface
            analysisContainer.innerHTML = `
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">
                        <i class="fas fa-brain text-blue-400 mr-2"></i> 
                        OpenAI Analysis
                    </h3>
                    <div class="px-3 py-1 rounded-full bg-blue-900/30 text-blue-400 text-xs flex items-center">
                        <i class="fas fa-robot mr-2"></i>
                        <span>OpenAI API</span>
                    </div>
                </div>
                
                <!-- AI Chat Interface -->
                <div class="bg-gray-900/70 rounded-lg overflow-hidden">
                    <!-- Thinking animation initially -->
                    <div class="ai-thinking p-5 border-b border-gray-800">
                        <div class="flex items-start">
                            <div class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0 mr-3">
                                <i class="fas fa-robot text-white"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <span class="text-blue-400 text-sm font-medium">AI Assistant</span>
                                    <span class="ml-2 px-2 py-0.5 rounded-full bg-blue-900/30 text-blue-400 text-xs">Analyzing</span>
                                </div>
                                <div class="thinking-dots flex space-x-1">
                                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 0.3s"></div>
                                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 0.6s"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- AI conversation will be added here -->
                    <div id="ai-conversation-container" class="py-2">
                        <!-- Messages will be added here dynamically -->
                    </div>
                </div>
                
                <!-- Interactive question input -->
                <div class="mt-4 flex items-center rounded-lg bg-gray-900/50 overflow-hidden">
                    <input type="text" id="user-question" placeholder="Ask a follow-up question..." class="w-full bg-transparent p-3 text-white focus:outline-none" disabled>
                    <button id="send-question" class="bg-blue-600 text-white px-4 py-3 disabled:bg-gray-700 disabled:text-gray-400" disabled>
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            `;
            
            // Start the OpenAI analysis
            setTimeout(() => {
                performOpenAIAnalysis();
            }, 1500);
        }
        
        function performOpenAIAnalysis() {
            const conversationContainer = document.getElementById('ai-conversation-container');
            const thinkingElement = document.querySelector('.ai-thinking');
            
            // Remove thinking animation
            if (thinkingElement) {
                thinkingElement.remove();
            }
            
            // Function to add AI message
            function addAIMessage(message, delay = 0) {
                return new Promise(resolve => {
                    setTimeout(() => {
                        const messageElement = document.createElement('div');
                        messageElement.className = 'ai-message p-5 border-b border-gray-800';
                        messageElement.innerHTML = `
                            <div class="flex items-start">
                                <div class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0 mr-3">
                                    <i class="fas fa-robot text-white"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <span class="text-blue-400 text-sm font-medium">AI Assistant</span>
                                    </div>
                                    <div class="text-white message-content"></div>
                                </div>
                            </div>
                        `;
                        
                        conversationContainer.appendChild(messageElement);
                        
                        // Type out the message content
                        const contentElement = messageElement.querySelector('.message-content');
                        typeOutAnalysisText(contentElement, message, 0, 20, resolve);
                        
                        // Scroll to bottom
                        conversationContainer.scrollTop = conversationContainer.scrollHeight;
                    }, delay);
                });
            }
            
            // Add interactive insight cards after initial analysis
            function addInsightCard(type, icon, title, content) {
                return new Promise(resolve => {
                    const cardElement = document.createElement('div');
                    cardElement.className = 'insight-card p-5 mx-5 my-3 bg-gray-800/50 rounded-lg transform transition-all cursor-pointer hover:bg-gray-800';
                    
                    // Set color based on type
                    let colorClass = '';
                    switch(type) {
                        case 'medication':
                            colorClass = 'text-purple-400';
                            break;
                        case 'symptoms':
                            colorClass = 'text-red-400';
                            break;
                        case 'treatment':
                            colorClass = 'text-green-400';
                            break;
                        case 'concerns':
                            colorClass = 'text-yellow-400';
                            break;
                        default:
                            colorClass = 'text-blue-400';
                    }
                    
                    cardElement.innerHTML = `
                        <div class="flex items-start">
                            <div class="w-8 h-8 rounded-full bg-${type == 'medication' ? 'purple' : type == 'symptoms' ? 'red' : type == 'treatment' ? 'green' : type == 'concerns' ? 'yellow' : 'blue'}-900/30 flex items-center justify-center mr-3 flex-shrink-0">
                                <i class="fas fa-${icon} ${colorClass}"></i>
                            </div>
                            <div class="flex-1">
                                <h4 class="text-white font-medium mb-1">${title}</h4>
                                <p class="text-gray-400 text-sm card-preview">${content}</p>
                                <div class="card-details hidden mt-3 pt-3 border-t border-gray-700">
                                    <!-- Details will be added dynamically -->
                                </div>
                                <div class="mt-2 text-${type == 'medication' ? 'purple' : type == 'symptoms' ? 'red' : type == 'treatment' ? 'green' : type == 'concerns' ? 'yellow' : 'blue'}-400 text-xs cursor-pointer card-toggle">
                                    <span class="flex items-center">
                                        <span>View details</span>
                                        <i class="fas fa-chevron-down ml-1"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    conversationContainer.appendChild(cardElement);
                    
                    // Add toggle event
                    const toggle = cardElement.querySelector('.card-toggle');
                    const details = cardElement.querySelector('.card-details');
                    const preview = cardElement.querySelector('.card-preview');
                    
                    toggle.addEventListener('click', (e) => {
                        e.stopPropagation();
                        details.classList.toggle('hidden');
                        toggle.querySelector('i').classList.toggle('fa-chevron-down');
                        toggle.querySelector('i').classList.toggle('fa-chevron-up');
                        
                        if (!details.classList.contains('hidden')) {
                            // Only populate details the first time they're opened
                            if (details.innerHTML.trim() === '') {
                                // Add different content based on card type
                                if (type === 'medication') {
                                    details.innerHTML = `
                                        <div class="space-y-3">
                                            <div class="bg-gray-700/70 p-2 rounded">
                                                <h5 class="text-purple-400 text-xs font-medium mb-1">Coveram Information</h5>
                                                <p class="text-gray-400 text-xs">Combination medication containing perindopril and amlodipine, primarily used for hypertension (high blood pressure).</p>
                                            </div>
                                            <div class="bg-gray-700/70 p-2 rounded">
                                                <h5 class="text-purple-400 text-xs font-medium mb-1">Side Effects</h5>
                                                <p class="text-gray-400 text-xs">Dizziness is a known side effect of Coveram and may require dosage adjustment or medication change.</p>
                                            </div>
                                            <div class="bg-gray-700/70 p-2 rounded">
                                                <h5 class="text-purple-400 text-xs font-medium mb-1">Recommendation</h5>
                                                <p class="text-gray-400 text-xs">Patient should consult with their prescribing physician about the dizziness symptoms as they may be related to the medication.</p>
                                            </div>
                                        </div>
                                    `;
                                } else if (type === 'symptoms') {
                                    details.innerHTML = `
                                        <div class="space-y-3">
                                            <div class="flex justify-between items-center text-xs">
                                                <span class="text-gray-400">Severity Assessment</span>
                                                <span class="text-yellow-400 font-medium">Moderate to Severe</span>
                                            </div>
                                            <div class="h-2 w-full bg-gray-700 rounded-full overflow-hidden">
                                                <div class="h-full bg-yellow-500 rounded-full" style="width: 75%"></div>
                                            </div>
                                            <div class="bg-gray-700/70 p-2 rounded">
                                                <h5 class="text-red-400 text-xs font-medium mb-1">Primary Symptoms</h5>
                                                <ul class="text-gray-400 text-xs space-y-1">
                                                    <li>• Dizziness with sensation of room spinning</li>
                                                    <li>• Fear of falling during dizzy episodes</li>
                                                    <li>• Anxiety related to dizziness symptoms</li>
                                                </ul>
                                            </div>
                                            <div class="bg-gray-700/70 p-2 rounded">
                                                <h5 class="text-red-400 text-xs font-medium mb-1">Possible Causes</h5>
                                                <ul class="text-gray-400 text-xs space-y-1">
                                                    <li>• Medication side effect (Coveram)</li>
                                                    <li>• Blood pressure fluctuations</li>
                                                    <li>• Anxiety-related vestibular symptoms</li>
                                                </ul>
                                            </div>
                                        </div>
                                    `;
                                } else if (type === 'treatment') {
                                    details.innerHTML = `
                                        <div class="space-y-3">
                                            <div class="bg-gray-700/70 p-2 rounded">
                                                <h5 class="text-green-400 text-xs font-medium mb-1">Breathing Techniques</h5>
                                                <p class="text-gray-400 text-xs">Patient reports that breathing techniques are effective in helping manage symptoms. This is a positive sign and suggests continuing and expanding this approach.</p>
                                            </div>
                                            <div class="bg-gray-700/70 p-2 rounded">
                                                <h5 class="text-green-400 text-xs font-medium mb-1">Recommended Enhancement</h5>
                                                <p class="text-gray-400 text-xs">Consider adding grounding exercises that specifically target dizziness sensations when they occur. This can complement the breathing techniques.</p>
                                            </div>
                                            <div class="bg-gray-700/70 p-2 rounded">
                                                <h5 class="text-green-400 text-xs font-medium mb-1">Frequency & Duration</h5>
                                                <p class="text-gray-400 text-xs">Suggest patient practice breathing techniques preventatively (twice daily) in addition to during dizziness episodes.</p>
                                            </div>
                                        </div>
                                    `;
                                } else if (type === 'concerns') {
                                    details.innerHTML = `
                                        <div class="space-y-2">
                                            <div class="bg-gray-700/70 p-2 rounded">
                                                <h5 class="text-yellow-400 text-xs font-medium mb-1">Primary Concern</h5>
                                                <p class="text-gray-400 text-xs">Patient is worried about falling during dizzy spells, which is increasing overall anxiety and creating a feedback loop of symptoms.</p>
                                            </div>
                                            <div class="bg-gray-700/70 p-2 rounded">
                                                <h5 class="text-yellow-400 text-xs font-medium mb-1">Secondary Concerns</h5>
                                                <ul class="text-gray-400 text-xs space-y-1">
                                                    <li>• Uncertainty about cause of dizziness</li>
                                                    <li>• Worry about potential serious health conditions</li>
                                                    <li>• Impact on daily functioning</li>
                                                </ul>
                                            </div>
                                            <div class="bg-gray-700/70 p-2 rounded">
                                                <h5 class="text-yellow-400 text-xs font-medium mb-1">Addressing Concerns</h5>
                                                <p class="text-gray-400 text-xs">Recommend medical evaluation to rule out serious conditions, which will help reduce health-related anxiety.</p>
                                            </div>
                                        </div>
                                    `;
                                }
                            }
                        }
                        
                        // Scroll to the card
                        cardElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    });
                    
                    // Scroll to bottom
                    conversationContainer.scrollTop = conversationContainer.scrollHeight;
                    resolve();
                });
            }
            
            // Add user input handling after analysis
            function enableUserQuestions() {
                const userInput = document.getElementById('user-question');
                const sendButton = document.getElementById('send-question');
                
                // Enable input
                userInput.disabled = false;
                sendButton.disabled = false;
                
                // Add placeholder prompt
                const promptElement = document.createElement('div');
                promptElement.className = 'text-center py-3 text-gray-400 text-sm';
                promptElement.textContent = 'Ask a follow-up question about the analysis';
                conversationContainer.appendChild(promptElement);
                
                // Handle sending questions
                sendButton.addEventListener('click', () => {
                    const question = userInput.value.trim();
                    if (!question) return;
                    
                    // Add user message
                    const messageElement = document.createElement('div');
                    messageElement.className = 'user-message p-5 border-b border-gray-800';
                    messageElement.innerHTML = `
                        <div class="flex items-start justify-end">
                            <div class="flex-1 text-right">
                                <div class="inline-block bg-blue-600 text-white px-4 py-2 rounded-lg max-w-[80%]">
                                    ${question}
                                </div>
                            </div>
                            <div class="w-8 h-8 rounded-full bg-blue-600/20 flex items-center justify-center flex-shrink-0 ml-3">
                                <i class="fas fa-user text-blue-400"></i>
                            </div>
                        </div>
                    `;
                    
                    conversationContainer.appendChild(messageElement);
                    userInput.value = '';
                    
                    // Temporary disable while "thinking"
                    userInput.disabled = true;
                    sendButton.disabled = true;
                    
                    // Add thinking animation
                    const thinkingElement = document.createElement('div');
                    thinkingElement.className = 'ai-thinking p-5 border-b border-gray-800';
                    thinkingElement.innerHTML = `
                        <div class="flex items-start">
                            <div class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center flex-shrink-0 mr-3">
                                <i class="fas fa-robot text-white"></i>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center mb-2">
                                    <span class="text-blue-400 text-sm font-medium">AI Assistant</span>
                                    <span class="ml-2 px-2 py-0.5 rounded-full bg-blue-900/30 text-blue-400 text-xs">Thinking</span>
                                </div>
                                <div class="thinking-dots flex space-x-1">
                                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 0.3s"></div>
                                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 0.6s"></div>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    conversationContainer.appendChild(thinkingElement);
                    
                    // Scroll to bottom
                    conversationContainer.scrollTop = conversationContainer.scrollHeight;
                    
                    // Use OpenAI API for analysis instead of mock data
                    fetchOpenAIResponse(question, transcriptText).then(response => {
                        // Remove thinking after response is received
                        thinkingElement.remove();
                        
                        // Add AI response
                        addAIMessage(response).then(() => {
                            // Re-enable input
                            userInput.disabled = false;
                            sendButton.disabled = false;
                            userInput.focus();
                        });
                    }).catch(error => {
                        console.error("Error fetching OpenAI response:", error);
                        // Fallback to basic response if API call fails
                        thinkingElement.remove();
                        addAIMessage("I apologize, but I'm having trouble connecting to the analysis service. Please try again later.").then(() => {
                            userInput.disabled = false;
                            sendButton.disabled = false;
                            userInput.focus();
                        });
                    });
                });
                
                // Handle enter key
                userInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        sendButton.click();
                    }
                });
                
                // Focus input
                userInput.focus();
            }
            
            // Function to fetch response from OpenAI API
            async function fetchOpenAIResponse(question, conversationContext) {
                try {
                    // Make API call to OpenAI
                    const response = await fetch('https://api.openai.com/v1/chat/completions', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer ' + getOpenAIKey() // Function to securely retrieve the API key
                        },
                        body: JSON.stringify({
                            model: "gpt-4-turbo",
                            messages: [
                                {
                                    role: "system",
                                    content: "You are an AI medical assistant analyzing a therapy session transcript. Provide concise, professional insights. Focus on medical aspects like symptoms, medications, and treatment approaches. The patient is taking Coveram for blood pressure and experiencing dizziness as a side effect."
                                },
                                {
                                    role: "user",
                                    content: `Transcript: ${conversationContext}\n\nQuestion: ${question}`
                                }
                            ],
                            max_tokens: 400,
                            temperature: 0.7
                        })
                    });
                    
                    const data = await response.json();
                    return data.choices[0].message.content;
                } catch (error) {
                    console.error("OpenAI API error:", error);
                    // Return a fallback response if the API call fails
                    return generateFallbackResponse(question);
                }
            }
            
            // Securely retrieve OpenAI API key - in a real application, this would be handled server-side
            function getOpenAIKey() {
                // In production, API keys should NEVER be exposed in client-side code
                // This is just a placeholder - in real implementation, this would make a secure server request
                return "YOUR_OPENAI_API_KEY"; // Replace with secure key management
            }
            
            // Fallback responses if API isn't available
            function generateFallbackResponse(question) {
                if (question.toLowerCase().includes("breathing")) {
                    return "The breathing techniques are already helping the patient manage dizziness episodes, which is encouraging. I recommend expanding on these techniques by adding the 4-7-8 breathing method (inhale for 4 seconds, hold for 7, exhale for 8). This would be particularly beneficial during the onset of dizziness symptoms. The patient should be encouraged that their current practice is effective and worth continuing.";
                } else if (question.toLowerCase().includes("medication") || question.toLowerCase().includes("coveram")) {
                    return "Coveram is a combination medication containing perindopril (an ACE inhibitor) and amlodipine (a calcium channel blocker) used to treat high blood pressure. Dizziness is a known side effect of both components. The patient should consult with their prescribing physician about these symptoms, as they might need a dosage adjustment or medication change. It's important not to discontinue blood pressure medication without medical supervision.";
                } else if (question.toLowerCase().includes("dizz") || question.toLowerCase().includes("fall")) {
                    return "The patient's description of the room spinning suggests vertigo-like symptoms which could be related to their blood pressure medication. While the breathing techniques are helpful, I recommend a medical evaluation to rule out inner ear problems or other neurological causes. In the meantime, the patient should be advised to sit down immediately when feeling dizzy and avoid sudden position changes. These practical safety measures can help reduce the fear of falling.";
                } else {
                    return "Based on the transcript analysis, this patient is experiencing dizziness that may be a side effect of their blood pressure medication (Coveram). The symptom is causing anxiety, particularly fear of falling. A multi-pronged approach is recommended: 1) Medical evaluation regarding the medication; 2) Continued use of breathing techniques which the patient reports are effective; 3) Safety planning for dizzy episodes; and 4) Managing the anxiety that's developing around these symptoms. Would you like more specific recommendations for any of these components?";
                }
            }
            
            // Type out analysis text with realistic timing
            function typeOutAnalysisText(element, text, index, speed, callback) {
                if (!element) return;
                
                // If we've typed the entire text, stop and call callback if provided
                if (index >= text.length) {
                    if (typeof callback === 'function') {
                        callback();
                    }
                    return;
                }
                
                // Add the next character
                element.textContent = text.substring(0, index + 1);
                
                // Variable typing speed based on character
                let adjustedSpeed = speed;
                
                // Slow down at punctuation
                if (text[index] === '.' || text[index] === '?' || text[index] === '!') {
                    adjustedSpeed = speed * 4;
                } else if (text[index] === ',' || text[index] === ';') {
                    adjustedSpeed = speed * 3;
                }
                
                // Random variation
                const randomDelay = Math.random() * 30;
                
                // Schedule the next character
                setTimeout(() => {
                    typeOutAnalysisText(element, text, index + 1, speed, callback);
                }, adjustedSpeed + randomDelay);
            }
            
            // Begin the OpenAI analysis sequence
            async function runOpenAIAnalysis() {
                // Get transcribed text for analysis
                const patientText = document.getElementById('patient-text').textContent;
                const therapistText = document.getElementById('therapist-text').textContent;
                const transcriptText = `Patient: ${patientText}\nTherapist: ${therapistText}`;
                
                // Initial message
                await addAIMessage("Connecting to OpenAI for transcript analysis...");
                
                try {
                    // Call OpenAI API for initial analysis
                    const analysisPrompt = `
                        Analyze this therapy session transcript with focus on:
                        1. Medication mentions and potential side effects
                        2. Patient's symptoms and concerns
                        3. Effective treatment approaches mentioned
                        4. Patient's primary concerns
                        
                        Transcript:
                        ${transcriptText}
                        
                        Provide a brief initial analysis highlighting key medical insights.
                    `;
                    
                    // In a real implementation, this would use the fetchOpenAIResponse function
                    // For now, simulate the process with pre-defined responses
                    
                    // First observation about medication and symptoms
                    await addAIMessage("Based on the transcript, I notice the patient is taking Coveram for blood pressure and experiencing dizziness as a side effect. They describe the room spinning and fear of falling.", 1500);
                    
                    // Add medication insight card
                    await addInsightCard(
                        'medication', 
                        'pills', 
                        'Medication Analysis', 
                        'Patient is taking Coveram (perindopril/amlodipine) for blood pressure, which may be causing dizziness as a side effect.'
                    );
                    
                    // Add symptom analysis
                    await addAIMessage("The primary symptom is dizziness with a sensation that the room is spinning, which is causing significant concern. The patient is worried about falling during these episodes.", 1000);
                    
                    // Add symptoms insight card
                    await addInsightCard(
                        'symptoms', 
                        'dizzy', 
                        'Symptom Assessment', 
                        'Dizziness, vertigo-like symptoms (room spinning), and fear of falling during episodes. Symptoms are causing anxiety.'
                    );
                    
                    // Add treatment analysis
                    await addAIMessage("The patient mentioned that breathing techniques suggested in previous sessions have been helpful, which is a positive sign. This approach should be continued and expanded upon.", 1000);
                    
                    // Add treatment card
                    await addInsightCard(
                        'treatment', 
                        'lungs', 
                        'Effective Techniques', 
                        'Breathing techniques previously suggested are helping and should be continued and enhanced. Patient reports positive results with this approach.'
                    );
                    
                    // Add concern analysis
                    await addAIMessage("The patient's primary concern appears to be the dizziness itself and worry about falling. These physical symptoms are creating anxiety that may exacerbate the underlying condition.", 1000);
                    
                    // Add concerns card
                    await addInsightCard(
                        'concerns', 
                        'exclamation-triangle', 
                        'Patient Concerns', 
                        'Main concerns are dizziness, fear of falling, and anxiety about these symptoms. Patient is seeking reassurance about these symptoms.'
                    );
                    
                    // Final recommendations
                    await addAIMessage("I recommend: 1) Patient should consult their doctor about the Coveram medication and possible side effects; 2) Continue and enhance the breathing techniques that are already showing positive results; 3) Consider adding specific grounding techniques for dizziness episodes; 4) Provide reassurance about the connection between anxiety and dizziness symptoms.", 1500);
                    
                    // Enable user questions
                    enableUserQuestions();
                    
                } catch (error) {
                    console.error("Error in OpenAI analysis:", error);
                    await addAIMessage("I encountered an error while analyzing the transcript. Please try again later or contact support if the problem persists.");
                }
            }
            
            // Start the analysis - this will now use OpenAI integration
            runOpenAIAnalysis();
        }
        
        function animatePatientSpeaking() {
            // Activate patient waveform animation
            const patientWaveformBars = document.querySelectorAll('.patient-waveform .bg-blue-400');
            patientWaveformBars.forEach(bar => {
                // Generate random heights for a more dynamic effect
                const height = 20 + Math.random() * 80;
                bar.style.height = `${height}%`;
                bar.classList.add('animate-pulse');
                bar.style.animationDuration = `${0.5 + Math.random()}s`;
            });
            
            // Activate patient speech waves
            const patientSpeechWaves = document.querySelectorAll('.patient-speech-wave');
            patientSpeechWaves.forEach(wave => {
                wave.style.opacity = '1';
            });
        }
        
        function stopPatientSpeaking() {
            // Deactivate patient waveform animation
            const patientWaveformBars = document.querySelectorAll('.patient-waveform .bg-blue-400');
            patientWaveformBars.forEach(bar => {
                bar.style.height = '10%';
                bar.classList.remove('animate-pulse');
            });
            
            // Deactivate patient speech waves
            const patientSpeechWaves = document.querySelectorAll('.patient-speech-wave');
            patientSpeechWaves.forEach(wave => {
                wave.style.opacity = '0';
            });
        }
        
        function animateTherapistSpeaking() {
            // Activate therapist waveform animation
            const therapistWaveformBars = document.querySelectorAll('.therapist-waveform .bg-green-400');
            therapistWaveformBars.forEach(bar => {
                // Generate random heights for a more dynamic effect
                const height = 20 + Math.random() * 80;
                bar.style.height = `${height}%`;
                bar.classList.add('animate-pulse');
                bar.style.animationDuration = `${0.5 + Math.random()}s`;
            });
            
            // Activate therapist speech waves
            const therapistSpeechWaves = document.querySelectorAll('.therapist-speech-wave');
            therapistSpeechWaves.forEach(wave => {
                wave.style.opacity = '1';
            });
        }
        
        function stopTherapistSpeaking() {
            // Deactivate therapist waveform animation
            const therapistWaveformBars = document.querySelectorAll('.therapist-waveform .bg-green-400');
            therapistWaveformBars.forEach(bar => {
                bar.style.height = '10%';
                bar.classList.remove('animate-pulse');
            });
            
            // Deactivate therapist speech waves
            const therapistSpeechWaves = document.querySelectorAll('.therapist-speech-wave');
            therapistSpeechWaves.forEach(wave => {
                wave.style.opacity = '0';
            });
        }

        function closeModal(modalId) {
            const modal = document.getElementById(modalId);
            if (modal) {
                const content = modal.querySelector('.modal-content');
                if (content) {
                    content.classList.remove('scale-100', 'opacity-100');
                    content.classList.add('scale-95', 'opacity-0');
                }
                setTimeout(() => {
                    modal.classList.add('hidden');
                    
                    // Stop animations for AI recording demo modal
                    if (modalId === 'aiRecordingDemoModal') {
                        stopRecordingDemoAnimations();
                    }
                }, 300);
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            // Initialize translation system
            if (typeof initTranslator === 'function') {
                initTranslator();
            }

            const themeToggle = document.getElementById('themeToggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', () => {
                    document.body.classList.toggle('dark-theme');
                    const isDark = document.body.classList.contains('dark-theme');
                    localStorage.setItem('theme', isDark ? 'dark' : 'light');
                });
            }

            // Check for saved theme preference or use device preference
            const savedTheme = localStorage.getItem('theme');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

            if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
                document.body.classList.add('dark-theme');
            } else {
                document.body.classList.remove('dark-theme');
            }

            // Initialize phone scanning animation
            const phoneDevice = document.getElementById('phoneAnimation');
            if (phoneDevice) {
                // Add recording class after scanning animation
                setTimeout(() => {
                    phoneDevice.classList.add('recording');
                }, 3000);
            }

            // Initialize any modals on the page
            initializeModalControls();
        });
    </script>

       
    </script>
    <!-- Decorative background elements -->
    <div class="fixed w-full h-full pointer-events-none opacity-20 overflow-hidden">
        <div class="circuit-pattern absolute inset-0"></div>
        <div class="neural-network absolute inset-0">
            <!-- Neural network nodes will be added by JS -->
        </div>
        <div class="circle-bg w-96 h-96 top-0 right-0 animate-pulse-slow"></div>
        <div class="circle-bg w-[800px] h-[800px] -bottom-96 -left-40 animate-pulse-slow"></div>
        <div class="dot-pattern absolute inset-0 opacity-30"></div>
        
        <!-- Data flow particles will be added by JS -->
        <div class="data-flow absolute inset-0"></div>
    </div>

    <!-- Empty container for spacing -->
    <div class="container mx-auto px-4 py-6"></div>
    
    <!-- Feature Hero Section -->
    <!-- Back button -->
    <a href="javascript:history.back()" class="fixed top-6 left-6 z-50 flex items-center justify-center w-10 h-10 rounded-full bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white transition-all shadow-lg border border-white/20">
        <i class="fas fa-arrow-left"></i>
    </a>
    
    <section class="relative py-20 pt-32 overflow-hidden bg-gradient-to-br from-blue-600/5 to-cyan-500/10">
        <!-- Wave Background Animations -->
        <div class="absolute inset-0 z-0 overflow-hidden">
            <div class="wave-bg wave-1 opacity-30"></div>
            <div class="wave-bg wave-2 opacity-25"></div>
            <div class="wave-bg wave-3 opacity-20"></div>
        </div>
        
        <!-- Neural network animation background for the entire page -->
        <div class="absolute inset-0 overflow-hidden z-0 opacity-70 page-neural-network">
            <!-- Neural nodes -->
            <div class="page-neural-node" style="top: 15%; left: 10%"></div>
            <div class="page-neural-node" style="top: 25%; left: 35%"></div>
            <div class="page-neural-node" style="top: 45%; left: 25%"></div>
            <div class="page-neural-node" style="top: 65%; left: 15%"></div>
            <div class="page-neural-node" style="top: 75%; left: 40%"></div>
            <div class="page-neural-node" style="top: 20%; left: 60%"></div>
            <div class="page-neural-node" style="top: 40%; left: 80%"></div>
            <div class="page-neural-node" style="top: 60%; left: 70%"></div>
            <div class="page-neural-node" style="top: 80%; left: 85%"></div>
            <div class="page-neural-node" style="top: 30%; left: 50%"></div>
            <div class="page-neural-node" style="top: 55%; left: 45%"></div>
            <div class="page-neural-node" style="top: 35%; left: 75%"></div>
            <div class="page-neural-node" style="top: 70%; left: 60%"></div>
            <div class="page-neural-node" style="top: 85%; left: 30%"></div>
            <div class="page-neural-node" style="top: 10%; left: 65%"></div>
            <div class="page-neural-node" style="top: 50%; left: 20%"></div>
            <div class="page-neural-node" style="top: 15%; left: 90%"></div>
            <div class="page-neural-node" style="top: 90%; left: 15%"></div>
            <div class="page-neural-node" style="top: 5%; left: 40%"></div>
            <div class="page-neural-node" style="top: 95%; left: 70%"></div>
            <div class="page-neural-node" style="top: 22%; left: 78%"></div>
            <div class="page-neural-node" style="top: 48%; left: 62%"></div>
            <div class="page-neural-node" style="top: 38%; left: 12%"></div>
            <div class="page-neural-node" style="top: 82%; left: 52%"></div>
            <div class="page-neural-node" style="top: 8%; left: 22%"></div>
            
            <!-- Neural connections -->
            <div class="page-neural-connection" style="top: 15%; left: 10%; width: 280px; transform: rotate(15deg);"></div>
            <div class="page-neural-connection" style="top: 25%; left: 35%; width: 200px; transform: rotate(-25deg);"></div>
            <div class="page-neural-connection" style="top: 45%; left: 25%; width: 260px; transform: rotate(35deg);"></div>
            <div class="page-neural-connection" style="top: 20%; left: 60%; width: 230px; transform: rotate(45deg);"></div>
            <div class="page-neural-connection" style="top: 60%; left: 70%; width: 200px; transform: rotate(-15deg);"></div>
            <div class="page-neural-connection" style="top: 40%; left: 80%; width: 220px; transform: rotate(-35deg);"></div>
            <div class="page-neural-connection" style="top: 30%; left: 50%; width: 250px; transform: rotate(20deg);"></div>
            <div class="page-neural-connection" style="top: 55%; left: 45%; width: 210px; transform: rotate(-10deg);"></div>
            <div class="page-neural-connection" style="top: 70%; left: 60%; width: 240px; transform: rotate(5deg);"></div>
            <div class="page-neural-connection" style="top: 85%; left: 30%; width: 260px; transform: rotate(-30deg);"></div>
            <div class="page-neural-connection" style="top: 10%; left: 65%; width: 270px; transform: rotate(-5deg);"></div>
            <div class="page-neural-connection" style="top: 50%; left: 20%; width: 230px; transform: rotate(10deg);"></div>
            <div class="page-neural-connection" style="top: 80%; left: 85%; width: 220px; transform: rotate(-20deg);"></div>
            <div class="page-neural-connection" style="top: 5%; left: 40%; width: 240px; transform: rotate(25deg);"></div>
            <div class="page-neural-connection" style="top: 95%; left: 70%; width: 250px; transform: rotate(-15deg);"></div>
            <div class="page-neural-connection" style="top: 22%; left: 78%; width: 180px; transform: rotate(-10deg);"></div>
            <div class="page-neural-connection" style="top: 48%; left: 62%; width: 290px; transform: rotate(15deg);"></div>
            <div class="page-neural-connection" style="top: 38%; left: 12%; width: 200px; transform: rotate(30deg);"></div>
            <div class="page-neural-connection" style="top: 82%; left: 52%; width: 210px; transform: rotate(-25deg);"></div>
            <div class="page-neural-connection" style="top: 8%; left: 22%; width: 240px; transform: rotate(5deg);"></div>
        </div>
        
        <!-- Animated background elements -->
        <div class="absolute top-0 left-0 w-full h-full">
            <div class="absolute top-1/4 left-1/4 w-64 h-64 rounded-full bg-blue-400/10 blur-3xl animate-blob"></div>
            <div class="absolute bottom-1/4 right-1/4 w-80 h-80 rounded-full bg-cyan-400/10 blur-3xl animate-blob" style="animation-delay: -2s;"></div>
            <div class="absolute top-3/4 left-1/2 w-72 h-72 rounded-full bg-primary/5 blur-3xl animate-blob" style="animation-delay: -4s;"></div>
        </div>
        
        <!-- Decorative elements for voice recording theme -->
        <div class="absolute top-0 right-0 w-96 h-96 blob opacity-40 -z-10 hidden lg:block"></div>
        <div class="absolute bottom-0 left-0 w-64 h-64 blob-small opacity-30 -z-10 hidden lg:block"></div>
        
        <!-- Sound wave animations specific to voice recording -->
        <div class="absolute inset-0 overflow-hidden opacity-20">
            <svg width="100%" height="100%" viewBox="0 0 1200 800" preserveAspectRatio="none">
                <path class="sound-wave" d="M0,400 Q100,380 200,400 Q300,420 400,400 Q500,380 600,400 Q700,420 800,400 Q900,380 1000,400 Q1100,420 1200,400" fill="none" stroke="#0F6FFF" stroke-width="2">
                    <animate attributeName="d" 
                    values="M0,400 Q100,380 200,400 Q300,420 400,400 Q500,380 600,400 Q700,420 800,400 Q900,380 1000,400 Q1100,420 1200,400;
                            M0,400 Q100,350 200,400 Q300,450 400,400 Q500,350 600,400 Q700,450 800,400 Q900,350 1000,400 Q1100,450 1200,400;
                            M0,400 Q100,380 200,400 Q300,420 400,400 Q500,380 600,400 Q700,420 800,400 Q900,380 1000,400 Q1100,420 1200,400" 
                    dur="10s" repeatCount="indefinite"/>
                </path>
                <path class="sound-wave" d="M0,430 Q100,410 200,430 Q300,450 400,430 Q500,410 600,430 Q700,450 800,430 Q900,410 1000,430 Q1100,450 1200,430" fill="none" stroke="#00B0B6" stroke-width="2" opacity="0.6">
                    <animate attributeName="d" 
                    values="M0,430 Q100,410 200,430 Q300,450 400,430 Q500,410 600,430 Q700,450 800,430 Q900,410 1000,430 Q1100,450 1200,430;
                            M0,430 Q100,380 200,430 Q300,480 400,430 Q500,380 600,430 Q700,480 800,430 Q900,380 1000,430 Q1100,480 1200,430;
                            M0,430 Q100,410 200,430 Q300,450 400,430 Q500,410 600,430 Q700,450 800,430 Q900,410 1000,430 Q1100,450 1200,430" 
                    dur="7s" repeatCount="indefinite"/>
                </path>
                <path class="sound-wave" d="M0,370 Q100,350 200,370 Q300,390 400,370 Q500,350 600,370 Q700,390 800,370 Q900,350 1000,370 Q1100,390 1200,370" fill="none" stroke="#00D695" stroke-width="2" opacity="0.4">
                    <animate attributeName="d" 
                    values="M0,370 Q100,350 200,370 Q300,390 400,370 Q500,350 600,370 Q700,390 800,370 Q900,350 1000,370 Q1100,390 1200,370;
                            M0,370 Q100,320 200,370 Q300,420 400,370 Q500,320 600,370 Q700,420 800,370 Q900,320 1000,370 Q1100,420 1200,370;
                            M0,370 Q100,350 200,370 Q300,390 400,370 Q500,350 600,370 Q700,390 800,370 Q900,350 1000,370 Q1100,390 1200,370" 
                    dur="13s" repeatCount="indefinite"/>
                </path>
            </svg>
        </div>
        
        <div class="container mx-auto px-4 flex flex-col md:flex-row items-center relative z-1">
            <div class="md:w-1/2 mb-16 md:mb-0 md:pr-12 animate-fade-in-up bg-transparent">
                <div class="inline-block px-3 py-1 rounded-full bg-blue-50/80 backdrop-blur-sm text-primary font-medium text-sm mb-4" data-i18n="voiceRecordingFeature">
                    Voice Recording Feature
                </div>
                <h1 class="text-4xl md:text-5xl font-bold mb-6 leading-tight text-white" data-i18n="voiceRecordingTitle">High-Quality <span class="gradient-text">Voice Recording</span> Technology</h1>
                <p class="text-lg mb-8 text-gray-300 leading-relaxed" data-i18n="voiceRecordingSubtitle">Capture every nuance of patient conversations with crystal-clear audio. Our advanced recording system ensures no detail is missed.</p>
                <div class="flex flex-wrap gap-4">
                    <button class="px-6 py-3 rounded-full border-2 border-primary text-primary font-medium hover:bg-primary hover:text-white transition-all" onclick="openModal('demoModal')"><span class="font-bold" data-i18n="tryItNow">Try It Now</span></button>
                    <button class="px-6 py-3 rounded-full border-2 border-primary text-primary font-medium hover:bg-primary hover:text-white transition-all" onclick="openModal('specsModal')" data-i18n="viewSpecifications">View Specifications</button>
                </div>
            </div>
            <div class="md:w-1/2 flex justify-center relative animate-fade-in bg-transparent">
                <!-- Animated Recording Session Visualization -->
                <div class="w-full max-w-md relative bg-transparent">
                    <!-- Enhanced background glow effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-primary/30 to-secondary/30 rounded-2xl blur-3xl opacity-40 animate-pulse-slow"></div>
                    
                    <!-- Recording session visualization -->
                    <div class="relative bg-gray-900/50 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden">
                        <!-- Session header with live indicator -->
                        <div class="bg-gradient-to-r from-primary/30 to-secondary/30 backdrop-blur-md p-4 flex justify-between items-center border-b border-gray-700/50">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gradient-to-b from-gray-700 to-gray-900 rounded-full shadow flex items-center justify-center mr-3 relative">
                                    <div class="absolute inset-1 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full blur-sm"></div>
                                    <i class="fas fa-microphone text-white text-lg"></i>
                                </div>
                                <div class="text-lg font-semibold text-white" data-i18n="recordingSession">Recording Session</div>
                            </div>
                            <div class="flex items-center bg-black/10 px-3 py-1 rounded-full">
                                <div class="w-3 h-3 rounded-full bg-red-500 animate-pulse mr-2"></div>
                                <span class="text-sm text-red-500 font-medium">LIVE</span>
                            </div>
                        </div>
                        
                        <!-- Enhanced audio visualization interface -->
                        <div class="p-6 bg-gradient-to-b from-gray-800/70 to-gray-900/70"> 
                            <!-- Voice pattern visualization -->
                            <div class="relative h-48 mb-4 overflow-hidden">
                                <!-- Dynamic voice pattern -->
                                <div class="absolute inset-0">
                                    <!-- Circular sound visualization -->
                                    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                        <div class="absolute w-44 h-44 rounded-full border-2 border-primary/20 animate-ping opacity-60" style="animation-duration: 1.5s;"></div>
                                        <div class="absolute w-44 h-44 rounded-full border-2 border-secondary/20 animate-ping opacity-70" style="animation-duration: 2s; animation-delay: 0.3s;"></div>
                                        <div class="absolute w-44 h-44 rounded-full border-2 border-tertiary/20 animate-ping opacity-80" style="animation-duration: 2.5s; animation-delay: 0.6s;"></div>
                                        
                                        <!-- Central microphone glow -->
                                        <div class="absolute w-16 h-16 rounded-full bg-gradient-to-r from-primary/30 to-secondary/30 blur-md animate-pulse-slow"></div>
                                        <div class="w-14 h-14 bg-gray-800/80 backdrop-blur-md rounded-full flex items-center justify-center relative z-10 shadow-lg">
                                            <i class="fas fa-microphone-alt text-primary text-xl"></i>
                                        </div>
                                    </div>
                                    
                                    <!-- Radial audio wave visualization -->
                                    <svg class="absolute inset-0 w-full h-full" viewBox="0 0 400 200">
                                        <!-- Horizontal wave lines -->
                                        <path class="audio-wave" d="M0,100 Q40,70 80,100 Q120,130 160,100 Q200,70 240,100 Q280,130 320,100 Q360,70 400,100" fill="none" stroke="rgba(15, 111, 255, 0.6)" stroke-width="1.5">
                                            <animate attributeName="d" 
                                                values="M0,100 Q40,70 80,100 Q120,130 160,100 Q200,70 240,100 Q280,130 320,100 Q360,70 400,100;
                                                      M0,100 Q40,85 80,100 Q120,115 160,100 Q200,85 240,100 Q280,115 320,100 Q360,85 400,100;
                                                      M0,100 Q40,70 80,100 Q120,130 160,100 Q200,70 240,100 Q280,130 320,100 Q360,70 400,100" 
                                                dur="4s" repeatCount="indefinite"/>
                                        </path>
                                        <path class="audio-wave" d="M0,100 Q40,80 80,100 Q120,120 160,100 Q200,80 240,100 Q280,120 320,100 Q360,80 400,100" fill="none" stroke="rgba(0, 176, 182, 0.5)" stroke-width="1.5" opacity="0.8">
                                            <animate attributeName="d" 
                                                values="M0,100 Q40,80 80,100 Q120,120 160,100 Q200,80 240,100 Q280,120 320,100 Q360,80 400,100;
                                                      M0,100 Q40,60 80,100 Q120,140 160,100 Q200,60 240,100 Q280,140 320,100 Q360,60 400,100;
                                                      M0,100 Q40,80 80,100 Q120,120 160,100 Q200,80 240,100 Q280,120 320,100 Q360,80 400,100" 
                                                dur="5s" repeatCount="indefinite"/>
                                        </path>
                                        <path class="audio-wave" d="M0,100 Q40,90 80,100 Q120,110 160,100 Q200,90 240,100 Q280,110 320,100 Q360,90 400,100" fill="none" stroke="rgba(0, 214, 149, 0.4)" stroke-width="1.5" opacity="0.6">
                                            <animate attributeName="d" 
                                                values="M0,100 Q40,90 80,100 Q120,110 160,100 Q200,90 240,100 Q280,110 320,100 Q360,90 400,100;
                                                      M0,100 Q40,75 80,100 Q120,125 160,100 Q200,75 240,100 Q280,125 320,100 Q360,75 400,100;
                                                      M0,100 Q40,90 80,100 Q120,110 160,100 Q200,90 240,100 Q280,110 320,100 Q360,90 400,100" 
                                                dur="6s" repeatCount="indefinite"/>
                                        </path>
                                    </svg>
                                </div>
                                
                                <!-- Equalizer bars at bottom -->
                                <div class="absolute bottom-0 left-0 right-0 h-24 flex items-end justify-center">
                                    <div class="flex items-end space-x-1 h-full w-full px-2">
                                        <!-- Dynamic equalizer bars -->
                                        <div class="flex-1 bg-primary/70 rounded-t-md animate-pulse-slow h-1/5" style="animation-duration: 1.1s"></div>
                                        <div class="flex-1 bg-primary/70 rounded-t-md animate-pulse-slow h-2/5" style="animation-duration: 1.3s"></div>
                                        <div class="flex-1 bg-primary/70 rounded-t-md animate-pulse-slow h-3/5" style="animation-duration: 1.2s"></div>
                                        <div class="flex-1 bg-primary/70 rounded-t-md animate-pulse-slow h-4/5" style="animation-duration: 1.4s"></div>
                                        <div class="flex-1 bg-primary/70 rounded-t-md animate-pulse-slow h-full" style="animation-duration: 1.5s"></div>
                                        <div class="flex-1 bg-primary/80 rounded-t-md animate-pulse-slow h-4/5" style="animation-duration: 1.7s"></div>
                                        <div class="flex-1 bg-primary/80 rounded-t-md animate-pulse-slow h-3/5" style="animation-duration: 1.3s"></div>
                                        <div class="flex-1 bg-primary/90 rounded-t-md animate-pulse-slow h-4/5" style="animation-duration: 1.6s"></div>
                                        <div class="flex-1 bg-primary/90 rounded-t-md animate-pulse-slow h-full" style="animation-duration: 1.2s"></div>
                                        <div class="flex-1 bg-secondary/80 rounded-t-md animate-pulse-slow h-3/5" style="animation-duration: 1.5s"></div>
                                        <div class="flex-1 bg-secondary/80 rounded-t-md animate-pulse-slow h-2/5" style="animation-duration: 1.3s"></div>
                                        <div class="flex-1 bg-secondary/70 rounded-t-md animate-pulse-slow h-3/5" style="animation-duration: 1.4s"></div>
                                        <div class="flex-1 bg-secondary/70 rounded-t-md animate-pulse-slow h-4/5" style="animation-duration: 1.6s"></div>
                                        <div class="flex-1 bg-secondary/80 rounded-t-md animate-pulse-slow h-full" style="animation-duration: 1.2s"></div>
                                        <div class="flex-1 bg-secondary/80 rounded-t-md animate-pulse-slow h-3/5" style="animation-duration: 1.5s"></div>
                                        <div class="flex-1 bg-secondary/90 rounded-t-md animate-pulse-slow h-2/5" style="animation-duration: 1.7s"></div>
                                        <div class="flex-1 bg-secondary/90 rounded-t-md animate-pulse-slow h-3/5" style="animation-duration: 1.3s"></div>
                                        <div class="flex-1 bg-tertiary/80 rounded-t-md animate-pulse-slow h-4/5" style="animation-duration: 1.6s"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Voice quality indicator -->
                            <div class="bg-gray-800/50 backdrop-blur-sm rounded-md px-3 py-2 flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-2 h-2 rounded-full bg-green-500 animate-pulse mr-2"></div>
                                    <span class="text-xs text-gray-300 font-medium" data-i18n="HD Audio Quality">HD Audio Quality</span>
                                </div>
                                <div class="text-xs text-primary font-medium">48kHz/24-bit</div>
                            </div>
                            
                            <!-- Progress and controls -->
                            <div>
                                <!-- Progress bar -->
                                <div class="h-3 bg-gray-700/50 rounded-full mb-4 relative overflow-hidden">
                                    <div class="h-full bg-gradient-to-r from-primary/90 to-secondary/90 rounded-full animate-pulse-slow" style="width: 67%"></div>
                                </div>
                                
                                <!-- Time and controls -->
                                <div class="flex justify-between items-center">
                                    <div class="flex items-center space-x-2">
                                        <div class="text-xs text-gray-300 font-medium bg-gray-800/40 backdrop-blur-sm px-2 py-1 rounded-md">
                                            05:27
                                        </div>
                                        <div class="text-xs text-gray-300 font-medium" data-i18n="remaining">
                                            remaining
                                        </div>
                                    </div>
                                    
                                    <div class="flex space-x-3">
                                        <button class="w-12 h-12 rounded-full bg-primary/20 backdrop-blur-sm flex items-center justify-center text-primary hover:bg-primary/30 transition-colors">
                                            <i class="fas fa-pause"></i>
                                        </button>
                                        <button class="w-12 h-12 rounded-full gradient-bg flex items-center justify-center text-white shadow-md">
                                            <i class="fas fa-microphone-alt"></i>
                                        </button>
                                        <button class="w-12 h-12 rounded-full bg-primary/20 backdrop-blur-sm flex items-center justify-center text-primary hover:bg-primary/30 transition-colors">
                                            <i class="fas fa-stop"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Bottom curve -->
        <div class="absolute bottom-0 left-0 w-full overflow-hidden" style="height: 50px;">
            <svg class="absolute bottom-0 overflow-hidden" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="none" version="1.1" viewBox="0 0 2560 100" x="0" y="0">
                <polygon class="fill-gray-900 dark:fill-gray-900" points="2560 0 2560 100 0 100" style="stroke: none;"></polygon>
            </svg>
        </div>
    </section>

    <!-- Key Features Section -->
    <section class="py-20 bg-gray-900 relative overflow-hidden">
        <!-- Background elements -->
        <div class="absolute inset-0 bg-gradient-to-b from-gray-900 to-gray-800 opacity-70"></div>
        <div class="absolute inset-0 dot-pattern opacity-10" style="border-top: none; top: -2px; border-width: 0;"></div>
        <polygon class="fill-gray-900 dark:fill-gray-900" points="2560 0 2560 100 0 100" style="stroke: none;"></polygon>
        
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center max-w-3xl mx-auto animate-on-scroll mb-16">
                <div class="inline-block px-3 py-1 rounded-full bg-blue-900/50 text-primary font-medium text-sm mb-4" data-i18n="recordingCapabilities">
                    Recording Capabilities
                </div>
                <h2 class="text-3xl font-bold text-white mb-6" data-i18n="advancedFeatures">Advanced Voice Recording Features</h2>
                <p class="text-gray-300" data-i18n="recordingCapabilitiesSubtitle">Our recording technology provides exceptional clarity and precision to ensure the highest quality audio for AI analysis.</p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <!-- Feature Card 1 -->
                <div class="feature-card bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 animate-on-scroll group hover:bg-blue-900/30 hover:bg-opacity-30 shadow-sm hover:shadow-md relative overflow-hidden">
                    <!-- High-Fidelity animation -->
                    <div class="high-fidelity-waves">
                        <div class="audio-bar" style="height: 40%; --duration: 0.7s;"></div>
                        <div class="audio-bar" style="height: 60%; --duration: 0.8s;"></div>
                        <div class="audio-bar" style="height: 80%; --duration: 0.6s;"></div>
                        <div class="audio-bar" style="height: 100%; --duration: 0.9s;"></div>
                        <div class="audio-bar" style="height: 70%; --duration: 0.7s;"></div>
                        <div class="audio-bar" style="height: 90%; --duration: 0.8s;"></div>
                        <div class="audio-bar" style="height: 50%; --duration: 0.6s;"></div>
                        <div class="audio-bar" style="height: 80%; --duration: 0.9s;"></div>
                        <div class="audio-bar" style="height: 60%; --duration: 0.7s;"></div>
                        <div class="audio-bar" style="height: 100%; --duration: 0.8s;"></div>
                        <div class="audio-bar" style="height: 40%; --duration: 0.6s;"></div>
                    </div>

                    <div class="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mb-6 group-hover:bg-blue-200 transform transition-all group-hover:scale-110 relative z-10">
                        <i class="fas fa-wave-square text-xl text-primary"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 text-white relative z-10" data-i18n="highFidelityAudio">High-Fidelity Audio</h3>
                    <p class="text-gray-300 relative z-10" data-i18n="highFidelityDescription">Crystal-clear 48kHz 24-bit recording with advanced noise reduction for pristine audio quality.</p>
                    <div class="mt-6 pt-6 border-t border-gray-700 relative z-10">
                        <a href="#" onclick="openModal('audioQualityModal'); return false;" class="flex items-center text-primary font-medium group-hover:translate-x-2 transition-transform">
                            <span data-i18n="learnMore">Learn more</span>
                            <i class="fas fa-arrow-right text-sm ml-2"></i>
                        </a>
                    </div>
                </div>
                
                <!-- Feature Card 2 -->
                <div class="feature-card bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 animate-on-scroll group hover:bg-blue-900/30 hover:bg-opacity-30 shadow-sm hover:shadow-md relative overflow-hidden">
                    <!-- Intelligent Filtering animation -->
                    <div class="filter-sweep"></div>
                    <div class="noise-particles">
                        <div class="noise-particle" style="top: 20%; left: 30%;"></div>
                        <div class="noise-particle" style="top: 50%; left: 20%;"></div>
                        <div class="noise-particle" style="top: 70%; left: 40%;"></div>
                        <div class="noise-particle" style="top: 30%; left: 60%;"></div>
                        <div class="noise-particle" style="top: 60%; left: 70%;"></div>
                        <div class="noise-particle" style="top: 80%; left: 50%;"></div>
                        <div class="noise-particle" style="top: 40%; left: 80%;"></div>
                        <div class="noise-particle" style="top: 10%; left: 10%;"></div>
                    </div>

                    <div class="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mb-6 group-hover:bg-blue-200 transform transition-all group-hover:scale-110 relative z-10">
                        <i class="fas fa-filter text-xl text-primary"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 text-white relative z-10" data-i18n="intelligentFiltering">Intelligent Filtering</h3>
                    <p class="text-gray-300 relative z-10" data-i18n="intelligentFilteringDescription">Automatically filters out background noise and ambient sounds while preserving speech clarity.</p>
                    <div class="mt-6 pt-6 border-t border-gray-700 relative z-10">
                        <a href="#" onclick="openModal('filteringModal'); return false;" class="flex items-center text-primary font-medium group-hover:translate-x-2 transition-transform">
                            <span data-i18n="learnMore">Learn more</span>
                            <i class="fas fa-arrow-right text-sm ml-2"></i>
                        </a>
                    </div>
                </div>
                
                <!-- Feature Card 3 -->
                <div class="feature-card bg-gray-800/80 backdrop-blur-sm rounded-xl p-6 animate-on-scroll group hover:bg-blue-900/30 hover:bg-opacity-30 shadow-sm hover:shadow-md relative overflow-hidden">
                    <!-- Advanced Noise Cancellation animation -->
                    <div class="cancellation-field"></div>
                    <div class="cancellation-field" style="width: 80%; height: 80%; animation-delay: 0.5s;"></div>
                    <div class="cancellation-field" style="width: 100%; height: 100%; animation-delay: 1s; border-color: rgba(59, 130, 246, 0.1);"></div>

                    <div class="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mb-6 group-hover:bg-blue-200 transform transition-all group-hover:scale-110 relative z-10">
                        <i class="fas fa-microphone-alt-slash text-xl text-primary"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3 text-white relative z-10" data-i18n="advancedNoiseCancellation">Advanced Noise Cancellation</h3>
                    <p class="text-gray-300 relative z-10" data-i18n="advancedNoiseCancellationDescription">Multi-stage noise reduction technology eliminates distractions for focused patient conversations.</p>
                    <div class="mt-6 pt-6 border-t border-gray-700 relative z-10">
                        <a href="#" onclick="openModal('noiseCancellationModal'); return false;" class="flex items-center text-primary font-medium group-hover:translate-x-2 transition-transform">
                            <span data-i18n="learnMore">Learn more</span>
                            <i class="fas fa-arrow-right text-sm ml-2"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section class="py-20 relative bg-gradient-to-tr from-gray-800 to-gray-900">
        <!-- Background elements -->
        <div class="absolute top-0 left-0 w-full h-full dot-pattern opacity-10"></div>
        
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center max-w-3xl mx-auto animate-on-scroll mb-16">
                <div class="inline-block px-3 py-1 rounded-full bg-blue-900/50 text-primary font-medium text-sm mb-4" data-i18n="recordingProcess">
                    Recording Process
                </div>
                <h2 class="text-3xl font-bold text-white mb-6" data-i18n="howItWorks">How Our Voice Recording Works</h2>
                <p class="text-gray-300" data-i18n="howItWorksSubtitle">Our simple three-step process ensures perfect audio capture every time.</p>
            </div>
            
            <div class="relative">
                <!-- Connection line -->
                <div class="hidden md:block absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-blue-800 via-primary to-teal-800 rounded-full -z-10"></div>
                
                <div class="grid md:grid-cols-3 gap-10">
                    <!-- Step 1 -->
                    <div class="bg-gray-800/80 backdrop-blur-sm rounded-xl shadow-sm p-6 relative animate-on-scroll group hover:shadow-md transition-all">
                        <div class="absolute -top-6 left-1/2 transform -translate-x-1/2 w-12 h-12 rounded-full gradient-bg flex items-center justify-center text-white font-bold z-10 group-hover:scale-110 transition-transform">1</div>
                        <div class="mt-6">
                            <div class="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mb-6 mx-auto group-hover:scale-110 transition-all cursor-pointer" onclick="openModal('recordingStartModal')">
                                <i class="fas fa-power-off text-xl text-primary"></i>
                            </div>
                            <h3 class="text-xl font-semibold mb-3 text-white text-center" data-i18n="startSession">Start Session</h3>
                            <p class="text-gray-300 text-center" data-i18n="startSessionDescription">Begin recording with a simple tap. The system automatically calibrates for optimal voice capture.</p>
                            <button onclick="openModal('recordingStartModal')" class="mt-4 px-4 py-2 w-full rounded-full border border-primary text-primary hover:bg-primary hover:text-white transition-all" data-i18n="learnMore">Learn more</button>
                        </div>
                    </div>
                    
                    <!-- Step 2 -->
                    <div class="bg-gray-800/80 backdrop-blur-sm rounded-xl shadow-sm p-6 relative animate-on-scroll group hover:shadow-md transition-all">
                        <div class="absolute -top-6 left-1/2 transform -translate-x-1/2 w-12 h-12 rounded-full gradient-bg flex items-center justify-center text-white font-bold z-10 group-hover:scale-110 transition-transform">2</div>
                        <div class="mt-6">
                            <div class="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mb-6
                                 mx-auto cursor-pointer group-hover:scale-110 transition-all" 
                                 id="showRecordingModalBtn" onclick="openRecordingModal()">
                                <i class="fas fa-microphone text-xl text-primary"></i>
                            </div>
                            
                            <h3 class="text-xl font-semibold mb-3 text-white text-center" data-i18n="recordConversation">Record Conversation</h3>
                            <p class="text-gray-300 text-center" data-i18n="recordConversationDescription">Conduct your session naturally while our technology captures every word with pristine clarity.</p>
                            <button onclick="openRecordingModal()" class="mt-4 px-4 py-2 w-full rounded-full border border-primary text-primary hover:bg-primary hover:text-white transition-all" data-i18n="learnMore">Learn more</button>
                        </div>
                    </div>
                    
                    <!-- Step 3 -->
                    <div class="bg-gray-800/80 backdrop-blur-sm rounded-xl shadow-sm p-6 relative animate-on-scroll group hover:shadow-md transition-all">
                        <div class="absolute -top-6 left-1/2 transform -translate-x-1/2 w-12 h-12 rounded-full gradient-bg flex items-center justify-center text-white font-bold z-10 group-hover:scale-110 transition-transform">3</div>
                        <div class="mt-6">
                            <div class="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mb-6 mx-auto cursor-pointer group-hover:scale-110 transition-all" onclick="openModal('saveProcessModal')">
                                <i class="fas fa-check-circle text-xl text-primary"></i>
                            </div>
                            <h3 class="text-xl font-semibold mb-3 text-white text-center" data-i18n="saveProcess">Save & Process</h3>
                            <p class="text-gray-300 text-center" data-i18n="saveProcessDescription">Your recording is automatically saved, processed, and prepared for AI analysis.</p>
                            <button onclick="openModal('saveProcessModal')" class="mt-4 px-4 py-2 w-full rounded-full border border-primary text-primary hover:bg-primary hover:text-white transition-all" data-i18n="learnMore">Learn more</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Technical Specifications Section -->
    <section class="py-20 bg-white relative overflow-hidden">
        <!-- Background elements -->
        <div class="absolute inset-0 bg-gradient-to-b from-gray-900 to-gray-800 opacity-70 dark:from-gray-900 dark:to-gray-800"></div>
        
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center max-w-3xl mx-auto animate-on-scroll mb-16">
                <div class="inline-block px-3 py-1 rounded-full bg-blue-50 text-primary font-medium text-sm mb-4" data-i18n="technicalDetails">
                    Technical Details
                </div>
                <h2 class="text-3xl font-bold text-dark mb-6" data-i18n="technicalSpecifications">Technical Specifications</h2>
                <p class="text-gray-600" data-i18n="technicalSpecificationsSubtitle">Our recording technology is built with the highest quality standards to ensure reliable performance.</p>
            </div>

            <!-- Specifications Grid -->
            <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
                <!-- Left Column -->
                <div class="space-y-6">
                    <div class="bg-white shadow-sm rounded-xl p-6 hover:shadow-md transition-all">
                        <h3 class="text-lg font-semibold text-dark mb-4 flex items-center">
                            <i class="fas fa-file-audio text-primary mr-3"></i>
                            Audio Format
                        </h3>
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-center">
                                <i class="fas fa-check text-tertiary mr-2 text-sm"></i>
                                WAV, MP3, FLAC, and AAC
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-tertiary mr-2 text-sm"></i>
                                48kHz / 24-bit high-resolution audio
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-tertiary mr-2 text-sm"></i>
                                Variable bit-rate encoding
                            </li>
                        </ul>
                    </div>

                    <div class="bg-white shadow-sm rounded-xl p-6 hover:shadow-md transition-all">
                        <h3 class="text-lg font-semibold text-dark mb-4 flex items-center">
                            <i class="fas fa-microchip text-primary mr-3"></i>
                            Processing Technology
                        </h3>
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-center">
                                <i class="fas fa-check text-tertiary mr-2 text-sm"></i>
                                Real-time digital signal processing
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-tertiary mr-2 text-sm"></i>
                                Multi-layer noise cancellation
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-tertiary mr-2 text-sm"></i>
                                Adaptive gain control
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- Right Column -->
                <div class="space-y-6">
                    <div class="bg-white shadow-sm rounded-xl p-6 hover:shadow-md transition-all">
                        <h3 class="text-lg font-semibold text-dark mb-4 flex items-center">
                            <i class="fas fa-broadcast-tower text-primary mr-3"></i>
                            Input Sources
                        </h3>
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-center">
                                <i class="fas fa-check text-tertiary mr-2 text-sm"></i>
                                Built-in device microphone
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-tertiary mr-2 text-sm"></i>
                                External USB/Bluetooth microphones
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-tertiary mr-2 text-sm"></i>
                                Professional XLR microphone support
                            </li>
                        </ul>
                    </div>

                    <div class="bg-white shadow-sm rounded-xl p-6 hover:shadow-md transition-all">
                        <h3 class="text-lg font-semibold text-dark mb-4 flex items-center">
                            <i class="fas fa-shield-alt text-primary mr-3"></i>
                            Security
                        </h3>
                        <ul class="space-y-2 text-gray-600">
                            <li class="flex items-center">
                                <i class="fas fa-check text-tertiary mr-2 text-sm"></i>
                                End-to-end 256-bit encryption
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-tertiary mr-2 text-sm"></i>
                                HIPAA compliant storage
                            </li>
                            <li class="flex items-center">
                                <i class="fas fa-check text-tertiary mr-2 text-sm"></i>
                                Secure access controls
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Download Specs Button -->
            <div class="text-center mt-12">
                <a href="assets/pdf/wellora_report.html" target="_blank" class="px-6 py-3 rounded-full border-2 border-primary text-primary font-medium hover:bg-primary hover:text-white transition-all inline-flex items-center" data-i18n="downloadFullSpecs">
                    <i class="fas fa-download mr-2"></i>
                    Download Full Specifications
                </a>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="gradient-bg-animated py-20 relative overflow-hidden">
        <!-- Floating particles -->
        <div class="absolute inset-0 z-0">
            <div class="absolute w-4 h-4 rounded-full bg-white opacity-20 top-1/4 left-1/4 animate-float" style="animation-delay: -1s;"></div>
            <div class="absolute w-3 h-3 rounded-full bg-white opacity-30 top-1/2 right-1/4 animate-float" style="animation-delay: -2.5s;"></div>
            <div class="absolute w-5 h-5 rounded-full bg-white opacity-20 bottom-1/3 left-1/3 animate-float" style="animation-delay: -3.5s;"></div>
        </div>
        
        <div class="container mx-auto px-4 text-center animate-on-scroll relative z-10">
            <div class="max-w-3xl mx-auto">
                <h2 class="text-3xl md:text-4xl font-bold mb-6 text-white" data-i18n="experienceRecording">Experience Crystal-Clear Voice Recording Today</h2>
                <p class="text-xl opacity-90 text-white mb-10" data-i18n="experienceRecordingSubtitle">Request a free demo to see how our voice recording technology can enhance your patient conversations.</p>
                <div class="flex flex-wrap justify-center gap-4">
                    <button class="px-8 py-4 rounded-full border-2 border-white text-white font-medium hover:bg-white hover:text-primary transition-all transform hover:-translate-y-1" onclick="openModal('demoModal')" data-i18n="requestADemo">Request a Demo</button>
                    <button class="px-8 py-4 rounded-full border-2 border-white text-white font-medium hover:bg-white hover:text-primary transition-all transform hover:-translate-y-1" data-i18n="contactSales">Contact Sales</button>
                </div>
                
                <div class="mt-16 pt-16 border-t border-white border-opacity-20">
                    <div class="grid grid-cols-3 gap-y-8">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white mb-2 counter">99.9%</div>
                            <p class="text-blue-100">Uptime Reliability</p>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white mb-2 counter">48kHz</div>
                            <p class="text-blue-100">Audio Quality</p>
                        </div>
                        <div class="text-center">
                            <div class="text-3xl font-bold text-white mb-2 counter">100%</div>
                            <p class="text-blue-100">HIPAA Compliant</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <footer class="bg-dark text-white py-12 relative">
        <!-- Wave Background -->
        <div class="absolute top-0 left-0 right-0 h-8 overflow-hidden">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none" class="h-12 w-full">
                <path fill="#ffffff" d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"></path>
            </svg>
        </div>

        <!-- Circuit patterns -->
        <div class="circuit-pattern absolute inset-0 opacity-5"></div>
        
        <!-- Blob elements -->
        <div class="absolute bottom-0 right-0 w-80 h-80 blob-small opacity-5 animate-blob" style="animation-delay: -3s;"></div>

        <div class="container mx-auto px-4 relative z-10">
            <div class="grid md:grid-cols-4 gap-8 mb-8">
                <div class="animate-on-scroll">
                    <div class="flex items-center mb-4">
                        <img src="hl8vl2wwz1.svg" alt="Logo" class="h-10 w-auto object-contain">
                        <span class="text-xl font-bold ml-2">Wellora</span>
                    </div>
                    <p class="text-gray-400">Transforming healthcare with AI-powered conversation analysis.</p>
                    <div class="mt-6">
                        <a href="#" class="btn-primary inline-block px-4 py-2 rounded-full gradient-bg text-white font-medium hover:shadow-lg transition-all">Get Started</a>
                    </div>
                </div>
                <div class="animate-on-scroll">
                    <h3 class="text-lg font-semibold mb-6">Quick Links</h3>
                    <ul class="space-y-3">
                        <li><a href="index.html#features" class="text-gray-400 hover:text-white transition-colors hover:translate-x-1 flex items-center"><i class="fas fa-chevron-right text-xs mr-2"></i> Features</a></li>
                        <li><a href="index.html#benefits" class="text-gray-400 hover:text-white transition-colors hover:translate-x-1 flex items-center"><i class="fas fa-chevron-right text-xs mr-2"></i> Benefits</a></li>
                        <li><a href="index.html#about" class="text-gray-400 hover:text-white transition-colors hover:translate-x-1 flex items-center"><i class="fas fa-chevron-right text-xs mr-2"></i> About</a></li>
                        <li><a href="index.html#how-it-works" class="text-gray-400 hover:text-white transition-colors hover:translate-x-1 flex items-center"><i class="fas fa-chevron-right text-xs mr-2"></i> How It Works</a></li>
                        <li><a href="index.html#contact" class="text-gray-400 hover:text-white transition-colors hover:translate-x-1 flex items-center"><i class="fas fa-chevron-right text-xs mr-2"></i> Contact</a></li>
                    </ul>
                </div>
                <div class="animate-on-scroll">
                    <h3 class="text-lg font-semibold mb-6">Resources</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors hover:translate-x-1 flex items-center"><i class="fas fa-chevron-right text-xs mr-2"></i> Blog</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors hover:translate-x-1 flex items-center"><i class="fas fa-chevron-right text-xs mr-2"></i> Case Studies</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors hover:translate-x-1 flex items-center"><i class="fas fa-chevron-right text-xs mr-2"></i> Documentation</a></li>
                        <li><a href="help-center.html" class="text-gray-400 hover:text-white transition-colors hover:translate-x-1 flex items-center"><i class="fas fa-chevron-right text-xs mr-2"></i> Help Center</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors hover:translate-x-1 flex items-center"><i class="fas fa-chevron-right text-xs mr-2"></i> API</a></li>
                    </ul>
                </div>
                <div class="animate-on-scroll">
                    <h3 class="text-lg font-semibold mb-6">Legal</h3>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors hover:translate-x-1 flex items-center"><i class="fas fa-chevron-right text-xs mr-2"></i> Legal</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors hover:translate-x-1 flex items-center"><i class="fas fa-chevron-right text-xs mr-2"></i> Terms of Service</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors hover:translate-x-1 flex items-center"><i class="fas fa-chevron-right text-xs mr-2"></i> Privacy Policy</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors hover:translate-x-1 flex items-center"><i class="fas fa-chevron-right text-xs mr-2"></i> HIPAA Compliance</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-white transition-colors hover:translate-x-1 flex items-center"><i class="fas fa-chevron-right text-xs mr-2"></i> Data Security</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 pt-8 text-center">
                <p class="text-gray-400">&copy; 2024 All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Modals -->
    <div id="loginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white/95 backdrop-blur-md rounded-xl p-8 max-w-md w-full transform transition-all scale-95 opacity-0 animate-fade-in modal-content shadow-xl border border-blue-100">
            <!-- Background elements -->
            <div class="absolute inset-0 overflow-hidden rounded-xl z-0">
                <div class="absolute top-0 right-0 w-64 h-64 rounded-full bg-primary/5 blur-3xl"></div>
                <div class="absolute bottom-0 left-0 w-64 h-64 rounded-full bg-secondary/5 blur-3xl"></div>
            </div>
            
            <div class="relative z-10">
                <!-- Header with icon -->
                <div class="flex justify-between items-center mb-8">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-3">
                            <i class="fas fa-user-circle text-primary text-xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-dark">Login for Registered Users</h2>
                    </div>
                    <button onclick="closeModal('loginModal')" class="text-gray-500 hover:text-primary transition-colors bg-gray-100 hover:bg-gray-200 rounded-full w-8 h-8 flex items-center justify-center">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form id="loginForm" class="space-y-6">
                    <div class="space-y-2">
                        <label for="loginEmail" class="block text-gray-700 font-medium">Email Address</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                            <input type="email" id="loginEmail" name="email" required class="w-full pl-10 px-4 py-3 bg-gray-50 border-0 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white shadow-sm transition-all" placeholder="<EMAIL>">
                        </div>
                    </div>
                    
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <label for="loginPassword" class="block text-gray-700 font-medium">Password</label>
                            <a href="#" class="text-sm text-primary hover:underline">Forgot password?</a>
                        </div>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input type="password" id="loginPassword" name="password" required class="w-full pl-10 px-4 py-3 bg-gray-50 border-0 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white shadow-sm transition-all" placeholder="Your password">
                        </div>
                    </div>
                    
                    <div>
                        <button type="submit" class="w-full px-6 py-3 rounded-lg gradient-bg text-white font-medium hover:shadow-lg transition-all flex items-center justify-center">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Sign In to Your Account
                        </button>
                    </div>
                </form>
                
                <div class="mt-6 pt-6 border-t border-gray-100 text-center">
                    <p class="text-gray-600 text-sm">Don't have an account? <a href="#" class="text-primary font-medium hover:underline">Request access</a></p>
                </div>
            </div>
        </div>
    </div>

    <div id="demoModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl p-8 max-w-md w-full transform transition-all scale-95 opacity-0 animate-fade-in modal-content">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold">Request a Demo</h2>
                <button onclick="closeModal('demoModal')" class="text-gray-500 hover:text-gray-700 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <form id="demoForm">
                <div class="mb-4">
                    <label for="demoName" class="block text-gray-700 mb-2 font-medium">Full Name</label>
                    <input type="text" id="demoName" name="name" required class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                </div>
                <div class="mb-4">
                    <label for="demoEmail" class="block text-gray-700 mb-2 font-medium">Email</label>
                    <input type="email" id="demoEmail" name="email" required class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                </div>
                <div class="mb-4">
                    <label for="demoOrganization" class="block text-gray-700 mb-2 font-medium">Organization</label>
                    <input type="text" id="demoOrganization" name="organization" required class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                </div>
                <div class="mb-4">
                    <label for="demoPhone" class="block text-gray-700 mb-2 font-medium">Phone</label>
                    <input type="tel" id="demoPhone" name="phone" required class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all">
                </div>
                <div class="mb-6">
                    <label for="demoMessage" class="block text-gray-700 mb-2 font-medium">What are you most interested in?</label>
                    <textarea id="demoMessage" name="message" rows="3" class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all"></textarea>
                </div>
                <button type="submit" class="w-full px-6 py-3 rounded-full gradient-bg text-white font-medium hover:shadow-md transition-all">Request Demo</button>
            </form>
        </div>
    </div>
    
    <!-- Specifications Modal -->
    <div id="specsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white/80 backdrop-blur-md rounded-xl p-8 max-w-3xl w-full transform transition-all scale-95 opacity-0 animate-fade-in modal-content shadow-xl border border-blue-100 relative">
            <!-- Neural network animation background -->
            <style>
                /* Enhanced Neural Network Animation Styles */
                .modal-neural-network {
                    position: absolute;
                    inset: 0;
                    overflow: hidden;
                    z-index: 0;
                }
                
                .modal-neural-node {
                    position: absolute;
                    width: 8px;
                    height: 8px;
                    background: linear-gradient(to right, rgba(15, 111, 255, 0.8), rgba(0, 176, 182, 0.8));
                    border-radius: 50%;
                    filter: drop-shadow(0 0 5px rgba(15, 111, 255, 0.5));
                    z-index: 1;
                    animation: nodeGlow 3s ease-in-out infinite;
                }
                
                .modal-neural-connection {
                    position: absolute;
                    height: 2px;
                    background: linear-gradient(to right, rgba(15, 111, 255, 0.6), rgba(0, 214, 149, 0.6));
                    z-index: 1;
                    transform-origin: left center;
                    animation: connectionPulse 4s linear infinite;
                }
                
                /* Node glow animation */
                @keyframes nodeGlow {
                    0%, 100% { transform: scale(1); opacity: 0.8; }
                    50% { transform: scale(1.3); opacity: 1; }
                }
                
                /* Connection pulse animation */
                @keyframes connectionPulse {
                    0%, 100% { opacity: 0.4; }
                    50% { opacity: 0.8; }
                }
                
                /* Data pulse animation */
                @keyframes dataPulse {
                    0% { transform: translateX(0) scale(0.8); opacity: 1; }
                    100% { transform: translateX(100%) scale(1.2); opacity: 0; }
                }
                
                /* Neural network data pulses */
                .data-pulse {
                    position: absolute;
                    width: 6px;
                    height: 6px;
                    border-radius: 50%;
                    background-color: rgba(255, 255, 255, 0.9);
                    filter: drop-shadow(0 0 5px rgba(59, 130, 246, 0.7));
                    animation: dataPulse 3s linear infinite;
                    opacity: 0.8;
                }
            </style>
            
            <div class="modal-neural-network">
                <!-- Neural nodes with enhanced visibility and glow effects -->
                <div class="modal-neural-node" style="top: 15%; left: 10%"></div>
                <div class="modal-neural-node" style="top: 25%; left: 35%"></div>
                <div class="modal-neural-node" style="top: 45%; left: 25%"></div>
                <div class="modal-neural-node" style="top: 65%; left: 15%"></div>
                <div class="modal-neural-node" style="top: 75%; left: 40%"></div>
                <div class="modal-neural-node" style="top: 20%; left: 60%"></div>
                <div class="modal-neural-node" style="top: 40%; left: 80%"></div>
                <div class="modal-neural-node" style="top: 60%; left: 70%"></div>
                <div class="modal-neural-node" style="top: 80%; left: 85%"></div>
                <div class="modal-neural-node" style="top: 30%; left: 50%"></div>
                <div class="modal-neural-node" style="top: 55%; left: 45%"></div>
                <div class="modal-neural-node" style="top: 35%; left: 75%"></div>
                <div class="modal-neural-node" style="top: 70%; left: 60%"></div>
                <div class="modal-neural-node" style="top: 85%; left: 30%"></div>
                <div class="modal-neural-node" style="top: 10%; left: 65%"></div>
                
                <!-- Enhanced neural connections with glowing effects -->
                <div class="modal-neural-connection" style="top: 15%; left: 10%; width: 180px; transform: rotate(15deg);"></div>
                <div class="modal-neural-connection" style="top: 25%; left: 35%; width: 100px; transform: rotate(-25deg);"></div>
                <div class="modal-neural-connection" style="top: 45%; left: 25%; width: 160px; transform: rotate(35deg);"></div>
                <div class="modal-neural-connection" style="top: 20%; left: 60%; width: 130px; transform: rotate(45deg);"></div>
                <div class="modal-neural-connection" style="top: 60%; left: 70%; width: 100px; transform: rotate(-15deg);"></div>
                <div class="modal-neural-connection" style="top: 40%; left: 80%; width: 120px; transform: rotate(-35deg);"></div>
                <div class="modal-neural-connection" style="top: 30%; left: 50%; width: 150px; transform: rotate(20deg);"></div>
                <div class="modal-neural-connection" style="top: 55%; left: 45%; width: 110px; transform: rotate(-10deg);"></div>
                <div class="modal-neural-connection" style="top: 70%; left: 60%; width: 140px; transform: rotate(5deg);"></div>
                <div class="modal-neural-connection" style="top: 85%; left: 30%; width: 160px; transform: rotate(-30deg);"></div>
                
                <!-- Data pulses moving along the connections -->
                <div class="data-pulse" style="top: 15%; left: 10%; animation-delay: 0s"></div>
                <div class="data-pulse" style="top: 45%; left: 25%; animation-delay: 0.5s"></div>
                <div class="data-pulse" style="top: 60%; left: 70%; animation-delay: 1s"></div>
                <div class="data-pulse" style="top: 30%; left: 50%; animation-delay: 1.5s"></div>
                <div class="data-pulse" style="top: 70%; left: 60%; animation-delay: 2s"></div>
                <div class="data-pulse" style="top: 20%; left: 60%; animation-delay: 2.5s"></div>
            </div>
            
            <!-- Phone scanning QR code animation -->
            <div class="phone-scan-animation">
                <div class="qr-placeholder">
                    <div class="qr-pattern"></div>
                </div>
                <div class="phone-device" id="phoneAnimation">
                    <div class="phone-screen">
                        <div class="phone-notch"></div>
                        <div class="qr-code"></div>
                        <div class="scan-beam"></div>
                        <div class="recording-dot"></div>
                        <div class="audio-waves">
                            <div class="audio-wave-bar"></div>
                            <div class="audio-wave-bar"></div>
                            <div class="audio-wave-bar"></div>
                            <div class="audio-wave-bar"></div>
                            <div class="audio-wave-bar"></div>
                            <div class="audio-wave-bar"></div>
                            <div class="audio-wave-bar"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-between items-center mb-6 pb-4 border-b border-blue-100 relative z-10">
                <div class="flex items-center">
                    <div class="mr-6 p-2 bg-blue-50/70 backdrop-blur-sm rounded-lg border border-blue-100 shadow-sm">
                        <i class="fas fa-microphone text-primary text-4xl"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-dark">
                            <span class="gradient-text" data-i18n="specsModalTitle">Voice Recording Specifications</span>
                        </h2>
                        <p class="text-gray-600 text-sm mt-1" data-i18n="specsModalSubtitle">Technical details about our recording system</p>
                    </div>
                </div>
                <button onclick="closeModal('specsModal')" class="text-gray-500 hover:text-primary transition-colors bg-blue-50/70 backdrop-blur-sm rounded-full w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- Specifications Cards with interactive effects -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 relative z-10">
                <!-- Mobile Device Recording -->
                <div class="transition-all duration-300 transform hover:scale-105 hover:shadow-lg bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-xl p-6 overflow-hidden relative group">
                    <!-- Animated background effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    
                    <!-- Floating particles -->
                    <div class="absolute w-3 h-3 rounded-full bg-primary/10 top-1/4 right-1/4 opacity-0 group-hover:opacity-100 transition-opacity animate-float" style="animation-delay: -1s;"></div>
                    <div class="absolute w-2 h-2 rounded-full bg-secondary/10 bottom-1/3 left-1/4 opacity-0 group-hover:opacity-100 transition-opacity animate-float" style="animation-delay: -2.5s;"></div>
                    
                    <div class="flex items-start">
                        <div class="bg-primary/10 group-hover:bg-primary/20 transition-all duration-300 rounded-full p-3 mr-4 flex-shrink-0 relative overflow-hidden">
                            <div class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-r from-primary/10 to-transparent animate-pulse-slow rounded-full"></div>
                            <i class="fas fa-mobile-alt text-primary text-xl relative z-10"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-2 group-hover:text-primary transition-colors" data-i18n="mobileDeviceRecording">Mobile Device Recording</h3>
                            <p class="text-gray-300 mb-3" data-i18n="mobileDeviceRecordingDesc">Record sessions using any mobile device by connecting to your Wellora account:</p>
                            <ul class="space-y-2 text-gray-600">
                                <li class="flex items-start group-hover:translate-x-1 transition-transform duration-300">
                                    <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                    <span data-i18n="mobileFeature1">Unique QR code generated for each session</span>
                                </li>
                                <li class="flex items-start group-hover:translate-x-1 transition-transform duration-300" style="transition-delay: 50ms;">
                                    <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                    <span data-i18n="mobileFeature2">Secure, end-to-end encrypted data transmission</span>
                                </li>
                                <li class="flex items-start group-hover:translate-x-1 transition-transform duration-300" style="transition-delay: 100ms;">
                                    <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                    <span data-i18n="mobileFeature3">Immediately available in your dashboard</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- Tablet & External Mics -->
                <div class="transition-all duration-300 transform hover:scale-105 hover:shadow-lg bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-xl p-6 overflow-hidden relative group">
                    <!-- Animated background effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-secondary/5 to-tertiary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    
                    <div class="flex items-start">
                        <div class="bg-secondary/10 group-hover:bg-secondary/20 transition-all duration-300 rounded-full p-3 mr-4 flex-shrink-0 relative overflow-hidden">
                            <div class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-r from-secondary/10 to-transparent animate-pulse-slow rounded-full"></div>
                            <i class="fas fa-tablet-alt text-secondary text-xl relative z-10"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-2 group-hover:text-secondary transition-colors" data-i18n="multipleDeviceSupport">Multiple Device Support</h3>
                            <p class="text-gray-300 mb-3" data-i18n="multipleDeviceSupportDesc">Flexible recording options for any situation:</p>
                            <ul class="space-y-2 text-gray-300">
                                <li class="flex items-start group-hover:translate-x-1 transition-transform duration-300">
                                    <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                    <span data-i18n="multipleFeature1">iPad/tablet recording with QR code connection</span>
                                </li>
                                <li class="flex items-start group-hover:translate-x-1 transition-transform duration-300" style="transition-delay: 50ms;">
                                    <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                    <span data-i18n="multipleFeature2">External microphone support for enhanced quality</span>
                                </li>
                                <li class="flex items-start group-hover:translate-x-1 transition-transform duration-300" style="transition-delay: 100ms;">
                                    <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                    <span data-i18n="multipleFeature3">Compatible with USB and Bluetooth microphones</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- Audio Quality -->
                <div class="transition-all duration-300 transform hover:scale-105 hover:shadow-lg bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-xl p-6 overflow-hidden relative group">
                    <!-- Sound wave animation that appears on hover -->
                    <div class="absolute bottom-0 left-0 right-0 h-12 opacity-0 group-hover:opacity-70 transition-opacity duration-300">
                        <div class="flex items-end justify-around h-full px-4">
                            <div class="bg-primary/60 w-1 rounded-t animate-pulse-slow h-1/3" style="animation-duration: 0.9s;"></div>
                            <div class="bg-primary/60 w-1 rounded-t animate-pulse-slow h-2/3" style="animation-duration: 0.8s;"></div>
                            <div class="bg-primary/60 w-1 rounded-t animate-pulse-slow h-full" style="animation-duration: 0.7s;"></div>
                            <div class="bg-primary/60 w-1 rounded-t animate-pulse-slow h-2/3" style="animation-duration: 0.6s;"></div>
                            <div class="bg-primary/60 w-1 rounded-t animate-pulse-slow h-1/3" style="animation-duration: 1.0s;"></div>
                            <div class="bg-primary/60 w-1 rounded-t animate-pulse-slow h-1/2" style="animation-duration: 0.7s;"></div>
                            <div class="bg-primary/60 w-1 rounded-t animate-pulse-slow h-3/4" style="animation-duration: 0.8s;"></div>
                            <div class="bg-primary/60 w-1 rounded-t animate-pulse-slow h-1/2" style="animation-duration: 0.9s;"></div>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="bg-tertiary/10 group-hover:bg-tertiary/20 transition-all duration-300 rounded-full p-3 mr-4 flex-shrink-0">
                            <i class="fas fa-volume-up text-tertiary text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-2 group-hover:text-tertiary transition-colors">Studio-Quality Audio</h3>
                            <p class="text-gray-300 mb-3">Optimized for precision AI analysis:</p>
                            <ul class="space-y-2 text-gray-300">
                                <li class="flex items-start group-hover:translate-x-1 transition-transform duration-300">
                                    <i class="fas fa-check-circle text-tertiary mt-1.5 mr-2"></i>
                                    <div>
                                        <span class="font-medium">High-definition 48kHz/24-bit recording</span>
                                        <p class="text-xs text-gray-500 mt-0.5">Professional studio-quality standard</p>
                                    </div>
                                </li>
                                <li class="flex items-start group-hover:translate-x-1 transition-transform duration-300" style="transition-delay: 50ms;">
                                    <i class="fas fa-check-circle text-tertiary mt-1.5 mr-2"></i>
                                    <div>
                                        <span class="font-medium">Active noise suppression</span>
                                        <p class="text-xs text-gray-500 mt-0.5">Removes background noise automatically</p>
                                    </div>
                                </li>
                                <li class="flex items-start group-hover:translate-x-1 transition-transform duration-300" style="transition-delay: 100ms;">
                                    <i class="fas fa-check-circle text-tertiary mt-1.5 mr-2"></i>
                                    <div>
                                        <span class="font-medium">Advanced echo cancellation</span>
                                        <p class="text-xs text-gray-500 mt-0.5">Clear dialogue regardless of environment</p>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- AI Optimization -->
                <div class="transition-all duration-300 transform hover:scale-105 hover:shadow-lg bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-sm rounded-xl p-6 overflow-hidden relative group">
                    <!-- Brain neural network animation that appears on hover -->
                    <div class="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-300">
                        <div class="absolute w-2 h-2 rounded-full bg-primary/80 top-1/4 left-1/4 animate-pulse-slow"></div>
                        <div class="absolute w-2 h-2 rounded-full bg-primary/80 bottom-1/3 right-1/3 animate-pulse-slow" style="animation-delay: 0.5s"></div>
                        <div class="absolute w-2 h-2 rounded-full bg-primary/80 top-1/2 right-1/4 animate-pulse-slow" style="animation-delay: 1s"></div>
                        <div class="absolute h-px w-20 bg-primary/40 top-1/4 left-1/4 rotate-30" style="transform-origin: left center;"></div>
                        <div class="absolute h-px w-32 bg-primary/40 bottom-1/3 right-1/3 -rotate-15" style="transform-origin: right center;"></div>
                        <div class="absolute h-px w-24 bg-primary/40 top-1/2 right-1/4 rotate-15" style="transform-origin: right center;"></div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="bg-primary/10 group-hover:bg-primary/20 transition-all duration-300 rounded-full p-3 mr-4 flex-shrink-0">
                            <i class="fas fa-brain text-primary text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-2 group-hover:text-primary transition-colors">AI-Optimized Processing</h3>
                            <p class="text-gray-300 mb-3">Enhanced for maximum effectiveness:</p>
                            <div class="grid grid-cols-1 gap-3">
                                <div class="bg-gray-700 group-hover:bg-gray-600 transition-all duration-200 rounded-md p-2 shadow-sm">
                                    <div class="flex items-start">
                                        <i class="fas fa-check-circle text-tertiary mt-0.5 mr-2"></i>
                                        <span class="font-medium text-gray-300">Advanced audio preprocessing pipeline</span>
                                    </div>
                                </div>
                                <div class="bg-gray-700 group-hover:bg-gray-600 transition-all duration-200 rounded-md p-2 shadow-sm" style="transition-delay: 50ms;">
                                    <div class="flex items-start">
                                        <i class="fas fa-check-circle text-tertiary mt-0.5 mr-2"></i>
                                        <span class="font-medium text-gray-300">Speaker separation technology</span>
                                    </div>
                                </div>
                                <div class="bg-gray-700 group-hover:bg-gray-600 transition-all duration-200 rounded-md p-2 shadow-sm" style="transition-delay: 100ms;">
                                    <div class="flex items-start">
                                        <i class="fas fa-check-circle text-tertiary mt-0.5 mr-2"></i>
                                        <span class="font-medium text-gray-300">Real-time quality monitoring</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-8 pt-6 border-t border-blue-100 text-center relative z-10">
                <button class="px-6 py-3 rounded-full border-2 border-primary text-primary font-medium hover:bg-primary hover:text-white transition-all" onclick="closeModal('specsModal')" data-i18n="closeSpecifications">
                    Close Specifications
                </button>
            </div>
        </div>
    </div>

    <!-- Audio Quality Modal -->
    <div id="audioQualityModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white/95 backdrop-blur-md rounded-xl p-8 max-w-3xl w-full transform transition-all scale-95 opacity-0 animate-fade-in modal-content shadow-xl border border-blue-100 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6 pb-4 border-b border-blue-100">
                <div class="flex items-center">
                    <div class="mr-6 p-2 bg-blue-50 rounded-lg border border-blue-100 shadow-sm relative overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-100 to-transparent animate-pulse-slow rounded-lg opacity-70"></div>
                        <i class="fas fa-wave-square text-primary text-4xl"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-dark">
                            <span class="gradient-text">High-Fidelity Audio Technology</span>
                </h2>
                        <p class="text-gray-600 text-sm mt-1">Studio-quality recording optimized for AI analysis</p>
                    </div>
                </div>
                <button onclick="closeModal('audioQualityModal')" class="text-gray-500 hover:text-primary transition-colors bg-blue-50 rounded-full w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- Simple static illustration without animation -->
            <div class="mb-8 bg-blue-50 rounded-xl p-6">
                <div class="flex justify-center mb-6">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-filter text-primary text-2xl"></i>
                    </div>
                </div>
                
                <div class="text-center mb-4">
                    <h3 class="text-lg font-medium text-dark">AI-Powered Noise Filtering</h3>
                    <p class="text-sm text-gray-600 mt-2">Our advanced technology removes background noise while preserving speech clarity</p>
                </div>
                
                <!-- Quality badges -->
                <div class="flex justify-center space-x-4 mt-4">
                    <span class="text-xs font-medium text-primary bg-white/80 backdrop-blur-sm px-3 py-1.5 rounded-md shadow-sm border border-blue-100">
                        <i class="fas fa-check-circle mr-1"></i>
                        Crystal Clear Audio
                    </span>
                    <span class="text-xs font-medium text-primary bg-white/80 backdrop-blur-sm px-3 py-1.5 rounded-md shadow-sm border border-blue-100">
                        <i class="fas fa-check-circle mr-1"></i>
                        Studio Quality
                    </span>
                            </div>
                        </div>
                        
            <style>
                @keyframes moveLeftToRight {
                    0% { transform: translateX(-50%); }
                    100% { transform: translateX(50%); }
                }
                @keyframes moveRightToLeft {
                    0% { transform: translateX(50%); }
                    100% { transform: translateX(-50%); }
                }
            </style>
            
            <!-- Content sections -->
            <div class="space-y-6">
                <div class="bg-white rounded-lg p-6 shadow-sm border border-blue-50">
                    <h3 class="text-lg font-semibold text-dark mb-3 flex items-center">
                        <i class="fas fa-microphone-alt text-primary mr-2"></i>
                        Professional Audio Specifications
                    </h3>
                    <p class="text-gray-600 mb-4">
                        Wellora uses 48kHz 24-bit audio recording - the same specifications used in professional recording studios and film production. This delivers:
                    </p>
                    <ul class="space-y-2 ml-6">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                            <div>
                                <span class="font-medium text-dark">Wider frequency range</span>
                                <p class="text-sm text-gray-600">Captures subtle nuances in voice from 20Hz to 22kHz</p>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                            <div>
                                <span class="font-medium text-dark">Higher dynamic range</span>
                                <p class="text-sm text-gray-600">144dB dynamic range eliminates audio clipping and distortion</p>
                            </div>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                            <div>
                                <span class="font-medium text-dark">Greater bit depth</span>
                                <p class="text-sm text-gray-600">24-bit depth provides 16,777,216 possible amplitude values</p>
                            </div>
                        </li>
                    </ul>
                        </div>
                        
                <div class="bg-white rounded-lg p-6 shadow-sm border border-blue-50">
                    <h3 class="text-lg font-semibold text-dark mb-3 flex items-center">
                        <i class="fas fa-filter text-primary mr-2"></i>
                        Advanced Noise Reduction
                    </h3>
                    <p class="text-gray-600 mb-4">
                        Our proprietary AI-powered noise reduction technology ensures pristine audio quality by:
                    </p>
                    <div class="grid md:grid-cols-2 gap-4">
                        <div class="bg-blue-50/50 p-3 rounded-lg">
                            <h4 class="text-md font-medium text-dark mb-1">Adaptive Noise Filtering</h4>
                            <p class="text-sm text-gray-600">Automatically identifies and removes background sounds while preserving speech</p>
                        </div>
                        <div class="bg-blue-50/50 p-3 rounded-lg">
                            <h4 class="text-md font-medium text-dark mb-1">Echo Cancellation</h4>
                            <p class="text-sm text-gray-600">Eliminates room resonance and echo effects for clear dialogue</p>
                        </div>
                        <div class="bg-blue-50/50 p-3 rounded-lg">
                            <h4 class="text-md font-medium text-dark mb-1">Spectral Gating</h4>
                            <p class="text-sm text-gray-600">Reduces continuous background noise like fans, air conditioners, and ambient hum</p>
                        </div>
                        <div class="bg-blue-50/50 p-3 rounded-lg">
                            <h4 class="text-md font-medium text-dark mb-1">Transient Noise Reduction</h4>
                            <p class="text-sm text-gray-600">Detects and removes sudden noises like door slams and keyboard clicks</p>
                        </div>
                    </div>
                        </div>
                        
                <div class="bg-gradient-to-r from-gray-800/90 to-gray-900/90 rounded-lg p-6 shadow-sm border border-gray-700">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 bg-gray-700/80 rounded-full p-3 shadow-sm mr-4">
                            <i class="fas fa-brain text-primary text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white mb-2">AI-Optimized Audio</h3>
                            <p class="text-gray-400">
                                The superior audio quality isn't just for human ears - it's specifically optimized for AI analysis. Higher quality recordings provide our AI algorithms with more data points for analysis, resulting in more accurate insights, better pattern recognition, and more detailed therapeutic recommendations.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-8 pt-6 border-t border-blue-100 text-center">
                <button class="px-6 py-3 rounded-full border-2 border-primary text-primary font-medium hover:bg-primary hover:text-white transition-all" onclick="closeModal('audioQualityModal')">
                    Close
                </button>
            </div>
        </div>
    </div>

    <!-- Filtering Modal -->
    <div id="filteringModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white/95 backdrop-blur-md rounded-xl p-8 max-w-3xl w-full transform transition-all scale-95 opacity-0 animate-fade-in modal-content shadow-xl border border-blue-100 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6 pb-4 border-b border-blue-100">
                <div class="flex items-center">
                    <div class="mr-6 p-2 bg-blue-50 rounded-lg border border-blue-100 shadow-sm relative overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-100 to-transparent animate-pulse-slow rounded-lg opacity-70"></div>
                        <i class="fas fa-filter text-primary text-4xl"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-dark">
                            <span class="gradient-text">Intelligent Noise Filtering</span>
                        </h2>
                        <p class="text-gray-600 text-sm mt-1">Advanced technology for pristine audio clarity</p>
                    </div>
                </div>
                <button onclick="closeModal('filteringModal')" class="text-gray-500 hover:text-primary transition-colors bg-blue-50 rounded-full w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- Animated filtering visualization with SVG -->
            <div class="mb-8 bg-gray-800/80 rounded-xl p-6 border border-gray-700">
                <div class="text-center mb-4">
                    <h3 class="text-lg font-medium text-white">Intelligent Audio Filtering</h3>
                    <p class="text-sm text-gray-400 mt-1 mb-4">Our advanced technology removes background noise while preserving speech clarity</p>
                </div>
                
                <!-- SVG animation of noise filtering process -->
                <div class="relative h-56 w-full overflow-hidden">
                    <!-- Background glow effects -->
                    <div class="absolute inset-0 flex justify-center">
                        <div class="w-2/3 h-16 bg-blue-500/10 blur-xl rounded-full"></div>
                    </div>
                    
                    <!-- Main SVG Animation -->
                    <svg width="100%" height="100%" viewBox="0 0 400 200" class="filter-animation-svg">
                        <!-- Definitions for gradients and filters -->
                        <defs>
                            <!-- Gradient for noisy wave -->
                            <linearGradient id="noiseGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" stop-color="#ef4444" stop-opacity="0.7" />
                                <stop offset="100%" stop-color="#f87171" stop-opacity="0.7" />
                            </linearGradient>
                            
                            <!-- Gradient for clean wave -->
                            <linearGradient id="cleanGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" stop-color="#10b981" stop-opacity="0.7" />
                                <stop offset="100%" stop-color="#34d399" stop-opacity="0.7" />
                            </linearGradient>
                            
                            <!-- Filter line gradient -->
                            <linearGradient id="filterGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                <stop offset="0%" stop-color="#3b82f6" stop-opacity="0.3" />
                                <stop offset="50%" stop-color="#3b82f6" stop-opacity="0.9" />
                                <stop offset="100%" stop-color="#3b82f6" stop-opacity="0.3" />
                            </linearGradient>
                        </defs>
                        
                        <!-- Top section: Noisy Audio -->
                        <g transform="translate(0, 50)">
                            <!-- Label -->
                            <text x="20" y="-25" fill="#94a3b8" font-size="12">Original Audio (with noise)</text>
                            
                            <!-- Noisy waveform path -->
                            <path class="noise-wave" d="M0,0 Q13,30 25,-20 Q38,15 50,-25 Q63,20 75,-15 Q88,25 100,-10 Q113,30 125,-20 Q138,15 150,-25 Q163,20 175,-15 Q188,25 200,-10 Q213,30 225,-20 Q238,15 250,-25 Q263,20 275,-15 Q288,25 300,-10 Q313,30 325,-20 Q338,15 350,-25 Q363,20 375,-15 Q388,25 400,-10" 
                                  fill="none" 
                                  stroke="url(#noiseGradient)" 
                                  stroke-width="2">
                                <animate attributeName="d" 
                                         values="M0,0 Q13,30 25,-20 Q38,15 50,-25 Q63,20 75,-15 Q88,25 100,-10 Q113,30 125,-20 Q138,15 150,-25 Q163,20 175,-15 Q188,25 200,-10 Q213,30 225,-20 Q238,15 250,-25 Q263,20 275,-15 Q288,25 300,-10 Q313,30 325,-20 Q338,15 350,-25 Q363,20 375,-15 Q388,25 400,-10;
                                         
                                                M0,0 Q13,25 25,-25 Q38,20 50,-20 Q63,15 75,-20 Q88,20 100,-15 Q113,25 125,-25 Q138,20 150,-20 Q163,15 175,-20 Q188,20 200,-15 Q213,25 225,-25 Q238,20 250,-20 Q263,15 275,-20 Q288,20 300,-15 Q313,25 325,-25 Q338,20 350,-20 Q363,15 375,-20 Q388,20 400,-15;
                                                
                                                M0,0 Q13,30 25,-20 Q38,15 50,-25 Q63,20 75,-15 Q88,25 100,-10 Q113,30 125,-20 Q138,15 150,-25 Q163,20 175,-15 Q188,25 200,-10 Q213,30 225,-20 Q238,15 250,-25 Q263,20 275,-15 Q288,25 300,-10 Q313,30 325,-20 Q338,15 350,-25 Q363,20 375,-15 Q388,25 400,-10" 
                                         dur="10s" 
                                         repeatCount="indefinite" />
                            </path>
                            
                            <!-- Noise particles -->
                            <circle class="noise-particle" cx="30" cy="-10" r="2" fill="#ef4444" opacity="0.7">
                                <animate attributeName="opacity" values="0.7;0;0.7" dur="3s" repeatCount="indefinite" />
                                <animate attributeName="cy" values="-10;10;-10" dur="2s" repeatCount="indefinite" />
                            </circle>
                            <circle class="noise-particle" cx="120" cy="15" r="2" fill="#ef4444" opacity="0.5">
                                <animate attributeName="opacity" values="0.5;0;0.5" dur="2.5s" repeatCount="indefinite" />
                                <animate attributeName="cy" values="15;-5;15" dur="3s" repeatCount="indefinite" />
                            </circle>
                            <circle class="noise-particle" cx="220" cy="-15" r="2" fill="#ef4444" opacity="0.6">
                                <animate attributeName="opacity" values="0.6;0;0.6" dur="2.7s" repeatCount="indefinite" />
                                <animate attributeName="cy" values="-15;0;-15" dur="2.7s" repeatCount="indefinite" />
                            </circle>
                            <circle class="noise-particle" cx="320" cy="10" r="2" fill="#ef4444" opacity="0.8">
                                <animate attributeName="opacity" values="0.8;0.1;0.8" dur="2.2s" repeatCount="indefinite" />
                                <animate attributeName="cy" values="10;-10;10" dur="3.2s" repeatCount="indefinite" />
                            </circle>
                        </g>
                        
                        <!-- Middle section: Filtering Process -->
                        <g transform="translate(0, 100)">
                            <!-- Filter line -->
                            <line x1="0" y1="0" x2="400" y2="0" 
                                  stroke="url(#filterGradient)" 
                                  stroke-width="3" 
                                  stroke-dasharray="5,3">
                                <animate attributeName="stroke-dashoffset" 
                                         values="0;-16" 
                                         dur="1s" 
                                         repeatCount="indefinite" />
                            </line>
                            
                            <!-- Filter label -->
                            <rect x="170" y="-12" width="60" height="20" rx="10" 
                                  fill="#1e293b" stroke="#3b82f6" stroke-width="1" />
                            <text x="200" y="2" fill="#3b82f6" font-size="12" 
                                  text-anchor="middle" dominant-baseline="middle">FILTER</text>
                            
                            <!-- Animated particles being filtered -->
                            <g class="filter-particles">
                                <circle cx="75" cy="0" r="2" fill="#ef4444">
                                    <animate attributeName="cy" values="0;20;40" dur="1s" begin="0.2s" repeatCount="indefinite" />
                                    <animate attributeName="opacity" values="1;0" dur="1s" begin="0.2s" repeatCount="indefinite" />
                                </circle>
                                <circle cx="150" cy="0" r="2" fill="#ef4444">
                                    <animate attributeName="cy" values="0;20;40" dur="1s" begin="0.5s" repeatCount="indefinite" />
                                    <animate attributeName="opacity" values="1;0" dur="1s" begin="0.5s" repeatCount="indefinite" />
                                </circle>
                                <circle cx="225" cy="0" r="2" fill="#ef4444">
                                    <animate attributeName="cy" values="0;20;40" dur="1s" begin="0.8s" repeatCount="indefinite" />
                                    <animate attributeName="opacity" values="1;0" dur="1s" begin="0.8s" repeatCount="indefinite" />
                                </circle>
                                <circle cx="300" cy="0" r="2" fill="#ef4444">
                                    <animate attributeName="cy" values="0;20;40" dur="1s" begin="1.1s" repeatCount="indefinite" />
                                    <animate attributeName="opacity" values="1;0" dur="1s" begin="1.1s" repeatCount="indefinite" />
                                </circle>
                            </g>
                        </g>
                        
                        <!-- Bottom section: Clean Audio -->
                        <g transform="translate(0, 150)">
                            <!-- Label -->
                            <text x="20" y="25" fill="#94a3b8" font-size="12">Filtered Audio (clean)</text>
                            
                            <!-- Clean waveform path - simpler, smoother -->
                            <path class="clean-wave" 
                                  d="M0,0 Q50,-15 100,0 Q150,15 200,0 Q250,-15 300,0 Q350,15 400,0" 
                                  fill="none" 
                                  stroke="url(#cleanGradient)" 
                                  stroke-width="2.5">
                                <animate attributeName="d" 
                                         values="M0,0 Q50,-15 100,0 Q150,15 200,0 Q250,-15 300,0 Q350,15 400,0;
                                                M0,0 Q50,-10 100,0 Q150,10 200,0 Q250,-10 300,0 Q350,10 400,0;
                                                M0,0 Q50,-15 100,0 Q150,15 200,0 Q250,-15 300,0 Q350,15 400,0" 
                                         dur="6s" 
                                         repeatCount="indefinite" />
                            </path>
                        </g>
                    </svg>
                    
                    <!-- Processing indicators at bottom -->
                    <div class="absolute bottom-0 left-0 right-0 flex justify-between px-4 pt-2">
                        <div class="text-xs text-red-400">High Noise</div>
                        <div class="text-xs text-green-400">Clean Signal</div>
                    </div>
                </div>
                
                <!-- Technology indicators -->
                <div class="flex justify-center space-x-4 mt-4">
                    <span class="text-xs font-medium text-blue-400 bg-blue-900/20 px-2 py-1 rounded-md">
                        <i class="fas fa-microchip mr-1"></i>
                        AI Processing
                    </span>
                    <span class="text-xs font-medium text-green-400 bg-green-900/20 px-2 py-1 rounded-md">
                        <i class="fas fa-check-circle mr-1"></i>
                        98% Noise Reduction
                    </span>
                </div>
            </div>
            
            <!-- Content sections about noise filtering -->
            <div class="space-y-6">
                <div class="bg-gray-800/90 rounded-lg p-6 shadow-sm border border-gray-700">
                    <h3 class="text-lg font-semibold text-white mb-3 flex items-center">
                        <i class="fas fa-magic text-primary mr-2"></i>
                        How Intelligent Filtering Works
                    </h3>
                    <p class="text-gray-300 mb-4">
                        Wellora's intelligent filtering technology uses a multi-layered approach to separate speech from unwanted noise:
                    </p>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-900/50 flex items-center justify-center mr-3 mt-0.5">
                                <span class="text-primary font-medium">1</span>
                            </div>
                            <div>
                                <h4 class="font-medium text-white mb-1">Spectrum Analysis</h4>
                                <p class="text-sm text-gray-400">Our system continuously analyzes the audio spectrum to identify speech patterns versus noise frequencies.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-900/50 flex items-center justify-center mr-3 mt-0.5">
                                <span class="text-primary font-medium">2</span>
                            </div>
                            <div>
                                <h4 class="font-medium text-white mb-1">Adaptive Filtering</h4>
                                <p class="text-sm text-gray-400">Dynamically adjusts to changing environmental conditions, ensuring consistent noise reduction regardless of your location.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-900/50 flex items-center justify-center mr-3 mt-0.5">
                                <span class="text-primary font-medium">3</span>
                            </div>
                            <div>
                                <h4 class="font-medium text-white mb-1">Speech Preservation</h4>
                                <p class="text-sm text-gray-400">While removing unwanted noise, our algorithm carefully preserves speech characteristics, maintaining natural voice tone and inflection.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-8 h-8 rounded-full bg-blue-900/50 flex items-center justify-center mr-3 mt-0.5">
                                <span class="text-primary font-medium">4</span>
                            </div>
                            <div>
                                <h4 class="font-medium text-white mb-1">Real-time Processing</h4>
                                <p class="text-sm text-gray-400">All filtering happens instantly, with no perceptible delay in the audio recording process.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="bg-gray-800/90 rounded-lg p-6 shadow-sm border border-gray-700">
                        <h3 class="text-lg font-semibold text-white mb-3 flex items-center">
                            <i class="fas fa-ban text-primary mr-2"></i>
                            Noise Types Reduced
                        </h3>
                        <ul class="space-y-2 text-gray-400">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                <span>Background conversations and crosstalk</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                <span>HVAC systems and fan noise</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                <span>Street noise and traffic sounds</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                <span>Office equipment (printers, keyboards, etc.)</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                <span>Room echo and reverberations</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                <span>Unwanted sudden sounds (door closing, etc.)</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="bg-gray-800/90 rounded-lg p-6 shadow-sm border border-gray-700">
                        <h3 class="text-lg font-semibold text-white mb-3 flex items-center">
                            <i class="fas fa-brain text-primary mr-2"></i>
                            AI Benefits
                        </h3>
                        <p class="text-gray-400 mb-3">
                            Clean audio provides significant advantages for AI analysis:
                        </p>
                        <ul class="space-y-2 text-gray-400">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                <span>35% more accurate speech transcription</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                <span>42% improvement in emotional tone detection</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                <span>Better identification of subtle speech patterns</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                <span>Enhanced detection of vocal stress indicators</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-tertiary mt-1 mr-2"></i>
                                <span>Clearer distinction between speakers</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="mt-8 pt-6 border-t border-blue-100 text-center">
                <button class="px-6 py-3 rounded-full border-2 border-primary text-primary font-medium hover:bg-primary hover:text-white transition-all" onclick="closeModal('filteringModal')">
                    Close
                </button>
            </div>
        </div>
    </div>

    <!-- Noise Cancellation Modal -->
    <div id="noiseCancellationModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white/95 backdrop-blur-md rounded-xl p-8 max-w-3xl w-full transform transition-all scale-95 opacity-0 animate-fade-in modal-content shadow-xl border border-blue-100 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6 pb-4 border-b border-blue-100">
                <div class="flex items-center">
                    <div class="mr-6 p-2 bg-blue-50 rounded-lg border border-blue-100 shadow-sm relative overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-100 to-transparent animate-pulse-slow rounded-lg opacity-70"></div>
                        <i class="fas fa-microphone-alt-slash text-primary text-4xl"></i>
</div>
                    <div>
                        <h2 class="text-2xl font-bold text-dark">
                            <span class="gradient-text">Advanced Noise Cancellation</span>
                        </h2>
                        <p class="text-gray-600 text-sm mt-3">Multi-stage technology for distraction-free conversations</p>
                    </div>
                </div>
                <button onclick="closeModal('noiseCancellationModal')" class="text-gray-500 hover:text-primary transition-colors bg-blue-50 rounded-full w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- Content about noise cancellation -->
            <div class="space-y-6">
                <p class="text-gray-600">
                    Designed specifically for healthcare environments, our advanced noise cancellation uses multi-stage technology to create crystal clear conversations without the distractions of background noise.
                </p>
                
                <!-- Animated noise cancellation visualization (original) -->
                <div class="bg-blue-50 rounded-xl p-6 overflow-hidden relative mb-6">
                    <div class="relative h-48 flex items-center justify-center">
                        <!-- Noise visualization -->
                        <div id="noise-viz" class="absolute inset-0 flex items-center justify-center overflow-hidden">
                            <!-- Animated noise particles -->
                            <div class="absolute inset-0 opacity-80">
                                <div class="noise-particle absolute bg-red-400/40 rounded-full w-3 h-3 animate-ping" style="top: 20%; left: 25%; animation-duration: 1.5s;"></div>
                                <div class="noise-particle absolute bg-red-400/40 rounded-full w-4 h-4 animate-ping" style="top: 35%; left: 15%; animation-duration: 2s;"></div>
                                <div class="noise-particle absolute bg-red-400/40 rounded-full w-2 h-2 animate-ping" style="top: 50%; left: 30%; animation-duration: 1.7s;"></div>
                                <div class="noise-particle absolute bg-red-400/40 rounded-full w-5 h-5 animate-ping" style="top: 65%; left: 20%; animation-duration: 2.2s;"></div>
                                <div class="noise-particle absolute bg-red-400/40 rounded-full w-3 h-3 animate-ping" style="top: 80%; left: 25%; animation-duration: 1.8s;"></div>
                                <!-- Right side particles -->
                                <div class="noise-particle absolute bg-red-400/40 rounded-full w-4 h-4 animate-ping" style="top: 25%; left: 75%; animation-duration: 1.9s;"></div>
                                <div class="noise-particle absolute bg-red-400/40 rounded-full w-3 h-3 animate-ping" style="top: 40%; left: 80%; animation-duration: 1.6s;"></div>
                                <div class="noise-particle absolute bg-red-400/40 rounded-full w-5 h-5 animate-ping" style="top: 55%; left: 75%; animation-duration: 2.1s;"></div>
                                <div class="noise-particle absolute bg-red-400/40 rounded-full w-2 h-2 animate-ping" style="top: 70%; left: 85%; animation-duration: 1.5s;"></div>
                            </div>
                            
                            <!-- Central microphone with noise cancellation shield -->
                            <div class="relative z-10">
                                <!-- Outer cancellation shield -->
                                <div class="absolute inset-0 w-40 h-40 rounded-full border-4 border-primary/30 animate-pulse-slow"></div>
                                
                                <!-- Inner cancellation field -->
                                <div class="absolute inset-0 w-32 h-32 rounded-full border-4 border-primary/50 -translate-x-1/2 -translate-y-1/2 left-1/2 top-1/2"></div>
                                
                                <!-- Microphone -->
                                <div class="w-24 h-24 bg-white rounded-full shadow-lg flex items-center justify-center relative z-10">
                                    <div class="absolute inset-0 bg-gradient-to-r from-blue-100 to-blue-200 rounded-full animate-pulse-slow opacity-50"></div>
                                    <i class="fas fa-microphone text-primary text-4xl"></i>
                                </div>
                            </div>
                            
                            <!-- Sound wave visualization (clean audio) -->
                            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-64 h-12">
                                <div class="relative h-full flex items-center justify-center">
                                    <div class="text-xs text-primary absolute -top-5 left-1/2 -translate-x-1/2 bg-white px-2 py-1 rounded-full shadow-sm">
                                        Clean Audio
                                    </div>
                                    <div class="flex items-end justify-center space-x-1 h-full">
                                        <!-- Clean audio waveform -->
                                        <div class="w-1 bg-green-400 rounded-t-lg h-3/5 animate-pulse-slow"></div>
                                        <div class="w-1 bg-green-400 rounded-t-lg h-4/5 animate-pulse-slow" style="animation-delay: 0.1s"></div>
                                        <div class="w-1 bg-green-400 rounded-t-lg h-full animate-pulse-slow" style="animation-delay: 0.2s"></div>
                                        <div class="w-1 bg-green-400 rounded-t-lg h-4/5 animate-pulse-slow" style="animation-delay: 0.3s"></div>
                                        <div class="w-1 bg-green-400 rounded-t-lg h-3/5 animate-pulse-slow" style="animation-delay: 0.4s"></div>
                                        <div class="w-1 bg-green-400 rounded-t-lg h-2/5 animate-pulse-slow" style="animation-delay: 0.5s"></div>
                                        <div class="w-1 bg-green-400 rounded-t-lg h-3/5 animate-pulse-slow" style="animation-delay: 0.6s"></div>
                                        <div class="w-1 bg-green-400 rounded-t-lg h-4/5 animate-pulse-slow" style="animation-delay: 0.7s"></div>
                                        <div class="w-1 bg-green-400 rounded-t-lg h-full animate-pulse-slow" style="animation-delay: 0.8s"></div>
                                        <div class="w-1 bg-green-400 rounded-t-lg h-4/5 animate-pulse-slow" style="animation-delay: 0.9s"></div>
                                        <div class="w-1 bg-green-400 rounded-t-lg h-3/5 animate-pulse-slow" style="animation-delay: 1.0s"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Multi-Stage Noise Cancellation Process Animation -->
                <div class="bg-blue-50 rounded-xl p-6 overflow-hidden relative mb-6">
                    <h3 class="text-lg font-semibold text-dark text-center mb-4">Multi-Stage Noise Cancellation Process</h3>
                    
                    <div class="relative h-80 overflow-hidden">
                        <!-- Stage Flow Animation -->
                        <div class="relative w-full h-full flex flex-col md:flex-row">
                            
                            <!-- Stage 1: Raw Audio Input -->
                            <div class="stage-container flex-1 p-3 relative">
                                <div class="bg-white/80 backdrop-blur-sm rounded-lg p-4 shadow-sm h-full relative overflow-hidden z-10">
                                    <!-- Stage header -->
                                    <div class="flex items-center mb-3">
                                        <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-red-500 font-bold">1</span>
                                        </div>
                                        <h4 class="font-medium text-dark">Raw Audio Input</h4>
                                    </div>
                                    
                                    <!-- Noisy audio visualization -->
                                    <div class="relative h-28 mb-3 rounded-md overflow-hidden border border-red-100">
                                        <!-- Chaotic wave animation -->
                                        <div class="absolute inset-0">
                                            <svg width="100%" height="100%" viewBox="0 0 100 50" preserveAspectRatio="none">
                                                <path d="M0,25 Q5,5 10,25 Q15,45 20,25 Q25,5 30,25 Q35,45 40,25 Q45,5 50,25 Q55,45 60,25 Q65,5 70,25 Q75,45 80,25 Q85,5 90,25 Q95,45 100,25" 
                                                    fill="none" stroke="#EF4444" stroke-width="1.5" class="audio-path">
                                                    <animate attributeName="d" 
                                                        values="M0,25 Q5,5 10,25 Q15,45 20,25 Q25,5 30,25 Q35,45 40,25 Q45,5 50,25 Q55,45 60,25 Q65,5 70,25 Q75,45 80,25 Q85,5 90,25 Q95,45 100,25;
                                                            M0,25 Q5,40 10,25 Q15,10 20,25 Q25,40 30,25 Q35,10 40,25 Q45,40 50,25 Q55,10 60,25 Q65,40 70,25 Q75,10 80,25 Q85,40 90,25 Q95,10 100,25;
                                                            M0,25 Q5,5 10,25 Q15,45 20,25 Q25,5 30,25 Q35,45 40,25 Q45,5 50,25 Q55,45 60,25 Q65,5 70,25 Q75,45 80,25 Q85,5 90,25 Q95,45 100,25" 
                                                        dur="4s" repeatCount="indefinite"/>
                                                </path>
                                            </svg>
                                        </div>
                                        
                                        <!-- Red noise particles -->
                                        <div class="absolute inset-0">
                                            <div class="absolute bg-red-400/30 rounded-full w-2 h-2 animate-ping" style="top: 20%; left: 15%; animation-duration: 2s;"></div>
                                            <div class="absolute bg-red-400/30 rounded-full w-2 h-2 animate-ping" style="top: 70%; left: 25%; animation-duration: 1.7s;"></div>
                                            <div class="absolute bg-red-400/30 rounded-full w-2 h-2 animate-ping" style="top: 30%; left: 65%; animation-duration: 2.5s;"></div>
                                            <div class="absolute bg-red-400/30 rounded-full w-2 h-2 animate-ping" style="top: 60%; left: 85%; animation-duration: 1.8s;"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-xs text-gray-500 text-center">
                                        Audio with background noise and distractions
                                    </div>
                                </div>
                                
                                <!-- Flow arrow -->
                                <div class="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 z-20 hidden md:block">
                                    <div class="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center shadow-sm">
                                        <i class="fas fa-arrow-right text-primary"></i>
                                    </div>
                                </div>
                                <div class="absolute left-1/2 bottom-0 transform -translate-x-1/2 translate-y-1/2 z-20 md:hidden">
                                    <div class="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center shadow-sm">
                                        <i class="fas fa-arrow-down text-primary"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Stage 2: Noise Identification & Filtering -->
                            <div class="stage-container flex-1 p-3 relative">
                                <div class="bg-white/80 backdrop-blur-sm rounded-lg p-4 shadow-sm h-full relative overflow-hidden z-10">
                                    <!-- Stage header -->
                                    <div class="flex items-center mb-3">
                                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-yellow-500 font-bold">2</span>
                                        </div>
                                        <h4 class="font-medium text-dark">Noise Identification</h4>
                                    </div>
                                    
                                    <!-- Processing visualization -->
                                    <div class="relative h-28 mb-3 rounded-md overflow-hidden border border-yellow-100">
                                        <!-- Filtering animation -->
                                        <div class="absolute inset-0">
                                            <!-- Original red wave -->
                                            <svg width="100%" height="100%" viewBox="0 0 100 50" preserveAspectRatio="none" class="absolute inset-0">
                                                <path d="M0,25 Q5,5 10,25 Q15,45 20,25 Q25,5 30,25 Q35,45 40,25 Q45,5 50,25 Q55,45 60,25 Q65,5 70,25 Q75,45 80,25 Q85,5 90,25 Q95,45 100,25" 
                                                    fill="none" stroke="#EF4444" stroke-width="1" stroke-opacity="0.3" class="audio-path">
                                                </path>
                                            </svg>
                                            
                                            <!-- Yellow identification markers -->
                                            <div class="absolute top-1/4 left-1/5 w-8 h-8">
                                                <div class="absolute inset-0 border-2 border-yellow-400 rounded-full animate-ping opacity-30"></div>
                                                <div class="absolute inset-2 bg-yellow-200/40 rounded-full flex items-center justify-center">
                                                    <i class="fas fa-exclamation-triangle text-yellow-500 text-xs"></i>
                                                </div>
                                            </div>
                                            
                                            <div class="absolute bottom-1/4 right-1/4 w-8 h-8">
                                                <div class="absolute inset-0 border-2 border-yellow-400 rounded-full animate-ping opacity-30"></div>
                                                <div class="absolute inset-2 bg-yellow-200/40 rounded-full flex items-center justify-center">
                                                    <i class="fas fa-exclamation-triangle text-yellow-500 text-xs"></i>
                                                </div>
                                            </div>
                                            
                                            <!-- Filter system visualization -->
                                            <div class="absolute inset-x-0 bottom-0 h-1/2">
                                                <div class="h-1 w-full bg-yellow-300/50 absolute top-0 animate-pulse"></div>
                                                <div class="absolute top-0 inset-x-0 h-full">
                                                    <svg width="100%" height="100%" viewBox="0 0 100 25" preserveAspectRatio="none" class="absolute inset-0">
                                                        <defs>
                                                            <linearGradient id="filterGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                                                <stop offset="0%" stop-color="rgba(252, 211, 77, 0.3)" />
                                                                <stop offset="100%" stop-color="rgba(252, 211, 77, 0)" />
                                                            </linearGradient>
                                                        </defs>
                                                        <rect x="0" y="0" width="100" height="25" fill="url(#filterGradient)" />
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-xs text-gray-500 text-center">
                                        AI identifies and isolates noise patterns
                                    </div>
                                </div>
                                
                                <!-- Flow arrow -->
                                <div class="absolute right-0 top-1/2 transform translate-x-1/2 -translate-y-1/2 z-20 hidden md:block">
                                    <div class="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center shadow-sm">
                                        <i class="fas fa-arrow-right text-primary"></i>
                                    </div>
                                </div>
                                <div class="absolute left-1/2 bottom-0 transform -translate-x-1/2 translate-y-1/2 z-20 md:hidden">
                                    <div class="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center shadow-sm">
                                        <i class="fas fa-arrow-down text-primary"></i>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Stage 3: Clean Audio Output -->
                            <div class="stage-container flex-1 p-3 relative">
                                <div class="bg-white/80 backdrop-blur-sm rounded-lg p-4 shadow-sm h-full relative overflow-hidden z-10">
                                    <!-- Stage header -->
                                    <div class="flex items-center mb-3">
                                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-green-500 font-bold">3</span>
                                        </div>
                                        <h4 class="font-medium text-dark">Clean Audio Output</h4>
                                    </div>
                                    
                                    <!-- Clean audio visualization -->
                                    <div class="relative h-28 mb-3 rounded-md overflow-hidden border border-green-100">
                                        <!-- Smooth wave animation -->
                                        <div class="absolute inset-0">
                                            <svg width="100%" height="100%" viewBox="0 0 100 50" preserveAspectRatio="none">
                                                <path d="M0,25 Q10,20 20,25 Q30,30 40,25 Q50,20 60,25 Q70,30 80,25 Q90,20 100,25" 
                                                    fill="none" stroke="#10B981" stroke-width="1.5" class="audio-path">
                                                    <animate attributeName="d" 
                                                        values="M0,25 Q10,20 20,25 Q30,30 40,25 Q50,20 60,25 Q70,30 80,25 Q90,20 100,25;
                                                                M0,25 Q10,18 20,25 Q30,32 40,25 Q50,18 60,25 Q70,32 80,25 Q90,18 100,25;
                                                                M0,25 Q10,20 20,25 Q30,30 40,25 Q50,20 60,25 Q70,30 80,25 Q90,20 100,25" 
                                                        dur="3s" repeatCount="indefinite"/>
                                                </path>
                                            </svg>
                                            
                                            <!-- Visual speech patterns -->
                                            <div class="absolute inset-0 flex items-center justify-center">
                                                <div class="speech-pattern flex items-end space-x-px h-16 w-40">
                                                    <div class="w-1 bg-green-400/70 rounded-t-lg h-3/5 animate-pulse-slow"></div>
                                                    <div class="w-1 bg-green-400/70 rounded-t-lg h-4/5 animate-pulse-slow" style="animation-delay: 0.1s"></div>
                                                    <div class="w-1 bg-green-400/70 rounded-t-lg h-full animate-pulse-slow" style="animation-delay: 0.2s"></div>
                                                    <div class="w-1 bg-green-400/70 rounded-t-lg h-4/5 animate-pulse-slow" style="animation-delay: 0.3s"></div>
                                                    <div class="w-1 bg-green-400/70 rounded-t-lg h-2/5 animate-pulse-slow" style="animation-delay: 0.4s"></div>
                                                    <div class="w-1 bg-green-400/70 rounded-t-lg h-3/5 animate-pulse-slow" style="animation-delay: 0.5s"></div>
                                                    <div class="w-1 bg-green-400/70 rounded-t-lg h-full animate-pulse-slow" style="animation-delay: 0.6s"></div>
                                                    <div class="w-1 bg-green-400/70 rounded-t-lg h-4/5 animate-pulse-slow" style="animation-delay: 0.7s"></div>
                                                    <div class="w-1 bg-green-400/70 rounded-t-lg h-3/5 animate-pulse-slow" style="animation-delay: 0.8s"></div>
                                                </div>
                                            </div>
                                            
                                            <!-- Quality indicator -->
                                            <div class="absolute bottom-2 right-2 bg-white/70 backdrop-blur-sm rounded-full px-2 py-1 flex items-center space-x-1">
                                                <div class="w-2 h-2 rounded-full bg-green-500 animate-pulse"></div>
                                                <span class="text-xs font-medium text-green-600">HD Audio</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="text-xs text-gray-500 text-center">
                                        Clear, noise-free audio ready for AI analysis
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Background decorations -->
                        <div class="absolute inset-0 pointer-events-none -z-10">
                            <!-- Background particles -->
                            <div class="absolute top-1/4 left-1/4 w-16 h-16 rounded-full bg-blue-400/10 blur-xl animate-blob"></div>
                            <div class="absolute bottom-1/4 right-1/4 w-24 h-24 rounded-full bg-cyan-400/10 blur-xl animate-blob" style="animation-delay: -2s;"></div>
                            
                            <!-- Process flow line -->
                            <div class="absolute top-1/2 left-0 right-0 h-0.5 bg-blue-200/30 hidden md:block"></div>
                            <div class="absolute left-1/2 top-0 bottom-0 w-0.5 bg-blue-200/30 md:hidden"></div>
                        </div>
                    </div>
                </div>
                
                <!-- New Frequency Spectrum Visualization -->
                <div class="bg-blue-50 rounded-xl p-6 overflow-hidden relative">
                    <h3 class="text-lg font-semibold text-dark text-center mb-4">Frequency Spectrum Analysis</h3>
                    
                    <div class="relative h-64 flex items-center justify-center flex-col">
                        <!-- Frequency spectrum visualization container -->
                        <div class="w-full h-full flex flex-col items-center">
                            <div class="w-full flex justify-between text-xs text-gray-500 mb-1 px-4">
                                <span>Low Frequencies</span>
                                <span>Speech Range</span>
                                <span>High Frequencies</span>
                            </div>
                            
                            <!-- Frequency response visualization -->
                            <div class="relative w-full h-40 mb-4 rounded-md overflow-hidden bg-white/70 backdrop-blur-sm shadow-sm border border-blue-100">
                                <!-- Grid lines -->
                                <div class="absolute inset-0 grid grid-cols-10 gap-0">
                                    <div class="border-r border-blue-100/50 h-full"></div>
                                    <div class="border-r border-blue-100/50 h-full"></div>
                                    <div class="border-r border-blue-100/50 h-full"></div>
                                    <div class="border-r border-blue-100/50 h-full"></div>
                                    <div class="border-r border-blue-100/50 h-full"></div>
                                    <div class="border-r border-blue-100/50 h-full"></div>
                                    <div class="border-r border-blue-100/50 h-full"></div>
                                    <div class="border-r border-blue-100/50 h-full"></div>
                                    <div class="border-r border-blue-100/50 h-full"></div>
                                </div>
                                
                                <!-- Noisy audio spectrum (red line) -->
                                <div class="absolute inset-0 px-2 py-2">
                                    <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
                                        <!-- Noisy spectrum line with random peaks -->
                                        <path d="M0,70 C5,40 10,80 15,30 C20,60 25,20 30,70 C35,40 40,90 45,20 C50,50 55,30 60,80 C65,40 70,70 75,30 C80,60 85,40 90,70 C95,30 100,50" 
                                              fill="none" stroke="#EF4444" stroke-width="2" opacity="0.7" class="spectrum-line">
                                            <animate attributeName="d" 
                                                    values="M0,70 C5,40 10,80 15,30 C20,60 25,20 30,70 C35,40 40,90 45,20 C50,50 55,30 60,80 C65,40 70,70 75,30 C80,60 85,40 90,70 C95,30 100,50;
                                                           M0,60 C5,30 10,70 15,40 C20,80 25,30 30,60 C33,50 47,60 50,50 C53,60 57,40 60,70 C65,30 70,60 75,40 C80,70 85,30 90,60 C95,40 100,60;
                                                           M0,70 C5,40 10,80 15,30 C20,60 25,20 30,70 C35,40 40,90 45,20 C50,50 55,30 60,80 C65,40 70,70 75,30 C80,60 85,40 90,70 C95,30 100,50"
                                                    dur="8s" repeatCount="indefinite" />
                                        </path>
                                    </svg>
                                </div>
                                
                                <!-- Speech frequency range highlight (yellow area) -->
                                <div class="absolute inset-y-0 left-1/3 right-1/3 bg-yellow-100/30 border-l border-r border-dashed border-yellow-400/40"></div>
                                
                                <!-- Filtered audio spectrum (green line) -->
                                <div class="absolute inset-0 px-2 py-2">
                                    <svg width="100%" height="100%" viewBox="0 0 100 100" preserveAspectRatio="none">
                                        <!-- Filtered spectrum line - smoother in speech range, flat elsewhere -->
                                        <path d="M0,80 C5,80 10,80 15,80 L30,80 C33,60 37,40 40,60 C43,50 47,60 50,50 C53,60 57,40 60,60 C63,50 67,60 70,80 L85,80 C90,80 95,80 100,80" 
                                              fill="none" stroke="#10B981" stroke-width="2" opacity="0.7" class="spectrum-line">
                                            <animate attributeName="d" 
                                                    values="M0,80 C5,80 10,80 15,80 L30,80 C33,60 37,40 40,60 C43,50 47,60 50,50 C53,60 57,40 60,60 C63,50 67,60 70,80 L85,80 C90,80 95,80 100,80;
                                                           M0,80 C5,80 10,80 15,80 L30,80 C33,50 37,60 40,40 C43,60 47,40 50,60 C53,40 57,60 60,50 C63,60 67,50 70,80 L85,80 C90,80 95,80 100,80;
                                                           M0,80 C5,80 10,80 15,80 L30,80 C33,60 37,40 40,60 C43,50 47,60 50,50 C53,60 57,40 60,60 C63,50 67,60 70,80 L85,80 C90,80 95,80 100,80"
                                                    dur="8s" repeatCount="indefinite" />
                                        </path>
                                    </svg>
                                </div>
                                
                                <!-- Legend in the bottom right -->
                                <div class="absolute bottom-2 right-2 bg-white/70 backdrop-blur-sm p-1 rounded-md text-xs flex flex-col">
                                    <div class="flex items-center">
                                        <div class="w-3 h-1 bg-red-500 mr-1"></div>
                                        <span>Noisy Audio</span>
                                    </div>
                                    <div class="flex items-center mt-1">
                                        <div class="w-3 h-1 bg-green-500 mr-1"></div>
                                        <span>Filtered Audio</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Filter stage visualization -->
                            <div class="w-full relative h-16 flex items-center justify-center">
                                <div class="absolute inset-x-0 top-0 h-2 bg-gradient-to-r from-blue-500 via-primary to-blue-500 rounded-full opacity-70">
                                    <div class="w-full h-full bg-white/50 animate-pulse-slow opacity-50"></div>
                                </div>
                                
                                <div class="absolute left-1/3 top-0 bottom-0 w-0.5 border-l border-dashed border-blue-400"></div>
                                <div class="absolute right-1/3 top-0 bottom-0 w-0.5 border-r border-dashed border-blue-400"></div>
                                
                                <div class="relative flex flex-col items-center">
                                    <div class="text-sm font-medium text-primary bg-blue-50 px-3 py-1 rounded-full shadow-sm border border-blue-100">
                                        Noise Cancellation Filter
                                    </div>
                                    <div class="mt-2 text-xs text-gray-500">
                                        Preserves speech frequencies while reducing noise
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Noise cancellation stages -->
                <div class="bg-white rounded-lg p-6 shadow-sm border border-blue-50">
                    <h3 class="text-lg font-semibold text-dark mb-4">Multi-Stage Noise Cancellation Process</h3>
                    
                    <div class="grid md:grid-cols-3 gap-4">
                        <div class="bg-blue-50/50 p-4 rounded-lg relative overflow-hidden">
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-100/50 to-transparent opacity-50"></div>
                            <div class="relative z-10">
                                <div class="flex items-center mb-3">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-ear-listen text-primary"></i>
                                    </div>
                                    <h4 class="font-medium text-dark">Stage 1: Ambient Detection</h4>
                                </div>
                                <p class="text-sm text-gray-600">Identifies and isolates consistent background noise patterns</p>
                            </div>
                        </div>
                        
                        <div class="bg-blue-50/50 p-4 rounded-lg relative overflow-hidden">
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-100/50 to-transparent opacity-50"></div>
                            <div class="relative z-10">
                                <div class="flex items-center mb-3">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-filter text-primary"></i>
                                    </div>
                                    <h4 class="font-medium text-dark">Stage 2: Dynamic Filtering</h4>
                                </div>
                                <p class="text-sm text-gray-600">Applies active noise cancellation to irregular disruptions</p>
                            </div>
                        </div>
                        
                        <div class="bg-blue-50/50 p-4 rounded-lg relative overflow-hidden">
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-100/50 to-transparent opacity-50"></div>
                            <div class="relative z-10">
                                <div class="flex items-center mb-3">
                                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                                        <i class="fas fa-volume-up text-primary"></i>
                                    </div>
                                    <h4 class="font-medium text-dark">Stage 3: Voice Enhancement</h4>
                                </div>
                                <p class="text-sm text-gray-600">Boosts speech frequencies while preserving natural voice quality</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-8 pt-6 border-t border-blue-100 text-center">
                    <button class="px-6 py-3 rounded-full border-2 border-primary text-primary font-medium hover:bg-primary hover:text-white transition-all" onclick="closeModal('noiseCancellationModal')">
                        Close
                    </button>
                </div>
            </div>
            
            <style>
                @keyframes pulse-shield {
                    0% { transform: scale(1); opacity: 0.7; }
                    50% { transform: scale(1.1); opacity: 0.3; }
                    100% { transform: scale(1); opacity: 0.7; }
                }
                
                #noise-viz .noise-particle {
                    animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
                    opacity: 0;
                }
            </style>
        </div>
    </div>

    <!-- Recording Start Guide Modal -->
    <div id="recordingStartModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white/95 backdrop-blur-md rounded-xl p-8 max-w-6xl w-full transform transition-all scale-95 opacity-0 animate-fade-in modal-content shadow-xl border border-blue-100 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6 pb-4 border-b border-blue-100">
                <div class="flex items-center">
                    <div class="mr-6 p-2 bg-blue-50 rounded-lg border border-blue-100 shadow-sm relative overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-100 to-transparent animate-pulse-slow rounded-lg opacity-70"></div>
                        <i class="fas fa-microphone-alt text-primary text-4xl"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-dark">
                            <span class="gradient-text">Quick Start Recording Guide</span>
                        </h2>
                        <p class="text-gray-600 text-sm mt-1">Simple steps to start your recording session</p>
                    </div>
                </div>
                <button onclick="closeModal('recordingStartModal')" class="text-gray-500 hover:text-primary transition-colors bg-blue-50 rounded-full w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="mb-8">
                <p class="text-gray-600 mb-6">
                    Wellora offers a simple and secure way to record your therapy sessions. Our application ensures all recordings are encrypted and securely stored directly in your client's file.
                </p>

                <!-- Process steps animation -->
                <div class="relative py-12 mx-auto max-w-3xl">
                    <!-- Process flow line -->
                    <div class="absolute top-1/2 left-0 right-0 h-1 bg-gray-200 -z-10"></div>
                    
                    <!-- Process steps -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-10">
                        <!-- Step 1: Start Recording -->
                        <div class="relative">
                            <div class="absolute left-1/2 -top-12 transform -translate-x-1/2 w-10 h-10 rounded-full gradient-bg flex items-center justify-center text-white font-bold z-10">1</div>
                            <div class="bg-white rounded-[10px] shadow-md hover:shadow-lg p-6 border-2 border-primary h-full transform transition-all duration-300 hover:-translate-y-1" style="box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.1);">
                                <div class="w-16 h-16 bg-red-50 rounded-full flex items-center justify-center mb-4 mx-auto">
                                    <i class="fas fa-circle text-xl text-red-500"></i>
                                </div>
                                <h3 class="text-lg font-semibold mb-2 text-center">Start Recording</h3>
                                <p class="text-sm text-gray-600 text-center">Click "Start Recording" in the Wellora application dashboard.</p>
                            </div>
                        </div>
                        
                        <!-- Step 2: Choose Device -->
                        <div class="relative">
                            <div class="absolute left-1/2 -top-12 transform -translate-x-1/2 w-10 h-10 rounded-full gradient-bg flex items-center justify-center text-white font-bold z-10">2</div>
                            <div class="bg-white rounded-[10px] shadow-md hover:shadow-lg p-6 border-2 border-primary h-full transform transition-all duration-300 hover:-translate-y-1" style="box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.1);">
                                <div class="w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mb-4 mx-auto">
                                    <i class="fas fa-mobile-alt text-xl text-primary"></i>
                                </div>
                                <h3 class="text-lg font-semibold mb-2 text-center">Select "External"</h3>
                                <p class="text-sm text-gray-600 text-center">Choose "External Device" option from the recording menu.</p>
                            </div>
                        </div>
                        
                        <!-- Step 3: Scan QR Code -->
                        <div class="relative">
                            <div class="absolute left-1/2 -top-12 transform -translate-x-1/2 w-10 h-10 rounded-full gradient-bg flex items-center justify-center text-white font-bold z-10">3</div>
                            <div class="bg-white rounded-[10px] shadow-md hover:shadow-lg p-6 border-2 border-primary h-full transform transition-all duration-300 hover:-translate-y-1" style="box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.1);">
                                <div class="w-16 h-16 bg-blue-50 rounded-full flex items-center justify-center mb-4 mx-auto">
                                    <i class="fas fa-qrcode text-xl text-primary"></i>
                                </div>
                                <h3 class="text-lg font-semibold mb-2 text-center">Scan QR Code</h3>
                                <p class="text-sm text-gray-600 text-center">Scan the generated QR code with your external device.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- QR Code Animation -->
                <div class="bg-blue-50 p-6 rounded-xl relative overflow-hidden mb-8">
                    <div class="flex flex-col md:flex-row items-center">
                        <!-- QR Code Display -->
                        <div class="w-full md:w-1/3 flex justify-center mb-6 md:mb-0">
                            <div class="relative">
                                <div class="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg blur-xl animate-pulse-slow"></div>
                                <div class="relative bg-white p-4 rounded-lg shadow-md border border-gray-200 z-10">
                                    <!-- Animated QR code scanning effect -->
                                    <div class="w-48 h-48 relative mx-auto qr-code-container">
                                        <!-- QR Code Pattern 1 (Default) -->
                                        <div class="absolute inset-0 grid grid-cols-10 grid-rows-10 gap-1 qr-pattern qr-pattern-1">
                                            <!-- QR code pattern (simplified) -->
                                            <div class="col-span-3 row-span-3 bg-dark"></div>
                                            <div class="col-span-3 row-span-3 bg-dark col-start-8"></div>
                                            <div class="col-span-3 row-span-3 bg-dark row-start-8"></div>
                                            <div class="col-start-4 row-start-4 col-span-3 row-span-3 bg-dark"></div>
                                            <div class="col-start-2 row-start-5 bg-dark"></div>
                                            <div class="col-start-5 row-start-2 bg-dark"></div>
                                            <div class="col-start-7 row-start-7 bg-dark"></div>
                                            <div class="col-start-8 row-start-5 bg-dark"></div>
                                        </div>
                                        
                                        <!-- QR Code Pattern 2 -->
                                        <div class="absolute inset-0 grid grid-cols-10 grid-rows-10 gap-1 qr-pattern qr-pattern-2 opacity-0">
                                            <!-- Different QR code pattern -->
                                            <div class="col-span-3 row-span-3 bg-dark"></div>
                                            <div class="col-span-3 row-span-3 bg-dark col-start-8"></div>
                                            <div class="col-span-3 row-span-3 bg-dark row-start-8"></div>
                                            <div class="col-start-3 row-start-5 col-span-5 row-span-1 bg-dark"></div>
                                            <div class="col-start-3 row-start-3 bg-dark col-span-1 row-span-5"></div>
                                            <div class="col-start-7 row-start-3 bg-dark col-span-1 row-span-5"></div>
                                            <div class="col-start-5 row-start-4 bg-dark"></div>
                                            <div class="col-start-4 row-start-6 bg-dark"></div>
                                            <div class="col-start-6 row-start-6 bg-dark"></div>
                                        </div>
                                        
                                        <!-- QR Code Pattern 3 -->
                                        <div class="absolute inset-0 grid grid-cols-10 grid-rows-10 gap-1 qr-pattern qr-pattern-3 opacity-0">
                                            <!-- Third QR code pattern variation -->
                                            <div class="col-span-3 row-span-3 bg-dark"></div>
                                            <div class="col-span-3 row-span-3 bg-dark col-start-8"></div>
                                            <div class="col-span-3 row-span-3 bg-dark row-start-8"></div>
                                            <div class="col-start-4 row-start-3 col-span-1 row-span-5 bg-dark"></div>
                                            <div class="col-start-6 row-start-3 col-span-1 row-span-5 bg-dark"></div>
                                            <div class="col-start-4 row-start-3 col-span-3 row-span-1 bg-dark"></div>
                                            <div class="col-start-4 row-start-7 col-span-3 row-span-1 bg-dark"></div>
                                            <div class="col-start-2 row-start-2 bg-dark"></div>
                                            <div class="col-start-8 row-start-8 bg-dark"></div>
                                        </div>
                                        
                                        <!-- Countdown timer indicator -->
                                        <div class="absolute -top-4 -right-4 w-8 h-8 rounded-full bg-red-500 flex items-center justify-center text-white font-bold text-sm qr-countdown">3</div>
                                        
                                        <!-- Scanning line animation -->
                                        <div class="absolute inset-x-0 h-1 bg-primary/70 top-0 animate-pulse-slow" style="animation: scanLine 3s infinite linear;">
                                            <div class="absolute inset-0 bg-white/50 opacity-50"></div>
                                        </div>
                                        
                                        <!-- Wellora logo in center of QR -->
                                        <div class="absolute inset-0 flex items-center justify-center">
                                            <div class="w-12 h-12 bg-white/90 backdrop-blur-sm rounded-lg p-1">
                                                <div class="w-10 h-10 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg flex items-center justify-center">
                                                    <span class="text-primary text-xs font-bold">Wellora</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <p class="text-center mt-2 text-dark text-xs font-medium">One-time Session QR • Expires in <span class="font-bold qr-seconds">3</span> seconds</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Device Connection Animation -->
                        <div class="w-full md:w-2/3 md:pl-8">
                            <h3 class="text-lg font-semibold mb-4 text-dark flex items-center">
                                <span class="bg-green-100 p-1 rounded-md mr-2 flex items-center justify-center">
                                    <i class="fas fa-lock text-green-600 text-sm"></i>
                                </span>
                                Secure Device Connection
                                <span class="ml-2 bg-blue-100 text-xs px-2 py-1 rounded-full text-blue-600 font-normal flex items-center">
                                    <i class="fas fa-shield-alt mr-1"></i>
                                    256-bit Encrypted
                                </span>
                            </h3>
                            
                            <!-- Device Connection Flow -->
                            <div class="relative py-16 flex items-center justify-center">
                                <!-- Therapist's Device -->
                                <div class="relative">
                                    <div class="bg-white rounded-xl shadow-md p-3 border border-gray-200 w-36">
                                        <div class="w-full h-20 bg-blue-50 rounded-lg mb-2 flex items-center justify-center">
                                            <i class="fas fa-laptop text-primary text-2xl"></i>
                                        </div>
                                        <p class="text-xs text-center text-gray-700 font-medium">Therapist's Device</p>
                                    </div>
                                    
                                    <!-- Binary data transfer animation from therapist to server -->
                                    <div class="absolute -right-12 top-1/3 transform -translate-y-1/2">
                                        <div class="binary-data-stream h-6 overflow-hidden relative w-28">
                                            <!-- Binary bits animation -->
                                            <div class="font-mono text-[12px] whitespace-nowrap absolute" style="animation: packetMove 3s infinite;">
                                                <span class="text-primary font-bold">1</span><span class="text-secondary">0</span><span class="text-primary">1</span><span class="text-secondary font-bold">0</span><span class="text-primary">1</span><span class="text-secondary">0</span><span class="text-primary font-bold">1</span><span class="text-secondary">1</span><span class="text-primary">0</span><span class="text-secondary font-bold">1</span><span class="text-primary">0</span><span class="text-secondary">1</span>
                                            </div>
                                            <div class="font-mono text-[12px] whitespace-nowrap absolute top-3" style="animation: packetMove 3.5s infinite; animation-delay: 0.5s;">
                                                <span class="text-primary">0</span><span class="text-secondary font-bold">1</span><span class="text-primary">1</span><span class="text-secondary">0</span><span class="text-primary font-bold">0</span><span class="text-secondary">1</span><span class="text-primary">0</span><span class="text-secondary font-bold">1</span><span class="text-primary">1</span><span class="text-secondary font-bold">0</span><span class="text-primary">1</span><span class="text-secondary">0</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Wireless Mic Connection directly to therapist's device -->
                                    <div class="absolute -bottom-24 left-1/2 transform -translate-x-1/2">
                                        <div class="connection-line h-10 w-1 mx-auto bg-primary/80 relative">
                                            <div class="absolute inset-0 bg-white/50 animate-pulse-slow"></div>
                                            <!-- Radio wave animations -->
                                            <div class="absolute -left-8 -right-8 top-1/4 w-16 h-16 rounded-full border-2 border-primary/50 animate-ping" style="animation-duration: 1.5s;"></div>
                                            <div class="absolute -left-6 -right-6 top-1/4 w-12 h-12 rounded-full border-2 border-primary/60 animate-ping" style="animation-duration: 1.2s;"></div>
                                            <div class="absolute -left-4 -right-4 top-1/4 w-8 h-8 rounded-full border-2 border-primary/70 animate-ping" style="animation-duration: 0.9s;"></div>
                                            <!-- Additional radio waves at bottom of connection -->
                                            <div class="absolute -left-6 -right-6 top-3/4 w-12 h-12 rounded-full border-2 border-primary/50 animate-ping" style="animation-duration: 1.7s;"></div>
                                            <div class="absolute -left-4 -right-4 top-3/4 w-8 h-8 rounded-full border-2 border-primary/60 animate-ping" style="animation-duration: 1.3s;"></div>
                                            <!-- Pulsating glow effect -->
                                            <div class="absolute -inset-1 bg-primary/20 rounded-full blur-sm animate-pulse"></div>
                                        </div>
                                        <div class="bg-white rounded-lg shadow-sm p-2 border border-gray-200">
                                            <div class="flex items-center space-x-2">
                                                <i class="fas fa-microphone text-primary text-sm"></i>
                                                <span class="text-xs text-gray-700">Rode Wireless GO II</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Connection Line from Server to Therapist -->
                                <div class="mx-6 h-4 w-24 bg-gradient-to-r from-green-400 to-primary relative rounded-sm overflow-hidden">
                                    <div class="absolute inset-0 bg-white/30 animate-pulse-slow"></div>
                                    <!-- Binary data flowing in the connection line -->
                                    <div class="absolute inset-0 flex items-center opacity-90">
                                        <div class="font-mono text-[10px] whitespace-nowrap text-white font-semibold animate-pulse-slow" style="animation: moveLeftToRight 5s linear infinite;">
                                            01011001 01101001 01011001 10101010
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Encrypted Server in the middle -->
                                <div class="relative mx-4">
                                    <div class="bg-white rounded-xl shadow-md p-3 border border-blue-200 w-32 relative overflow-hidden">
                                        <!-- Lock icon overlay for encryption -->
                                        <div class="absolute top-1 right-1 bg-green-100 rounded-full p-1">
                                            <i class="fas fa-lock text-green-500 text-xs"></i>
                                        </div>
                                        <div class="w-full h-28 bg-blue-50 rounded-lg mb-2 flex flex-col items-center justify-center">
                                            <i class="fas fa-server text-primary text-2xl mb-2"></i>
                                            <!-- Server indicators -->
                                            <div class="flex space-x-1 mb-1">
                                                <div class="w-2 h-2 rounded-full bg-green-500 animate-pulse" style="animation-delay: 0.1s"></div>
                                                <div class="w-2 h-2 rounded-full bg-green-500 animate-pulse" style="animation-delay: 0.3s"></div>
                                                <div class="w-2 h-2 rounded-full bg-green-500 animate-pulse" style="animation-delay: 0.5s"></div>
                                            </div>
                                            <!-- Animated encryption pattern -->
                                            <div class="w-16 h-4 bg-blue-100/50 rounded overflow-hidden relative">
                                                <div class="absolute inset-0 opacity-50 flex items-center">
                                                    <div class="text-[6px] font-mono text-primary whitespace-nowrap animate-pulse-slow" style="animation: moveLeftToRight 8s linear infinite;">
                                                        256-BIT ENCRYPTION 256-BIT ENCRYPTION
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <p class="text-xs text-center text-gray-700 font-medium">Encrypted Server</p>
                                    </div>
                                    

                                </div>
                                
                                <!-- Connection Line from Device to Server -->
                                <div class="mx-6 h-4 w-24 bg-gradient-to-r from-secondary to-green-400 relative rounded-sm overflow-hidden">
                                    <div class="absolute inset-0 bg-white/30 animate-pulse-slow"></div>
                                    <!-- Binary data flowing in the connection line -->
                                    <div class="absolute inset-0 flex items-center opacity-90">
                                        <div class="font-mono text-[10px] whitespace-nowrap text-white font-semibold animate-pulse-slow" style="animation: moveRightToLeft 4s linear infinite;">
                                            10110010 11010110 10001101 01011100
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- External Recording Device -->
                                <div class="relative">
                                    <div class="bg-white rounded-xl shadow-md p-3 border border-gray-200 w-28">
                                        <div class="w-full h-24 bg-blue-50 rounded-lg mb-2 flex flex-col items-center justify-center">
                                            <i class="fas fa-mobile-alt text-primary text-2xl mb-2"></i>
                                            <div class="w-10 h-2 rounded-full bg-red-500/60 animate-pulse"></div>
                                        </div>
                                        <p class="text-xs text-center text-gray-700 font-medium">Recording Device</p>
                                    </div>
                                    
                                    <!-- Binary data transfer animation from recording device to server -->
                                    <div class="absolute -left-12 top-1/3 transform -translate-y-1/2">
                                        <div class="binary-data-stream h-6 overflow-hidden relative w-28">
                                            <!-- Binary bits animation -->
                                            <div class="font-mono text-[12px] whitespace-nowrap absolute" style="animation: packetMove 3s infinite; animation-direction: reverse;">
                                                <span class="text-primary font-bold">0</span><span class="text-secondary">1</span><span class="text-primary">1</span><span class="text-secondary font-bold">1</span><span class="text-primary">0</span><span class="text-secondary">0</span><span class="text-primary font-bold">1</span><span class="text-secondary">0</span><span class="text-primary">1</span><span class="text-secondary font-bold">1</span><span class="text-primary">0</span><span class="text-secondary">0</span>
                                            </div>
                                            <div class="font-mono text-[12px] whitespace-nowrap absolute top-3" style="animation: packetMove 3.5s infinite; animation-delay: 0.5s; animation-direction: reverse;">
                                                <span class="text-primary">1</span><span class="text-secondary font-bold">0</span><span class="text-primary">0</span><span class="text-secondary">1</span><span class="text-primary font-bold">1</span><span class="text-secondary">0</span><span class="text-primary">1</span><span class="text-secondary font-bold">0</span><span class="text-primary">0</span><span class="text-secondary font-bold">1</span><span class="text-primary">1</span><span class="text-secondary">0</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-6 bg-green-50/60 p-3 rounded-lg border border-green-100">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 text-green-500 mt-0.5">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h4 class="text-sm font-medium text-green-800">End-to-End Encrypted</h4>
                                        <p class="text-xs text-green-700 mt-1">All recordings are transmitted with 256-bit encryption and stored securely in your HIPAA-compliant cloud database.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recommended Equipment -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-4 text-dark">Recommended Equipment</h3>
                    <div class="bg-white rounded-xl shadow-sm border border-blue-50 p-6">
                        <div class="flex flex-col md:flex-row items-center">
                            <div class="flex-shrink-0 mb-4 md:mb-0 md:mr-6">
                                <div class="w-32 h-32 bg-blue-50 rounded-lg flex items-center justify-center p-2 relative overflow-hidden">
                                    <div class="absolute inset-0 bg-gradient-to-r from-blue-100/50 to-transparent animate-pulse-slow"></div>
                                    <div class="relative z-10 flex flex-col items-center">
                                        <i class="fas fa-microphone-alt text-primary text-3xl mb-2"></i>
                                        <div class="text-xs text-center text-primary font-medium">Rode Wireless GO II TX</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="text-md font-medium text-dark mb-2">Rode Wireless GO II TX</h4>
                                <p class="text-sm text-gray-600 mb-3">
                                    We recommend using the Rode Wireless GO II TX wireless microphone system for optimal audio quality. This compact device delivers professional-grade sound with minimal setup.
                                </p>
                                <ul class="space-y-1 text-sm">
                                    <li class="flex items-start">
                                        <i class="fas fa-check text-tertiary mr-2 mt-1 text-xs"></i>
                                        <span class="text-gray-700">Compact, easy-to-use wireless microphone</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check text-tertiary mr-2 mt-1 text-xs"></i>
                                        <span class="text-gray-700">7-hour battery life for long sessions</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check text-tertiary mr-2 mt-1 text-xs"></i>
                                        <span class="text-gray-700">128-bit encryption for secure transmission</span>
                                    </li>
                                    <li class="flex items-start">
                                        <i class="fas fa-check text-tertiary mr-2 mt-1 text-xs"></i>
                                        <span class="text-gray-700">Compatible with all iOS and Android devices</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Live Monitoring Feature -->
                <div class="bg-primary/5 p-6 rounded-xl border border-primary/20 mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-eye text-primary"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-dark">Live Monitoring</h3>
                    </div>
                    <p class="text-gray-700 mb-4">
                        As a therapist, you can see the recording in real-time as it's being captured. The audio file appears directly in your client's profile in the Wellora application.
                    </p>
                    <!-- Live Preview Mockup -->
                    <div class="bg-white rounded-lg shadow-sm p-3 border border-gray-200 max-w-md mx-auto">
                        <div class="flex items-center mb-2 border-b border-gray-100 pb-2">
                            <div class="w-8 h-8 bg-blue-50 rounded-full flex items-center justify-center mr-2">
                                <i class="fas fa-user-circle text-primary text-sm"></i>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-dark">Client: Sarah Johnson</div>
                                <div class="text-xs text-gray-500">Session #12 - 06/15/2024</div>
                            </div>
                            <div class="ml-auto">
                                <div class="flex items-center text-xs bg-red-50 text-red-500 px-2 py-1 rounded-full">
                                    <div class="w-2 h-2 rounded-full bg-red-500 animate-pulse mr-1"></div>
                                    <span>LIVE</span>
                                </div>
                            </div>
                        </div>
                        <div class="h-16 bg-gray-50 rounded-md mb-2 flex items-center justify-center relative overflow-hidden">
                            <!-- Animated waveform -->
                            <div class="flex items-end justify-center space-x-1 h-10 w-full">
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-1/3" style="animation-duration: 1.1s"></div>
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-2/3" style="animation-duration: 1.4s"></div>
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-full" style="animation-duration: 1.2s"></div>
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-2/3" style="animation-duration: 1.7s"></div>
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-1/3" style="animation-duration: 1.3s"></div>
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-2/3" style="animation-duration: 1.6s"></div>
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-full" style="animation-duration: 1.1s"></div>
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-3/4" style="animation-duration: 1.5s"></div>
                                <!-- Repeat pattern -->
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-1/3" style="animation-duration: 1.1s"></div>
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-2/3" style="animation-duration: 1.4s"></div>
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-full" style="animation-duration: 1.2s"></div>
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-2/3" style="animation-duration: 1.7s"></div>
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-1/3" style="animation-duration: 1.3s"></div>
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-2/3" style="animation-duration: 1.6s"></div>
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-full" style="animation-duration: 1.1s"></div>
                                <div class="w-1 bg-primary rounded-t-sm animate-pulse-slow h-3/4" style="animation-duration: 1.5s"></div>
                            </div>
                        </div>
                        <div class="flex justify-between items-center text-xs text-gray-500">
                            <div>Duration: 24:18</div>
                            <div class="flex items-center">
                                <i class="fas fa-microphone text-primary mr-1"></i>
                                <span>External Mic</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-8 pt-6 border-t border-blue-100 text-center">
                <button class="px-6 py-3 rounded-full gradient-bg text-white font-medium hover:shadow-md transition-all" onclick="closeModal('recordingStartModal')">
                    Got It
                </button>
            </div>
        </div>
    </div>
    
    <style>
        @keyframes scanLine {
            0% { top: 0; }
            100% { top: 100%; }
        }
        
        @keyframes packetMove {
            0% { transform: translateX(0); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateX(94px); opacity: 0; }
        }
        
        @keyframes reversePacketMove {
            0% { transform: translateX(94px); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateX(0); opacity: 0; }
        }
        
        @keyframes radioWaveEmit {
            0% { transform: scale(0.6); opacity: 0.8; }
            100% { transform: scale(1.5); opacity: 0; }
        }
        
        @keyframes moveLeftToRight {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        @keyframes moveRightToLeft {
            0% { transform: translateX(100%); }
            100% { transform: translateX(-100%); }
        }
        
        @keyframes binaryFlicker {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }
        
        /* QR Code animation */
        .qr-pattern {
            transition: opacity 0.5s ease;
        }
        
        @keyframes qrChange {
            0%, 33% { opacity: 1; }
            33.1%, 100% { opacity: 0; }
        }
        
        @keyframes qrChange2 {
            0%, 33% { opacity: 0; }
            33.1%, 66% { opacity: 1; }
            66.1%, 100% { opacity: 0; }
        }
        
        @keyframes qrChange3 {
            0%, 66% { opacity: 0; }
            66.1%, 100% { opacity: 1; }
        }
        
        /* Removed animation, now controlled by JS */
        .qr-pattern-1, .qr-pattern-2, .qr-pattern-3 {
            transition: opacity 0.5s ease;
        }
        
        .qr-pattern-1 {
            opacity: 1;
        }
        
        .qr-pattern-2, .qr-pattern-3 {
            opacity: 0;
        }
    </style>
    
    <script>
        // QR code countdown timer with synchronized pattern change
        document.addEventListener('DOMContentLoaded', function() {
            const countdowns = document.querySelectorAll('.qr-countdown, .qr-seconds');
            const qrPattern1 = document.querySelector('.qr-pattern-1');
            const qrPattern2 = document.querySelector('.qr-pattern-2');
            const qrPattern3 = document.querySelector('.qr-pattern-3');
            let count = 3;
            let currentPattern = 1;
            
            // Update countdown timer and sync QR code pattern
            setInterval(function() {
                count = count > 1 ? count - 1 : 3;
                countdowns.forEach(el => el.textContent = count);
                
                // When countdown resets, change QR code pattern
                if (count === 3) {
                    // Reset all patterns
                    qrPattern1.style.opacity = '0';
                    qrPattern2.style.opacity = '0';
                    qrPattern3.style.opacity = '0';
                    
                    // Show next pattern
                    currentPattern = currentPattern % 3 + 1;
                    if (currentPattern === 1) {
                        qrPattern1.style.opacity = '1';
                    } else if (currentPattern === 2) {
                        qrPattern2.style.opacity = '1';
                    } else {
                        qrPattern3.style.opacity = '1';
                    }
                }
            }, 1000);
        });
    </script>

    <script src="script.js"></script>
    <script>
        // Generate random waveform patterns when the modal opens
        document.addEventListener('DOMContentLoaded', function() {
            // Function to create noisy waveform
            function createNoisyWaveform() {
                const container = document.getElementById('noisy-waveform');
                if (!container) return;
                
                // Clear existing content
                container.innerHTML = '';
                
                // Create random height bars with noise
                for (let i = 0; i < 40; i++) {
                    const bar = document.createElement('div');
                    // Very random heights for noisy audio
                    const height = Math.random() * 100;
                    bar.className = 'w-1 bg-red-400/70 rounded-t-lg mx-px';
                    bar.style.height = height + '%';
                    
                    // Add small random fluctuations to simulate noise
                    if (Math.random() > 0.5) {
                        bar.classList.remove('bg-red-400/70');
                        bar.classList.add('bg-yellow-400/70');
                    }
                    
                    // Animate each bar with different timing for chaotic effect
                    bar.style.animation = 'pulse 1s ease-in-out infinite';
                    bar.style.animationDelay = (Math.random() * 0.5) + 's';
                    
                    container.appendChild(bar);
                }
            }
            
            // Function to create clean waveform
            function createCleanWaveform() {
                const container = document.getElementById('clean-waveform');
                if (!container) return;
                
                // Clear existing content
                container.innerHTML = '';
                
                // Create smoother, more consistent bars for clean audio
                // Using a sine wave pattern for more natural speech appearance
                for (let i = 0; i < 40; i++) {
                    const bar = document.createElement('div');
                    // Sine wave pattern with some small variations but more consistent
                    const height = 30 + 30 * Math.sin(i * 0.3) + (Math.random() * 15);
                    bar.className = 'w-1 bg-green-400/70 rounded-t-lg mx-px';
                    bar.style.height = height + '%';
                    
                    // Gentle animation for clean audio
                    bar.style.animation = 'pulse 1.5s ease-in-out infinite';
                    bar.style.animationDelay = (i * 0.05) + 's';
                    
                    container.appendChild(bar);
                }
            }
            
            // Initialize waveforms when the modal opens
            const originalOpenModal = window.openModal;
            window.openModal = function(modalId) {
                originalOpenModal(modalId);
                if (modalId === 'filteringModal') {
                    setTimeout(() => {
                        createNoisyWaveform();
                        createCleanWaveform();
                    }, 100);
                }
            };
        });
    </script>
                        
                        <!-- AI Analysis badges -->
                        <div class="flex flex-wrap gap-2">
                            <div class="bg-blue-900/30 px-2 py-1 rounded-md flex items-center">
                                <i class="fas fa-brain text-blue-400 text-xs mr-1"></i>
                                <span class="text-xs text-gray-200">Anxiety: High</span>
                            </div>
                            <div class="bg-blue-900/30 px-2 py-1 rounded-md flex items-center">
                                <i class="fas fa-heartbeat text-blue-400 text-xs mr-1"></i>
                                <span class="text-xs text-gray-200">Stress: Moderate</span>
                            </div>
                            <div class="bg-blue-900/30 px-2 py-1 rounded-md flex items-center">
                                <i class="fas fa-chart-line text-blue-400 text-xs mr-1"></i>
                                <span class="text-xs text-gray-200">Progress: +12%</span>
                            </div>
                            <div class="bg-blue-900/30 px-2 py-1 rounded-md flex items-center">
                                <i class="fas fa-lightbulb text-blue-400 text-xs mr-1"></i>
                                <span class="text-xs text-gray-200">Social trigger detected</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- AI Analysis section -->
                <div class="bg-gray-800/70 rounded-xl p-5 relative overflow-hidden">
                    <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                        <i class="fas fa-brain text-blue-400 mr-2"></i>
                        Real-time AI Analysis
                    </h3>
                    
                    <div class="grid grid-cols-3 gap-4">
                        <!-- Sentiment Analysis -->
                        <div class="bg-gray-900/50 rounded-lg p-3 border border-gray-700">
                            <h4 class="text-blue-400 text-sm font-medium mb-2">Sentiment Analysis</h4>
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-xs text-gray-400">Anxiety</span>
                                <span class="text-xs text-gray-300">68%</span>
                            </div>
                            <div class="h-2 bg-gray-700 rounded-full mb-3">
                                <div class="h-full bg-red-500 rounded-full" style="width: 68%"></div>
                            </div>
                            
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-xs text-gray-400">Depression</span>
                                <span class="text-xs text-gray-300">23%</span>
                            </div>
                            <div class="h-2 bg-gray-700 rounded-full mb-3">
                                <div class="h-full bg-yellow-500 rounded-full" style="width: 23%"></div>
                            </div>
                            
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-xs text-gray-400">Optimism</span>
                                <span class="text-xs text-gray-300">42%</span>
                            </div>
                            <div class="h-2 bg-gray-700 rounded-full">
                                <div class="h-full bg-green-500 rounded-full" style="width: 42%"></div>
                            </div>
                        </div>
                        
                        <!-- Topic Detection -->
                        <div class="bg-gray-900/50 rounded-lg p-3 border border-gray-700">
                            <h4 class="text-blue-400 text-sm font-medium mb-2">Topic Detection</h4>
                            <div class="space-y-2">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                                        <span class="text-xs text-gray-300">Work anxiety</span>
                                    </div>
                                    <span class="text-xs text-gray-400">Primary</span>
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 rounded-full bg-purple-500 mr-2"></div>
                                        <span class="text-xs text-gray-300">Social stressors</span>
                                    </div>
                                    <span class="text-xs text-gray-400">Secondary</span>
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-2 h-2 rounded-full bg-teal-500 mr-2"></div>
                                        <span class="text-xs text-gray-300">Coping mechanisms</span>
                                    </div>
                                    <span class="text-xs text-gray-400">Tertiary</span>
                                </div>
                                
                                <div class="text-xs text-gray-400 mt-2 pt-2 border-t border-gray-700">
                                    <span class="text-blue-400">Emerging topic:</span> Physical symptoms of anxiety
                                </div>
                            </div>
                        </div>
                        
                        <!-- Treatment Insights -->
                        <div class="bg-gray-900/50 rounded-lg p-3 border border-gray-700">
                            <h4 class="text-blue-400 text-sm font-medium mb-2">Treatment Insights</h4>
                            <div class="space-y-3">
                                <div class="flex items-start">
                                    <i class="fas fa-lightbulb text-yellow-400 text-xs mt-1 mr-2"></i>
                                    <span class="text-xs text-gray-300">Consider adding guided visualization techniques for work presentations</span>
                                </div>
                                
                                <div class="flex items-start">
                                    <i class="fas fa-lightbulb text-yellow-400 text-xs mt-1 mr-2"></i>
                                    <span class="text-xs text-gray-300">Physical response to authority figures suggests potential for role-play therapy</span>
                                </div>
                                
                                <div class="flex items-start">
                                    <i class="fas fa-chart-line text-green-400 text-xs mt-1 mr-2"></i>
                                    <span class="text-xs text-gray-300">Breathing techniques showing 32% improvement from previous sessions</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Processing bars -->
                    <div class="mt-4 pt-4 border-t border-gray-700 flex justify-between items-center">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full bg-blue-500 animate-pulse mr-2"></div>
                            <span class="text-sm text-blue-400">AI Analysis in progress</span>
                        </div>
                        
                        <div class="flex items-center space-x-2">
                            <!-- Processing icon -->
                            <div class="text-xs text-gray-400">
                                <i class="fas fa-microchip text-blue-400 mr-1"></i>
                                Processing on encrypted server
                            </div>
                            
                            <!-- Neural network visualization -->
                            <div class="relative w-24 h-6 bg-gray-900/50 rounded-md overflow-hidden">
                                <div class="absolute inset-0 flex items-center justify-evenly px-1">
                                    <div class="w-1 h-1/2 bg-blue-400 rounded-sm animate-bounce" style="animation-delay: 0s;"></div>
                                    <div class="w-1 h-1/3 bg-blue-400 rounded-sm animate-bounce" style="animation-delay: 0.1s;"></div>
                                    <div class="w-1 h-2/3 bg-blue-400 rounded-sm animate-bounce" style="animation-delay: 0.2s;"></div>
                                    <div class="w-1 h-1/4 bg-blue-400 rounded-sm animate-bounce" style="animation-delay: 0.3s;"></div>
                                    <div class="w-1 h-1/2 bg-blue-400 rounded-sm animate-bounce" style="animation-delay: 0.4s;"></div>
                                    <div class="w-1 h-2/3 bg-blue-400 rounded-sm animate-bounce" style="animation-delay: 0.5s;"></div>
                                    <div class="w-1 h-1/3 bg-blue-400 rounded-sm animate-bounce" style="animation-delay: 0.6s;"></div>
                                    <div class="w-1 h-1/2 bg-blue-400 rounded-sm animate-bounce" style="animation-delay: 0.7s;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 pt-4 border-t border-gray-700 text-center">
                <button class="px-6 py-3 rounded-full gradient-bg text-white font-medium hover:shadow-md transition-all" onclick="document.getElementById('aiRecordingDemoModal').style.display = 'none';">
                    Close Demo
                </button>
            </div>
        </div>
    </div>

    <style>
        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0; }
        }
        
        .animate-blink {
            animation: blink 0.8s step-end infinite;
        }
        
        /* Styling for voice bars with animation */
        .voice-bar {
            transition: height 0.2s ease;
            will-change: height;
        }

        /* Specialized neural network for modals */
        .modal-neural-node {
            position: absolute;
            width: 6px;
            height: 6px;
            background-color: rgba(59, 130, 246, 0.3);
            border-radius: 50%;
            animation: pulse 3s infinite;
        }
        
        .modal-neural-connection {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.3), rgba(59, 130, 246, 0.1));
            transform-origin: left center;
        }
    </style>

    <script>
        // Generate random heights for voice bars to simulate voice activity
        function updateVoiceWaveform() {
            const voiceBars = document.querySelectorAll('.voice-bar');
            voiceBars.forEach(bar => {
                // Generate random height between 10% and 100%
                const height = 10 + Math.random() * 90;
                bar.style.height = `${height}%`;
            });
        }
        
        // Update transcript with typing effect
        function updateTranscription() {
            // This would be more elaborate in a real implementation
            // For demo purposes, we just update the existing typing animation
            const typingText = document.querySelector('.typing-animation p');
            const cursor = document.querySelector('.typing-cursor');
            
            // Simple back-and-forth effect on the cursor
            cursor.classList.toggle('animate-blink');
        }
        
        // Initialize modal animations when opened
        document.addEventListener('DOMContentLoaded', function() {
            // Set up modal opening actions
            const openModalButtons = document.querySelectorAll('[onclick*="openModal"]');
            openModalButtons.forEach(button => {
                const modalId = button.getAttribute('onclick').match(/'([^']+)'/)[1];
                if (modalId === 'aiRecordingDemoModal') {
                    button.addEventListener('click', function() {
                        // Start animations when modal opens
                        const voiceAnimationInterval = setInterval(updateVoiceWaveform, 200);
                        const transcriptionInterval = setInterval(updateTranscription, 500);
                        
                        // Store intervals to clear them when modal closes
                        window.modalIntervals = window.modalIntervals || {};
                        window.modalIntervals[modalId] = [voiceAnimationInterval, transcriptionInterval];
                    });
                }
            });
            
            // Set up modal closing to clean up animations
            const closeModalButtons = document.querySelectorAll('[onclick*="closeModal"]');
            closeModalButtons.forEach(button => {
                const modalId = button.getAttribute('onclick').match(/'([^']+)'/)[1];
                if (modalId === 'aiRecordingDemoModal') {
                    button.addEventListener('click', function() {
                        // Clear animation intervals when modal closes
                        if (window.modalIntervals && window.modalIntervals[modalId]) {
                            window.modalIntervals[modalId].forEach(interval => clearInterval(interval));
                        }
                    });
                }
            });
        });
    </script>
                                
                                <!-- Stage 4: Generate Reports -->
                                <div class="workflow-stage flex flex-col items-center">
                                    <div class="workflow-stage-icon w-16 h-16 rounded-full bg-teal-900/60 backdrop-blur-sm mb-4 flex items-center justify-center border-2 border-teal-500 relative overflow-hidden">
                                        <div class="absolute inset-0 bg-gradient-to-r from-teal-600/40 to-teal-500/40 animate-pulse opacity-80"></div>
                                        <i class="fas fa-file-medical-alt text-teal-400 text-2xl relative z-10"></i>
                                        
                                        <!-- Report page flipping animation -->
                                        <div class="absolute inset-0 pointer-events-none flex items-center justify-center">
                                            <div class="report-page w-7 h-8 bg-white/20 rounded-sm absolute" style="transform: rotate(-5deg); animation: flipPage 3s infinite;"></div>
                                            <div class="report-page w-7 h-8 bg-white/30 rounded-sm absolute" style="transform: rotate(-2deg); animation: flipPage 3s infinite 0.3s;"></div>
                                        </div>
                                    </div>
                                    <h4 class="text-white font-medium text-sm mb-1">Generate Reports</h4>
                                    <p class="text-gray-400 text-xs text-center">Create actionable insights</p>
                                </div>
                            </div>
                        
                        <!-- Middle: AI analysis -->
                        <div class="w-1/3 h-full flex flex-col justify-center items-center relative">
                            <!-- Connector lines from past sessions -->
                            <div class="absolute left-0 top-0 w-6 h-full pointer-events-none">
                                <svg width="24" height="100%" viewBox="0 0 24 192" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M0 48C8 48 16 96 24 96" stroke="rgba(37, 99, 235, 0.5)" stroke-width="1" />
                                    <path d="M0 96C8 96 16 96 24 96" stroke="rgba(37, 99, 235, 0.5)" stroke-width="1" />
                                    <path d="M0 144C8 144 16 96 24 96" stroke="rgba(37, 99, 235, 0.5)" stroke-width="1" />
                                </svg>
                            </div>
                            
                            <!-- Connector line to current data -->
                            <div class="absolute right-0 top-0 w-6 h-full pointer-events-none">
                                <svg width="24" height="100%" viewBox="0 0 24 192" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M0 96C8 96 16 96 24 96" stroke="rgba(37, 99, 235, 0.5)" stroke-width="1" />
                                </svg>
                            </div>
                            
                            <!-- AI Brain visualization -->
                            <div class="relative">
                                <div class="w-20 h-20 rounded-full bg-purple-900/30 flex items-center justify-center relative">
                                    <!-- Orbiting data particles -->
                                    <div class="absolute inset-0 pointer-events-none">
                                        <div class="w-full h-full rounded-full border-2 border-dashed border-purple-500/30 absolute animate-spin" style="animation-duration: 20s;"></div>
                                        <div class="absolute w-2 h-2 rounded-full bg-blue-400 top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 orbit-particle"></div>
                                        <div class="absolute w-2 h-2 rounded-full bg-teal-400 bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 orbit-particle" style="animation-delay: 1s;"></div>
                                        <div class="absolute w-2 h-2 rounded-full bg-purple-400 top-1/2 left-0 transform -translate-x-1/2 -translate-y-1/2 orbit-particle" style="animation-delay: 2s;"></div>
                                    </div>
                                    
                                    <div class="w-12 h-12 bg-gradient-to-r from-purple-800 to-indigo-800 rounded-full flex items-center justify-center shadow-lg border border-purple-600/50">
                                        <i class="fas fa-brain text-purple-300 text-xl"></i>
                                    </div>
                                </div>
                                <p class="text-xs text-center text-purple-400 mt-2">AI Analysis</p>
                            </div>
                        </div>
                        
                        <!-- Right side: Current session -->
                        <div class="w-1/3 h-full flex flex-col justify-center items-center px-4">
                            <p class="text-gray-300 text-sm font-medium mb-2">Current Session</p>
                            
                            <div class="p-3 bg-gray-800 rounded-lg w-full">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-xs text-white font-medium">Today</span>
                                    <div class="text-xs text-gray-400">
                                        <i class="fas fa-clock text-blue-500 mr-1"></i>
                                        <span>35:42</span>
                                    </div>
                                </div>
                                
                                <!-- Current session metrics -->
                                <div class="space-y-2">
                                    <div class="flex items-center justify-between">
                                        <span class="text-[10px] text-gray-400">Anxiety</span>
                                        <div class="w-24 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                                            <div class="h-full rounded-full bg-red-500 current-metric" style="width: 68%;"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between">
                                        <span class="text-[10px] text-gray-400">Focus</span>
                                        <div class="w-24 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                                            <div class="h-full rounded-full bg-blue-500 current-metric" style="width: 82%;"></div>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between">
                                        <span class="text-[10px] text-gray-400">Progress</span>
                                        <div class="w-24 h-1.5 bg-gray-700 rounded-full overflow-hidden">
                                            <div class="h-full rounded-full bg-green-500 current-metric" style="width: 45%;"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-gray-300 text-xs mt-4">
                        <p>Our AI combines historical patterns across multiple sessions with current session data to provide context-aware insights and track long-term therapeutic progress.</p>
                    </div>
                </div>
            </div>
            
            <div class="mt-8 pt-6 border-t border-gray-700 text-center">
                <button class="px-6 py-3 rounded-full gradient-bg text-white font-medium hover:shadow-md transition-all" onclick="closeModal('saveProcessModal')">
                    Close Visualization
                </button>
            </div>
        </div>
    </div>
    
    <style>
        @keyframes moveDataRight {
            0% { transform: translateX(0); opacity: 0; }
            20% { opacity: 1; }
            100% { transform: translateX(300%); opacity: 0; }
        }
        
        @keyframes neuralPulse {
            0% { transform: scale(1); opacity: 1; }
            100% { transform: scale(2.5); opacity: 0; }
        }
        
        @keyframes typewriter {
            0% { width: 0; }
            50% { width: 100%; }
            100% { width: 0; }
        }
        
        @keyframes flipPage {
            0%, 100% { transform: rotate(-5deg); }
            50% { transform: rotate(5deg); }
        }
        
        @keyframes neuralPulseHorizontal {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        @keyframes neuralPulseVertical {
            0% { transform: translateY(-100%); }
            100% { transform: translateY(100%); }
        }
        
        .audio-bar {
            animation: pulseBar 1.5s infinite;
        }
        
        @keyframes pulseBar {
            0%, 100% { height: 60%; }
            50% { height: 100%; }
        }
        
        .animation-dataflow {
            animation: moveDataPacket 2s infinite linear;
        }
        
        @keyframes moveDataPacket {
            0% { transform: translateX(0); opacity: 0; }
            20% { opacity: 1; }
            80% { opacity: 1; }
            100% { transform: translateX(180px); opacity: 0; }
        }
        
        .typing-cursor {
            animation: blink 0.8s step-end infinite;
        }
        
        @keyframes blink {
            0%, 100% { opacity: 1; }
            50% { opacity: 0; }
        }
        
        .typing-progress {
            animation: typing 3s forwards;
            animation-delay: 1s;
        }
        
        @keyframes typing {
            0% { width: 0%; }
            100% { width: 100%; }
        }
        
        .orbit-particle {
            animation: orbit 8s linear infinite;
        }
        
        @keyframes orbit {
            0% { transform: rotate(0deg) translateX(14px) rotate(0deg); }
            100% { transform: rotate(360deg) translateX(14px) rotate(-360deg); }
        }
        
        .current-metric {
            animation: growMetric 2s forwards;
        }
        
        @keyframes growMetric {
            0% { width: 0%; }
            100% { width: 100%; }
        }
        
        .pulse-indicator {
            animation: pulseIndicator 2s infinite;
        }
        
        @keyframes pulseIndicator {
            0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
            50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.8; }
        }
    </style>
    
    <script>
        // Animation for the workflow visualization
        document.addEventListener('DOMContentLoaded', function() {
            // Original openModal function reference
            const originalOpenModal = window.openModal;
            
            // Override openModal to add our custom animations for the save & process modal
            window.openModal = function(modalId) {
                // Call the original function
                if (typeof originalOpenModal === 'function') {
                    originalOpenModal(modalId);
                } else {
                    // Fallback if original function doesn't exist
                    const modal = document.getElementById(modalId);
                    if (modal) {
                        modal.classList.remove('hidden');
                        setTimeout(() => {
                            const content = modal.querySelector('.modal-content');
                            if (content) {
                                content.classList.add('scale-100', 'opacity-100');
                                content.classList.remove('scale-95', 'opacity-0');
                            }
                        }, 10);
                    }
                }
                
                // Add custom animations for our specific modal
                if (modalId === 'saveProcessModal') {
                    setTimeout(() => {
                        startSaveProcessAnimations();
                    }, 500);
                }
            };
            
            function startSaveProcessAnimations() {
                // File processing animation
                const processingIndicator = document.querySelector('.processing-indicator');
                if (processingIndicator) {
                    processingIndicator.style.width = '0%';
                    let progress = 0;
                    const progressInterval = setInterval(() => {
                        progress += 1;
                        processingIndicator.style.width = `${progress}%`;
                        
                        if (progress >= 100) {
                            clearInterval(progressInterval);
                            
                            // Show output files
                            setTimeout(() => {
                                document.querySelector('.files-output').style.opacity = '1';
                            }, 300);
                        }
                    }, 40);
                }
                
                // Typing effect for document generation
                setTimeout(() => {
                    const aiBrainOverlay = document.querySelector('.ai-brain-overlay');
                    if (aiBrainOverlay) {
                        aiBrainOverlay.style.opacity = '1';
                        
                        // Hide ai brain after a few seconds
                        setTimeout(() => {
                            aiBrainOverlay.style.opacity = '0';
                            
                            // Move cursor for typing effect
                            setTimeout(() => {
                                const cursor = document.querySelector('.typing-cursor');
                                if (cursor) {
                                    cursor.style.left = '130px';
                                    cursor.style.top = '100px';
                                    
                                    // Start progress bars for report generation
                                    document.querySelectorAll('.typing-progress').forEach((bar, index) => {
                                        bar.style.animationDelay = `${1 + (index * 0.5)}s`;
                                    });
                                }
                            }, 500);
                        }, 3000);
                    }
                }, 2000);
            }
            
            // Function to clean up animations when modal closes
            window.closeModal = function(modalId) {
                const modal = document.getElementById(modalId);
                if (modal) {
                    const content = modal.querySelector('.modal-content');
                    if (content) {
                        content.classList.remove('scale-100', 'opacity-100');
                        content.classList.add('scale-95', 'opacity-0');
                    }
                    setTimeout(() => {
                        modal.classList.add('hidden');
                    }, 300);
                }
            };
        });
    </script>
</body>
</html>
        