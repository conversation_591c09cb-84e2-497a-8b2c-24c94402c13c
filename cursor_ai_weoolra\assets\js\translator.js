// Translator script for Wellora application

// Initialize language on page load
document.addEventListener('DOMContentLoaded', function() {
    // Get the language from localStorage or default to English
    const currentLang = localStorage.getItem('wellora_language') || 'en';
    
    // Apply the current language
    applyLanguage(currentLang);
    
    // Update the language switcher buttons
    updateLanguageSwitcherUI(currentLang);
});

// Function to change the language
function changeLanguage(lang) {
    // Save the language preference to localStorage
    localStorage.setItem('wellora_language', lang);
    
    // Apply the selected language
    applyLanguage(lang);
    
    // Update the language switcher buttons
    updateLanguageSwitcherUI(lang);
}

// Function to apply translations based on the selected language
function applyLanguage(lang) {
    // Get all elements with data-i18n attribute
    const elements = document.querySelectorAll('[data-i18n]');
    
    // Loop through each element and apply the translation
    elements.forEach(el => {
        const key = el.getAttribute('data-i18n');
        
        // Check if the translation exists for the key
        if (translations[lang] && translations[lang][key]) {
            // If the element contains HTML, use innerHTML
            if (translations[lang][key].includes('<') && translations[lang][key].includes('>')) {
                el.innerHTML = translations[lang][key];
            } else {
                // Otherwise use textContent
                el.textContent = translations[lang][key];
            }
        }
    });
    
    // Update the page title
    if (translations[lang] && translations[lang]['page_title']) {
        document.title = translations[lang]['page_title'];
    }
    
    // Dispatch a custom event to notify other components that the language has changed
    document.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: lang } }));
}

// Update the UI of language switcher buttons
function updateLanguageSwitcherUI(activeLang) {
    const langButtons = document.querySelectorAll('.lang-button');
    
    langButtons.forEach(button => {
        const buttonLang = button.getAttribute('data-lang');
        
        if (buttonLang === activeLang) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }
    });
}
