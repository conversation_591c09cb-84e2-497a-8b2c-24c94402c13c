// translator.js - Translation utility functions for Wellora website

// Default language
let currentLanguage = 'en';

// Initialize translation system
function initTranslator() {
  // Check if language is stored in local storage
  const savedLanguage = localStorage.getItem('wellora_language');
  if (savedLanguage) {
    currentLanguage = savedLanguage;
    
    // Apply the saved language
    currentLanguage = savedLanguage;
    // Apply translations with current language
    applyTranslations();
    // Update language switcher to show current language
    updateLanguageSwitcher();
    return;
  }
  
  // Update language switcher to show current language
  updateLanguageSwitcher();
  
  // Apply translations with current language
  applyTranslations();
}

// Function to change language
function changeLanguage(langCode) {
  if (translations[langCode]) {
    // Set current language
    currentLanguage = langCode;
    localStorage.setItem('wellora_language', langCode);
    updateLanguageSwitcher();
    
    console.log("Changing language to: " + langCode);
    
    // FIRST PASS: Apply all translations
    document.querySelectorAll('[data-i18n]').forEach(element => {
      const key = element.getAttribute('data-i18n');
      if (translations[langCode][key]) {
        // Handle HTML in translations
        if (translations[langCode][key].includes('<')) {
          element.innerHTML = translations[langCode][key];
        } else {
          element.textContent = translations[langCode][key];
        }
      }
    });
    
    // Apply translations to placeholder attributes
    document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
      const key = element.getAttribute('data-i18n-placeholder');
      if (translations[langCode][key]) {
        element.placeholder = translations[langCode][key];
      }
    });
    
    // CRITICAL: Also update script.js translation system (data-lang-key elements)
    // Sync the two translation systems
    localStorage.setItem('preferredLanguage', langCode);
    if (window.applyTranslations) {
      console.log("Calling script.js applyTranslations with:", langCode);
      window.applyTranslations(langCode);
    } else {
      console.log("Manually applying translations to data-lang-key elements");
      // Manually apply translations to elements with data-lang-key
      document.querySelectorAll('[data-lang-key]').forEach(element => {
        const key = element.getAttribute('data-lang-key');
        const scriptTranslations = window.translations || {};
        if (scriptTranslations[langCode] && scriptTranslations[langCode][key]) {
          element.innerHTML = scriptTranslations[langCode][key];
        }
      });
    }
    
    // Force refresh the hero visualization elements - direct targeting
    if (langCode === 'en') {
      document.querySelectorAll('[data-i18n="patient_label"]').forEach(el => el.textContent = "Patient");
      document.querySelectorAll('[data-i18n="therapist_label"]').forEach(el => el.textContent = "Therapist");
      document.querySelectorAll('[data-i18n="patient_message"]').forEach(el => el.textContent = "I've been feeling anxious and having trouble sleeping lately.");
      document.querySelectorAll('[data-i18n="ai_analysis_title"]').forEach(el => el.textContent = "AI Analysis");
      document.querySelectorAll('[data-i18n="ai_realtime_insights"]').forEach(el => el.textContent = "Real-time insights");
      document.querySelectorAll('[data-i18n="processing_label"]').forEach(el => el.textContent = "Processing");
      document.querySelectorAll('[data-i18n="potential_pattern"]').forEach(el => el.textContent = "Potential Pattern Detected");
      document.querySelectorAll('[data-i18n="anxiety_pattern_desc"]').forEach(el => el.textContent = "Signs of generalized anxiety disorder with sleep disturbances");
      document.querySelectorAll('[data-i18n="suggested_approach"]').forEach(el => el.textContent = "Suggested Approach");
      document.querySelectorAll('[data-i18n="confidence_label"]').forEach(el => el.textContent = "Confidence");
      document.querySelectorAll('[data-i18n="recording_label"]').forEach(el => el.textContent = "Recording");
      document.querySelectorAll('[data-i18n="view_analysis_button"]').forEach(el => el.textContent = "View Analysis");
      
      // Force refresh How It Works section for English
      document.querySelectorAll('[data-i18n="howItWorksSection"]').forEach(el => el.textContent = "Simple Process");
      document.querySelectorAll('[data-i18n="workflowTitle"]').forEach(el => {
        el.innerHTML = "How <span class=\"gradient-text\">Wellora</span> Works";
      });
      document.querySelectorAll('[data-i18n="workflowSubtitle"]').forEach(el => el.textContent = "Our streamlined workflow makes it easy to incorporate AI-powered insights into your practice.");
      document.querySelectorAll('[data-i18n="startRecording"]').forEach(el => el.textContent = "Record");
      document.querySelectorAll('[data-i18n="startRecordingDesc"]').forEach(el => el.textContent = "Capture patient conversations with high-quality, secure recording technology.");
      document.querySelectorAll('[data-i18n="processWithAI"]').forEach(el => el.textContent = "Analyze");
      document.querySelectorAll('[data-i18n="processWithAIDesc"]').forEach(el => el.textContent = "Our AI processes the conversation to identify patterns and crucial information.");
      document.querySelectorAll('[data-i18n="deliverInsights"]').forEach(el => el.textContent = "Implement");
      document.querySelectorAll('[data-i18n="deliverInsightsDesc"]').forEach(el => el.textContent = "Access insights and apply recommended therapeutic approaches for better care.");
      document.querySelectorAll('[data-i18n="saveDocTime"]').forEach(el => el.innerHTML = "<i class=\"fas fa-clock mr-1\"></i> 60% Time Saved");
      document.querySelectorAll('[data-i18n="aiModels"]').forEach(el => el.innerHTML = "<i class=\"fas fa-brain mr-1\"></i> 15+ AI Models");
      document.querySelectorAll('[data-i18n="improveCareQuality"]').forEach(el => el.innerHTML = "<i class=\"fas fa-chart-line mr-1\"></i> 40% Better Outcomes");
      
      // Force refresh Benefits section for English
      document.querySelectorAll('[data-i18n="whyChooseUs"]').forEach(el => el.textContent = "Why Choose Us");
      document.querySelectorAll('[data-i18n="benefitsTitle"]').forEach(el => {
        el.innerHTML = "Benefits for <span class=\"gradient-text\">Healthcare Professionals</span>";
      });
      document.querySelectorAll('[data-i18n="benefitsDescription"]').forEach(el => el.textContent = "Wellora enhances your practice by providing AI-powered insights, saving time on documentation, and helping you deliver more personalized care to your patients.");
      document.querySelectorAll('[data-i18n="saveDocTimeTitle"]').forEach(el => el.textContent = "Save Documentation Time");
      document.querySelectorAll('[data-i18n="saveDocTimeDesc"]').forEach(el => el.textContent = "Reduce documentation time by up to 60% with automated transcription and report generation, allowing you to focus more on patient care.");
      document.querySelectorAll('[data-i18n="uncoverPatternsTitle"]').forEach(el => el.textContent = "Uncover Subtle Patterns");
      document.querySelectorAll('[data-i18n="uncoverPatternsDesc"]').forEach(el => el.textContent = "AI algorithms detect nuanced patterns in conversations that might be missed during the session, providing valuable insights for treatment.");
      document.querySelectorAll('[data-i18n="dataDecisionsTitle"]').forEach(el => el.textContent = "Data-Driven Decisions");
      document.querySelectorAll('[data-i18n="dataDecisionsDesc"]').forEach(el => el.textContent = "Make evidence-based therapeutic decisions using AI-generated recommendations backed by clinical research and analysis.");
      document.querySelectorAll('[data-i18n="requestDemoButton"]').forEach(el => el.textContent = "Request a Demo");
      document.querySelectorAll('[data-i18n="patternDetected"]').forEach(el => el.textContent = "Pattern Detected");
      document.querySelectorAll('[data-i18n="patternDetectedDesc"]').forEach(el => el.textContent = "Patient shows signs of improvement in anxiety management techniques");
      document.querySelectorAll('[data-i18n="progressTracker"]').forEach(el => el.textContent = "Progress Tracker");
      document.querySelectorAll('[data-i18n="progressTrackerDesc"]').forEach(el => el.textContent = "Session-to-session improvement rates increased by 27%");
      
      // Force refresh Testimonials section for English
      document.querySelectorAll('[data-i18n="testimonials_tag"]').forEach(el => el.textContent = "Client Stories");
      document.querySelectorAll('[data-i18n="testimonials_title"]').forEach(el => {
        el.innerHTML = "What Our <span class=\"gradient-text\">Users Say</span>";
      });
      document.querySelectorAll('[data-i18n="testimonials_description"]').forEach(el => el.textContent = "Hear from healthcare professionals who have transformed their practice with Wellora.");
      document.querySelectorAll('[data-i18n="testimonial_1"]').forEach(el => el.textContent = "\"Wellora has transformed my practice. I can now focus more on patient interaction while getting valuable insights I might have missed. The time saved on documentation alone has made this investment worthwhile.\"");
      document.querySelectorAll('[data-i18n="testimonial_1_name"]').forEach(el => el.textContent = "Dr. Jonathan Harris");
      document.querySelectorAll('[data-i18n="testimonial_1_title"]').forEach(el => el.textContent = "Clinical Psychologist");
      document.querySelectorAll('[data-i18n="testimonial_2"]').forEach(el => el.textContent = "\"The AI-powered insights have helped me identify patterns in patient behavior that I had overlooked for months. This tool has become an indispensable part of my practice and has improved outcomes for many of my patients.\"");
      document.querySelectorAll('[data-i18n="testimonial_2_name"]').forEach(el => el.textContent = "Dr. Sarah Matthews");
      document.querySelectorAll('[data-i18n="testimonial_2_title"]').forEach(el => el.textContent = "Psychiatrist");
      document.querySelectorAll('[data-i18n="testimonial_3"]').forEach(el => el.textContent = "\"As a busy therapist with multiple patients, Wellora helps me stay organized and provides insightful summaries of each session. The AI recommendations have been surprisingly accurate and helpful for treatment planning.\"");
      document.querySelectorAll('[data-i18n="testimonial_3_name"]').forEach(el => el.textContent = "Dr. Michael Chen");
      document.querySelectorAll('[data-i18n="testimonial_3_title"]').forEach(el => el.textContent = "Therapist");
      document.querySelectorAll('[data-i18n="testimonial_4"]').forEach(el => el.textContent = "\"The HIPAA compliance and security features give me peace of mind when recording sensitive patient discussions. The AI insights have helped me improve my therapeutic approach and achieve better patient outcomes.\"");
      document.querySelectorAll('[data-i18n="testimonial_4_name"]').forEach(el => el.textContent = "Dr. Emily Rodriguez");
      document.querySelectorAll('[data-i18n="testimonial_4_title"]').forEach(el => el.textContent = "Clinical Psychologist");
      document.querySelectorAll('[data-i18n="accuracy_rate"]').forEach(el => el.textContent = "Accuracy Rate");
      document.querySelectorAll('[data-i18n="time_saved"]').forEach(el => el.textContent = "Time Saved");
      document.querySelectorAll('[data-i18n="ai_models_count"]').forEach(el => el.textContent = "AI Models");
      document.querySelectorAll('[data-i18n="users_count"]').forEach(el => el.textContent = "Users");
      
      // Force refresh Contact section for English
      document.querySelectorAll('[data-i18n="contact_tag"]').forEach(el => el.textContent = "Get In Touch");
      document.querySelectorAll('[data-i18n="contact_title"]').forEach(el => {
        el.innerHTML = "Have Questions? <span class=\"gradient-text\">Contact Us</span>";
      });
      document.querySelectorAll('[data-i18n-placeholder="name_placeholder"]').forEach(el => el.placeholder = "Your name");
      document.querySelectorAll('[data-i18n="email_address"]').forEach(el => el.textContent = "Email Address");
      document.querySelectorAll('[data-i18n-placeholder="email_placeholder"]').forEach(el => el.placeholder = "<EMAIL>");
      document.querySelectorAll('[data-i18n="subject"]').forEach(el => el.textContent = "Subject");
      document.querySelectorAll('[data-i18n-placeholder="subject_placeholder"]').forEach(el => el.placeholder = "How can we help?");
      document.querySelectorAll('[data-i18n="message"]').forEach(el => el.textContent = "Message");
      document.querySelectorAll('[data-i18n-placeholder="message_placeholder"]').forEach(el => el.placeholder = "Your message...");
      document.querySelectorAll('[data-i18n="send_message"]').forEach(el => el.textContent = "Send Message");
      document.querySelectorAll('[data-i18n="contact_info"]').forEach(el => el.textContent = "Contact Information");
      document.querySelectorAll('[data-i18n="our_office"]').forEach(el => el.textContent = "Our Office");
      document.querySelectorAll('[data-i18n="office_address"]').forEach(el => el.innerHTML = "123 AI Healthcare Blvd<br>San Francisco, CA 94107<br>United States");
      document.querySelectorAll('[data-i18n="email_us"]').forEach(el => el.textContent = "Email Us");
      document.querySelectorAll('[data-i18n="email_contacts"]').forEach(el => el.innerHTML = "<EMAIL><br><EMAIL>");
      document.querySelectorAll('[data-i18n="call_us"]').forEach(el => el.textContent = "Call Us");
      document.querySelectorAll('[data-i18n="phone_numbers"]').forEach(el => el.innerHTML = "+****************<br>+****************");
      document.querySelectorAll('[data-i18n="working_hours"]').forEach(el => el.textContent = "Working Hours");
      document.querySelectorAll('[data-i18n="hours_details"]').forEach(el => el.innerHTML = "Monday - Friday: 9AM - 5PM<br>Saturday: 10AM - 2PM");
      
      // Force refresh CTA section for English
      document.querySelectorAll('[data-i18n="cta_title"]').forEach(el => el.textContent = "Ready to Transform Your Practice with AI-Powered Insights?");
      document.querySelectorAll('[data-i18n="cta_description"]').forEach(el => el.textContent = "Join thousands of healthcare professionals already benefiting from Wellora's conversation analysis technology.");
      document.querySelectorAll('[data-i18n="request_demo_button"]').forEach(el => el.textContent = "Request a Demo");
      document.querySelectorAll('[data-i18n="contact_sales"]').forEach(el => el.textContent = "Contact Sales");
      document.querySelectorAll('[data-i18n="language_label"]').forEach(el => el.textContent = "Language:");
    } else if (langCode === 'nl') {
      document.querySelectorAll('[data-i18n="patient_label"]').forEach(el => el.textContent = "Patiënt");
      document.querySelectorAll('[data-i18n="therapist_label"]').forEach(el => el.textContent = "Therapeut");
      document.querySelectorAll('[data-i18n="patient_message"]').forEach(el => el.textContent = "Ik voel me de laatste tijd angstig en heb slaapproblemen.");
      document.querySelectorAll('[data-i18n="ai_analysis_title"]').forEach(el => el.textContent = "AI-Analyse");
      document.querySelectorAll('[data-i18n="ai_realtime_insights"]').forEach(el => el.textContent = "Realtime inzichten");
      document.querySelectorAll('[data-i18n="processing_label"]').forEach(el => el.textContent = "Verwerken");
      document.querySelectorAll('[data-i18n="potential_pattern"]').forEach(el => el.textContent = "Mogelijk patroon gedetecteerd");
      document.querySelectorAll('[data-i18n="anxiety_pattern_desc"]').forEach(el => el.textContent = "Tekenen van gegeneraliseerde angststoornis met slaapstoornissen");
      document.querySelectorAll('[data-i18n="suggested_approach"]').forEach(el => el.textContent = "Voorgestelde aanpak");
      document.querySelectorAll('[data-i18n="confidence_label"]').forEach(el => el.textContent = "Betrouwbaarheid");
      document.querySelectorAll('[data-i18n="recording_label"]').forEach(el => el.textContent = "Opname");
      document.querySelectorAll('[data-i18n="view_analysis_button"]').forEach(el => el.textContent = "Analyse bekijken");
      
      // Force refresh How It Works section for Dutch
      document.querySelectorAll('[data-i18n="howItWorksSection"]').forEach(el => el.textContent = "Eenvoudig Proces");
      document.querySelectorAll('[data-i18n="workflowTitle"]').forEach(el => {
        el.innerHTML = "Hoe <span class=\"gradient-text\">Wellora</span> Werkt";
      });
      document.querySelectorAll('[data-i18n="workflowSubtitle"]').forEach(el => el.textContent = "Onze gestroomlijnde workflow maakt het gemakkelijk om AI-gestuurde inzichten in uw praktijk te integreren.");
      document.querySelectorAll('[data-i18n="startRecording"]').forEach(el => el.textContent = "Opnemen");
      document.querySelectorAll('[data-i18n="startRecordingDesc"]').forEach(el => el.textContent = "Leg patiëntgesprekken vast met hoogwaardige, beveiligde opnametechnologie.");
      document.querySelectorAll('[data-i18n="processWithAI"]').forEach(el => el.textContent = "Analyseren");
      document.querySelectorAll('[data-i18n="processWithAIDesc"]').forEach(el => el.textContent = "Onze AI verwerkt het gesprek om patronen en belangrijke informatie te identificeren.");
      document.querySelectorAll('[data-i18n="deliverInsights"]').forEach(el => el.textContent = "Implementeren");
      document.querySelectorAll('[data-i18n="deliverInsightsDesc"]').forEach(el => el.textContent = "Krijg toegang tot inzichten en pas aanbevolen therapeutische benaderingen toe voor betere zorg.");
      document.querySelectorAll('[data-i18n="saveDocTime"]').forEach(el => el.innerHTML = "<i class=\"fas fa-clock mr-1\"></i> 60% Tijdsbesparing");
      document.querySelectorAll('[data-i18n="aiModels"]').forEach(el => el.innerHTML = "<i class=\"fas fa-brain mr-1\"></i> 15+ AI-modellen");
      document.querySelectorAll('[data-i18n="improveCareQuality"]').forEach(el => el.innerHTML = "<i class=\"fas fa-chart-line mr-1\"></i> 40% Betere Resultaten");
      
      // Force refresh Benefits section for Dutch
      document.querySelectorAll('[data-i18n="whyChooseUs"]').forEach(el => el.textContent = "Waarom ons kiezen");
      document.querySelectorAll('[data-i18n="benefitsTitle"]').forEach(el => {
        el.innerHTML = "Voordelen voor <span class=\"gradient-text\">Zorgprofessionals</span>";
      });
      document.querySelectorAll('[data-i18n="benefitsDescription"]').forEach(el => el.textContent = "Wellora verbetert uw praktijk door AI-gestuurde inzichten te bieden, tijd te besparen op documentatie en u te helpen meer gepersonaliseerde zorg aan uw patiënten te leveren.");
      document.querySelectorAll('[data-i18n="saveDocTimeTitle"]').forEach(el => el.textContent = "Bespaar Documentatietijd");
      document.querySelectorAll('[data-i18n="saveDocTimeDesc"]').forEach(el => el.textContent = "Verminder documentatietijd tot wel 60% met geautomatiseerde transcriptie en rapportgeneratie, waardoor u zich meer kunt richten op patiëntenzorg.");
      document.querySelectorAll('[data-i18n="uncoverPatternsTitle"]').forEach(el => el.textContent = "Ontdek Subtiele Patronen");
      document.querySelectorAll('[data-i18n="uncoverPatternsDesc"]').forEach(el => el.textContent = "AI-algoritmes detecteren genuanceerde patronen in gesprekken die mogelijk tijdens de sessie over het hoofd worden gezien, wat waardevolle inzichten oplevert voor de behandeling.");
      document.querySelectorAll('[data-i18n="dataDecisionsTitle"]').forEach(el => el.textContent = "Datagestuurde Beslissingen");
      document.querySelectorAll('[data-i18n="dataDecisionsDesc"]').forEach(el => el.textContent = "Neem evidence-based therapeutische beslissingen met behulp van door AI gegenereerde aanbevelingen ondersteund door klinisch onderzoek en analyse.");
      document.querySelectorAll('[data-i18n="requestDemoButton"]').forEach(el => el.textContent = "Demo Aanvragen");
      document.querySelectorAll('[data-i18n="patternDetected"]').forEach(el => el.textContent = "Patroon Gedetecteerd");
      document.querySelectorAll('[data-i18n="patternDetectedDesc"]').forEach(el => el.textContent = "Patiënt toont tekenen van verbetering in angstbeheersingstechnieken");
      document.querySelectorAll('[data-i18n="progressTracker"]').forEach(el => el.textContent = "Voortgangstracker");
      document.querySelectorAll('[data-i18n="progressTrackerDesc"]').forEach(el => el.textContent = "Sessie-tot-sessie verbeteringspercentages zijn met 27% gestegen");
      
      // Force refresh Testimonials section for Dutch
      document.querySelectorAll('[data-i18n="testimonials_tag"]').forEach(el => el.textContent = "Klantenverhalen");
      document.querySelectorAll('[data-i18n="testimonials_title"]').forEach(el => {
        el.innerHTML = "Wat onze <span class=\"gradient-text\">gebruikers zeggen</span>";
      });
      document.querySelectorAll('[data-i18n="testimonials_description"]').forEach(el => el.textContent = "Hoor van gezondheidszorgprofessionals die hun praktijk hebben getransformeerd met Wellora.");
      document.querySelectorAll('[data-i18n="testimonial_1"]').forEach(el => el.textContent = "\"Wellora heeft mijn praktijk getransformeerd. Ik kan me nu meer richten op patiëntinteractie terwijl ik waardevolle inzichten krijg die ik mogelijk had gemist. Alleen al de tijdsbesparing op documentatie heeft deze investering de moeite waard gemaakt.\"");
      document.querySelectorAll('[data-i18n="testimonial_1_name"]').forEach(el => el.textContent = "Dr. Jonathan Harris");
      document.querySelectorAll('[data-i18n="testimonial_1_title"]').forEach(el => el.textContent = "Klinisch Psycholoog");
      document.querySelectorAll('[data-i18n="testimonial_2"]').forEach(el => el.textContent = "\"De door AI aangedreven inzichten hebben me geholpen patronen in patiëntgedrag te identificeren die ik maandenlang over het hoofd had gezien. Deze tool is een onmisbaar onderdeel van mijn praktijk geworden en heeft de resultaten voor veel van mijn patiënten verbeterd.\"");
      document.querySelectorAll('[data-i18n="testimonial_2_name"]').forEach(el => el.textContent = "Dr. Sarah Matthews");
      document.querySelectorAll('[data-i18n="testimonial_2_title"]').forEach(el => el.textContent = "Psychiater");
      document.querySelectorAll('[data-i18n="testimonial_3"]').forEach(el => el.textContent = "\"Als een drukke therapeut met meerdere patiënten helpt Wellora me georganiseerd te blijven en biedt het inzichtelijke samenvattingen van elke sessie. De AI-aanbevelingen zijn verrassend nauwkeurig en nuttig voor de behandelplanning.\"");
      document.querySelectorAll('[data-i18n="testimonial_3_name"]').forEach(el => el.textContent = "Dr. Michael Chen");
      document.querySelectorAll('[data-i18n="testimonial_3_title"]').forEach(el => el.textContent = "Therapeut");
      document.querySelectorAll('[data-i18n="testimonial_4"]').forEach(el => el.textContent = "\"De HIPAA-compliance en beveiligingsfuncties geven me gemoedsrust bij het opnemen van gevoelige patiëntgesprekken. De AI-inzichten hebben me geholpen mijn therapeutische aanpak te verbeteren en betere resultaten voor patiënten te bereiken.\"");
      document.querySelectorAll('[data-i18n="testimonial_4_name"]').forEach(el => el.textContent = "Dr. Emily Rodriguez");
      document.querySelectorAll('[data-i18n="testimonial_4_title"]').forEach(el => el.textContent = "Klinisch Psycholoog");
      document.querySelectorAll('[data-i18n="accuracy_rate"]').forEach(el => el.textContent = "Nauwkeurigheidsgraad");
      document.querySelectorAll('[data-i18n="time_saved"]').forEach(el => el.textContent = "Tijd Bespaard");
      document.querySelectorAll('[data-i18n="ai_models_count"]').forEach(el => el.textContent = "AI-modellen");
      document.querySelectorAll('[data-i18n="users_count"]').forEach(el => el.textContent = "Gebruikers");
    }
    
    // Fix specifically targeted elements that are known to cause issues
    fixHeroVisualizationSection(langCode);
    fixHowItWorksSection(langCode);
    
    // Handle special cases for both languages
    if (langCode === 'en') {
      // Apply special fixes for English
      const fixSpecialEnglish = () => {
        // Special handling for known problematic elements
        const problemSeverityElements = document.querySelectorAll('[data-i18n="problem_severity"]');
        problemSeverityElements.forEach(element => {
          element.textContent = "Problem Severity";
        });
        
        document.querySelectorAll('[data-i18n="anxiety_chart_label"]').forEach(element => {
          element.textContent = "■ Anxiety";
        });
        
        document.querySelectorAll('[data-i18n="sleep_chart_label"]').forEach(element => {
          element.textContent = "■ Sleep";
        });
        
        document.querySelectorAll('[data-i18n="stress_chart_label"]').forEach(element => {
          element.textContent = "■ Stress";
        });
        
        // Also update specific elements by ID
        const problemSeverityElement = document.getElementById('problem_severity_text');
        if (problemSeverityElement) {
          problemSeverityElement.textContent = "Problem Severity";
        }
        
        console.log("Applied special English fixes");
      };
      
      // Apply fixes immediately and again after a delay to ensure complete application
      fixSpecialEnglish();
      setTimeout(fixSpecialEnglish, 200);
      setTimeout(function() {
        // Force re-apply all English translations in the AI visualization section
        fixHeroVisualizationSection('en');
        fixHowItWorksSection('en');
        
        // Also reapply script.js translations for additional assurance
        if (window.applyTranslations) {
          window.applyTranslations('en');
        }
      }, 300);
    }
    else if (langCode === 'nl') {
      // Handle Dutch special cases if needed
      const fixSpecialDutch = () => {
        // Special handling for Dutch specific elements
        const problemSeverityElements = document.querySelectorAll('[data-i18n="problem_severity"]');
        problemSeverityElements.forEach(element => {
          element.textContent = "Ernst van Problemen";
        });
        
        document.querySelectorAll('[data-i18n="anxiety_chart_label"]').forEach(element => {
          element.textContent = "■ Angst";
        });
        
        document.querySelectorAll('[data-i18n="sleep_chart_label"]').forEach(element => {
          element.textContent = "■ Slaap";
        });
        
        document.querySelectorAll('[data-i18n="stress_chart_label"]').forEach(element => {
          element.textContent = "■ Stress";
        });
        
        // Update elements by ID
        const problemSeverityElement = document.getElementById('problem_severity_text');
        if (problemSeverityElement) {
          problemSeverityElement.textContent = "Ernst van Problemen";
        }
        
        console.log("Applied special Dutch fixes");
      };
      
      fixSpecialDutch();
      setTimeout(fixSpecialDutch, 200);
      
      // Force reapply script.js translations
      setTimeout(function() {
        if (window.applyTranslations) {
          window.applyTranslations('nl');
        }
      }, 300);
    }

    // Force refresh testimonials section for both languages
    const testimonialKeys = [
      'testimonials_desc',
      'testimonial1_initials', 'testimonial1_name', 'testimonial1_title', 'testimonial1_quote',
      'testimonial2_initials', 'testimonial2_name', 'testimonial2_title', 'testimonial2_quote',
      'testimonial3_initials', 'testimonial3_name', 'testimonial3_title', 'testimonial3_quote'
    ];
    testimonialKeys.forEach(key => {
      document.querySelectorAll(`[data-i18n="${key}"]`).forEach(el => {
        if (translations[currentLanguage] && translations[currentLanguage][key]) {
          el.textContent = translations[currentLanguage][key];
        }
      });
    });

    // At the end of changeLanguage function, force update testimonials_desc
    const testimonialsDesc = document.querySelector('[data-i18n="testimonials_desc"]');
    if (testimonialsDesc && translations[currentLanguage]['testimonials_desc']) {
      testimonialsDesc.innerHTML = translations[currentLanguage]['testimonials_desc'];
    }
  }
}

// Special function to fix the hero visualization section
function fixHeroVisualizationSection(langCode) {
  if (!translations[langCode]) return;
  
  console.log("Fixing Hero visualization section for language:", langCode);
  
  // First, target the entire hero section to make sure we catch everything
  const heroSection = document.querySelector('section.relative.py-20.md\\:py-32.overflow-hidden');
  if (heroSection) {
    console.log("Found hero section, applying translations to all data-i18n elements");
    
    // Apply translations to all elements with data-i18n in the hero section
    heroSection.querySelectorAll('[data-i18n]').forEach(element => {
      const key = element.getAttribute('data-i18n');
      if (translations[langCode][key]) {
        if (translations[langCode][key].includes('<')) {
          element.innerHTML = translations[langCode][key];
        } else {
          element.textContent = translations[langCode][key];
        }
        console.log(`Fixed hero element: ${key} -> ${translations[langCode][key].substring(0, 30)}...`);
      }
    });
  } else {
    console.warn("Could not find hero section");
  }
  
  // Now specifically target the AI visualization part
  const heroVisualization = document.querySelector('.md\\:w-1\\/2.relative.z-10 .relative .relative.w-full.max-w-lg');
  if (heroVisualization) {
    console.log("Found AI visualization component, applying translations");
    
    // Force translate specific problematic elements directly
    const specificElements = {
      'patient_label': heroVisualization.querySelector('[data-i18n="patient_label"]'),
      'patient_message': heroVisualization.querySelector('[data-i18n="patient_message"]'),
      'therapist_label': heroVisualization.querySelector('[data-i18n="therapist_label"]'),
      'therapist_message': heroVisualization.querySelector('[data-i18n="therapist_message"]'),
      'ai_analysis_title': heroVisualization.querySelector('[data-i18n="ai_analysis_title"]'),
      'ai_realtime_insights': heroVisualization.querySelector('[data-i18n="ai_realtime_insights"]'),
      'processing_label': heroVisualization.querySelector('[data-i18n="processing_label"]'),
      'potential_pattern': heroVisualization.querySelector('[data-i18n="potential_pattern"]'),
      'anxiety_pattern_desc': heroVisualization.querySelector('[data-i18n="anxiety_pattern_desc"]'),
      'suggested_approach': heroVisualization.querySelector('[data-i18n="suggested_approach"]'),
      'approach_desc': heroVisualization.querySelector('[data-i18n="approach_desc"]'),
      'confidence_label': heroVisualization.querySelector('[data-i18n="confidence_label"]'),
      'severity_label': heroVisualization.querySelector('[data-i18n="severity_label"]'),
      'confidence_value': heroVisualization.querySelector('[data-i18n="confidence_value"]'),
      'severity_value': heroVisualization.querySelector('[data-i18n="severity_value"]'),
      'recording_label': heroVisualization.querySelector('[data-i18n="recording_label"]'),
      'view_analysis_button': heroVisualization.querySelector('[data-i18n="view_analysis_button"]')
    };
    
    // Apply translations to each specific element
    for (const [key, element] of Object.entries(specificElements)) {
      if (element && translations[langCode][key]) {
        if (translations[langCode][key].includes('<')) {
          element.innerHTML = translations[langCode][key];
        } else {
          element.textContent = translations[langCode][key];
        }
        console.log(`Directly fixed visualization element: ${key}`);
      }
    }
    
    // Additionally, query each data-i18n element again to make sure
    heroVisualization.querySelectorAll('[data-i18n]').forEach(element => {
      const key = element.getAttribute('data-i18n');
      if (translations[langCode][key]) {
        if (translations[langCode][key].includes('<')) {
          element.innerHTML = translations[langCode][key];
        } else {
          element.textContent = translations[langCode][key];
        }
      }
    });
  } else {
    console.warn("Could not find AI visualization component");
  }
}

// Special function to fix the "how it works" section
function fixHowItWorksSection(langCode) {
  if (!translations[langCode]) return;
  
  console.log("Fixing How It Works section for language:", langCode);
  
  // Target the "how it works" section specifically
  const howItWorksSection = document.getElementById('how-it-works');
  if (howItWorksSection) {
    console.log("Found How It Works section");
    
    // Apply translations to all elements with data-i18n in this section
    howItWorksSection.querySelectorAll('[data-i18n]').forEach(element => {
      const key = element.getAttribute('data-i18n');
      if (translations[langCode][key]) {
        if (translations[langCode][key].includes('<')) {
          element.innerHTML = translations[langCode][key];
        } else {
          element.textContent = translations[langCode][key];
        }
        console.log(`Fixed how-it-works: ${key} -> ${translations[langCode][key].substring(0, 30)}...`);
      }
    });
    
    // Direct targeting of specific problematic elements
    const specificElements = {
      'howItWorksSection': howItWorksSection.querySelector('[data-i18n="howItWorksSection"]'),
      'workflowTitle': howItWorksSection.querySelector('[data-i18n="workflowTitle"]'),
      'workflowSubtitle': howItWorksSection.querySelector('[data-i18n="workflowSubtitle"]'),
      'startRecording': howItWorksSection.querySelector('[data-i18n="startRecording"]'),
      'startRecordingDesc': howItWorksSection.querySelector('[data-i18n="startRecordingDesc"]'),
      'processWithAI': howItWorksSection.querySelector('[data-i18n="processWithAI"]'),
      'processWithAIDesc': howItWorksSection.querySelector('[data-i18n="processWithAIDesc"]'),
      'deliverInsights': howItWorksSection.querySelector('[data-i18n="deliverInsights"]'),
      'deliverInsightsDesc': howItWorksSection.querySelector('[data-i18n="deliverInsightsDesc"]'),
      'saveDocTime': howItWorksSection.querySelector('[data-i18n="saveDocTime"]'),
      'aiModels': howItWorksSection.querySelector('[data-i18n="aiModels"]'),
      'improveCareQuality': howItWorksSection.querySelector('[data-i18n="improveCareQuality"]')
    };
    
    // Apply translations to each specific element
    for (const [key, element] of Object.entries(specificElements)) {
      if (element && translations[langCode][key]) {
        if (translations[langCode][key].includes('<')) {
          element.innerHTML = translations[langCode][key];
        } else {
          element.textContent = translations[langCode][key];
        }
        console.log(`Directly fixed how-it-works element: ${key}`);
      }
    }
    
    // Fix each step individually by finding all steps in the workflow
    const workflowSteps = howItWorksSection.querySelectorAll('.relative .bg-gray-800\\/50.backdrop-blur-sm');
    workflowSteps.forEach((step, index) => {
      console.log(`Checking workflow step ${index + 1}`);
      
      // Apply translations to all data-i18n elements in this step
      step.querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.getAttribute('data-i18n');
        if (translations[langCode][key]) {
          if (translations[langCode][key].includes('<')) {
            element.innerHTML = translations[langCode][key];
          } else {
            element.textContent = translations[langCode][key];
          }
          console.log(`Fixed step ${index + 1} element: ${key}`);
        }
      });
    });
  } else {
    console.warn("Could not find How It Works section");
  }
}

// Update language switcher UI
function updateLanguageSwitcher() {
  const langButtons = document.querySelectorAll('.lang-button');
  langButtons.forEach(button => {
    const lang = button.getAttribute('data-lang');
    if (lang === currentLanguage) {
      button.classList.add('active');
    } else {
      button.classList.remove('active');
    }
  });
}

// Apply translations to page elements
function applyTranslations() {
  // Get translation dictionary for current language
  const dictionary = translations[currentLanguage];
  if (!dictionary) return;
  
  // Find all elements with data-i18n attribute and translate them
  document.querySelectorAll('[data-i18n]').forEach(element => {
    const key = element.getAttribute('data-i18n');
    
    if (dictionary[key]) {
      // Special handling for chart labels and other problematic elements
      if (key === "problem_severity") {
        element.textContent = dictionary[key];
      } 
      else if (key === "anxiety_chart_label") {
        element.textContent = dictionary[key];
      }
      else if (key === "sleep_chart_label") {
        element.textContent = dictionary[key];
      }
      else if (key === "stress_chart_label") {
        element.textContent = dictionary[key];
      }
      // Normal translation handling for other elements
      else if (dictionary[key].includes('<')) {
        element.innerHTML = dictionary[key];
      } else {
        element.textContent = dictionary[key];
      }
    } else {
      console.warn(`Translation key not found: ${key} for language ${currentLanguage}`);
    }
  });
  
  // Also translate input placeholders
  document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
    const key = element.getAttribute('data-i18n-placeholder');
    if (dictionary[key]) {
      element.placeholder = dictionary[key];
    }
  });
  
  // Additional check for problem_severity by ID
  const problemSeverityElement = document.getElementById('problem_severity_text');
  if (problemSeverityElement && dictionary["problem_severity"]) {
    problemSeverityElement.textContent = dictionary["problem_severity"];
  }
  
  // Apply translations to contact section specifically
  applyContactSectionTranslations(dictionary);
  
  // Vertaal ook <option data-i18n> in <select> dropdowns
  const optionElements = document.querySelectorAll('option[data-i18n]');
  optionElements.forEach(option => {
    const key = option.getAttribute('data-i18n');
    if (translations[currentLanguage] && translations[currentLanguage][key]) {
      option.textContent = translations[currentLanguage][key];
    }
  });
  
  console.log(`Applied translations for language: ${currentLanguage}`);

  // In applyTranslations, after the main loop, force testimonials_desc update with innerHTML
  const testimonialsDesc = document.querySelector('[data-i18n="testimonials_desc"]');
  if (testimonialsDesc && translations[currentLanguage]['testimonials_desc']) {
    testimonialsDesc.innerHTML = translations[currentLanguage]['testimonials_desc'];
  }
}

// Function to specifically target contact section translations
function applyContactSectionTranslations(dictionary) {
  if (!dictionary) return;

  // Contact form elements
  const contactElements = {
    'full_name': document.querySelectorAll('[data-i18n="full_name"]'),
    'email_address': document.querySelectorAll('[data-i18n="email_address"]'),
    'subject': document.querySelectorAll('[data-i18n="subject"]'),
    'message': document.querySelectorAll('[data-i18n="message"]'),
    'send_message': document.querySelectorAll('[data-i18n="send_message"]'),
    'contact_info': document.querySelectorAll('[data-i18n="contact_info"]'),
    'our_office': document.querySelectorAll('[data-i18n="our_office"]'),
    'office_address': document.querySelectorAll('[data-i18n="office_address"]'),
    'contact_tag': document.querySelectorAll('[data-i18n="contact_tag"]'),
    'contact_title': document.querySelectorAll('[data-i18n="contact_title"]'),
    'contact_description': document.querySelectorAll('[data-i18n="contact_description"]')
  };

  // Apply translations to each contact element
  for (const [key, elements] of Object.entries(contactElements)) {
    if (dictionary[key]) {
      elements.forEach(element => {
        if (dictionary[key].includes('<')) {
          element.innerHTML = dictionary[key];
        } else {
          element.textContent = dictionary[key];
        }
      });
    }
  }

  // CTA section elements
  const ctaElements = {
    'cta_title': document.querySelectorAll('[data-i18n="cta_title"]'),
    'cta_description': document.querySelectorAll('[data-i18n="cta_description"]'),
    'request_demo_button': document.querySelectorAll('[data-i18n="request_demo_button"]'),
    'contact_sales': document.querySelectorAll('[data-i18n="contact_sales"]'),
    'language_label': document.querySelectorAll('[data-i18n="language_label"]')
  };

  // Apply translations to each CTA element
  for (const [key, elements] of Object.entries(ctaElements)) {
    if (dictionary[key]) {
      elements.forEach(element => {
        if (dictionary[key].includes('<')) {
          element.innerHTML = dictionary[key];
        } else {
          element.textContent = dictionary[key];
        }
      });
    }
  }
}

// Observeer DOM-mutaties en vertaal nieuwe data-i18n elementen automatisch
const i18nObserver = new MutationObserver(mutations => {
  mutations.forEach(mutation => {
    mutation.addedNodes.forEach(node => {
      if (node.nodeType === 1) {
        if (node.hasAttribute && node.hasAttribute('data-i18n')) {
          const key = node.getAttribute('data-i18n');
          if (translations[currentLanguage] && translations[currentLanguage][key]) {
            node.textContent = translations[currentLanguage][key];
          }
        }
        // Vertaal ook alle kinderen met data-i18n
        node.querySelectorAll && node.querySelectorAll('[data-i18n]').forEach(child => {
          const key = child.getAttribute('data-i18n');
          if (translations[currentLanguage] && translations[currentLanguage][key]) {
            child.textContent = translations[currentLanguage][key];
          }
        });
      }
    });
  });
});
i18nObserver.observe(document.body, { childList: true, subtree: true });

// Voeg een globale functie toe om dynamische content direct te vertalen
window.applyTranslationsToDynamicContent = function() {
  applyTranslations();
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initTranslator);