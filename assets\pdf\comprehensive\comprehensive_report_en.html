<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wellora Comprehensive Patient Report</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <style>
        :root {
            --primary: #0F6FFF;
            --primary-dark: #0A54C0;
            --primary-light: #E5F0FF;
            --secondary: #00B0B6;
            --tertiary: #00D695;
            --dark: #0A2540;
            --light: #F7F9FC;
            --gray-900: #111827;
            --gray-800: #1F2937;
            --gray-700: #374151;
            --gray-600: #4B5563;
            --gray-500: #6B7280;
            --gray-400: #9CA3AF;
            --gray-300: #D1D5DB;
            --gray-200: #E5E7EB;
            --gray-100: #F3F4F6;
            --red: #EF4444;
            --amber: #F59E0B;
            --green: #10B981;
            --blue: #3B82F6;
            --indigo: #6366F1;
            --purple: #8B5CF6;
            --pink: #EC4899;
            --teal: #14B8A6;
            --cyan: #06B6D4;
            --white: #FFFFFF;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        html {
            scroll-behavior: smooth;
            scroll-padding-top: 60px;
        }
        
        body {
            background-color: var(--light);
            color: var(--dark);
            line-height: 1.6;
            font-size: 14px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        header {
            background-color: var(--white);
            position: sticky;
            top: 0;
            z-index: 100;
            border-bottom: 1px solid var(--gray-200);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            padding: 15px 0;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo {
            height: 40px;
            margin-right: 15px;
        }
        
        .brand-text h1 {
            font-size: 20px;
            font-weight: 600;
            color: var(--dark);
            margin: 0;
        }
        
        .brand-text p {
            font-size: 12px;
            color: var(--gray-500);
            margin: 0;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
        }
        
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.2s ease;
        }
        
        .primary-button {
            background-color: var(--primary);
            color: var(--white);
        }
        
        .primary-button:hover {
            background-color: var(--primary-dark);
        }
        
        .secondary-button {
            background-color: var(--white);
            color: var(--dark);
            border: 1px solid var(--gray-300);
        }
        
        .secondary-button:hover {
            background-color: var(--gray-100);
        }
        
        .main-content {
            padding: 30px 0;
        }
        
        .floating-toc {
            position: fixed;
            top: 100px;
            left: 20px;
            width: 240px;
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            padding: 20px;
            max-height: calc(100vh - 140px);
            overflow-y: auto;
            z-index: 90;
            transition: all 0.3s ease;
        }
        
        .toc-title {
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .toc-list {
            list-style: none;
        }
        
        .toc-item {
            margin-bottom: 10px;
            line-height: 1.4;
        }
        
        .toc-link {
            color: var(--gray-600);
            text-decoration: none;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 5px 0;
            border-left: 2px solid transparent;
            padding-left: 10px;
            transition: all 0.2s ease;
        }
        
        .toc-link:hover, .toc-link.active {
            color: var(--primary);
            border-left: 2px solid var(--primary);
        }
        
        .toc-link i {
            width: 16px;
        }
        
        .toc-collapse-btn {
            cursor: pointer;
            padding: 5px;
            border-radius: 4px;
            transition: all 0.2s ease;
            color: var(--gray-500);
        }
        
        .toc-collapse-btn:hover {
            background-color: var(--gray-100);
            color: var(--dark);
        }
        
        .toc-collapsed {
            transform: translateX(-200px);
        }
        
        .toc-collapsed .toc-list {
            display: none;
        }
        
        .toc-expand-btn {
            position: fixed;
            top: 100px;
            left: 20px;
            background-color: var(--white);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            cursor: pointer;
            z-index: 89;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .show-expand-btn {
            opacity: 1;
            visibility: visible;
        }
        
        .report-container {
            margin-left: 280px;
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 30px;
            transition: all 0.3s ease;
        }
        
        .full-width {
            margin-left: 0;
        }
        
        .section {
            margin-bottom: 40px;
        }
        
        .section:last-child {
            margin-bottom: 0;
        }
        
        .section-heading {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .section-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--white);
            flex-shrink: 0;
        }
        
        .section-icon.patient-icon {
            background-color: var(--primary);
        }
        
        .section-icon.summary-icon {
            background-color: var(--teal);
        }
        
        .section-icon.vital-icon {
            background-color: var(--red);
        }
        
        .section-icon.subjective-icon {
            background-color: var(--amber);
        }
        
        .section-icon.objective-icon {
            background-color: var(--blue);
        }
        
        .section-icon.assessment-icon {
            background-color: var(--green);
        }
        
        .section-icon.plan-icon {
            background-color: var(--indigo);
        }
        
        .section-icon.progress-icon {
            background-color: var(--purple);
        }
        
        .section-icon.medication-icon {
            background-color: var(--cyan);
        }
        
        .section-icon.recommendation-icon {
            background-color: var(--pink);
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--dark);
            flex-grow: 1;
        }
        
        .section-tools {
            display: flex;
            gap: 10px;
        }
        
        .tool-button {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background-color: var(--gray-100);
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--gray-600);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .tool-button:hover {
            background-color: var(--gray-200);
            color: var(--dark);
        }
        
        .patient-info-card {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            background-color: var(--primary-light);
            border-radius: 10px;
            padding: 20px;
        }
        
        .patient-profile {
            display: flex;
            align-items: center;
        }
        
        .patient-avatar {
            width: 80px;
            height: 80px;
            background-color: var(--primary);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--white);
            font-size: 30px;
            font-weight: bold;
            margin-right: 20px;
            flex-shrink: 0;
        }
        
        .patient-details h3 {
            font-size: 18px;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 5px;
        }
        
        .patient-meta {
            color: var(--gray-600);
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .contact-info {
            display: flex;
            flex-direction: column;
        }
        
        .contact-info .title {
            font-size: 14px;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 5px;
        }
        
        .contact-detail {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 5px;
        }
        
        .contact-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: var(--primary-light);
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--primary);
        }
        
        .medical-info {
            display: flex;
            flex-direction: column;
        }
        
        .medical-info .title {
            font-size: 14px;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 5px;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .info-label {
            color: var(--gray-600);
        }
        
        .info-value {
            font-weight: 500;
            color: var(--dark);
        }
        
        .card-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .info-card {
            background-color: var(--light);
            border-radius: 10px;
            padding: 20px;
            height: 100%;
        }
        
        .info-card-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .info-card-icon {
            width: 28px;
            height: 28px;
            border-radius: 6px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--white);
        }
        
        .info-card-icon.diagnosis {
            background-color: var(--blue);
        }
        
        .info-card-icon.risk {
            background-color: var(--amber);
        }
        
        .info-card-content p {
            color: var(--gray-700);
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .tag-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }
        
        .tag {
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
            background-color: var(--gray-100);
            color: var(--gray-700);
        }
        
        .tag.blue {
            background-color: var(--blue-100);
            color: var(--blue-600);
        }
        
        .tag.red {
            background-color: var(--red-100);
            color: var(--red-600);
        }
        
        .tag.green {
            background-color: var(--green-100);
            color: var(--green-600);
        }
        
        .tag.amber {
            background-color: var(--amber-100);
            color: var(--amber-600);
        }
        
        .tag.indigo {
            background-color: var(--indigo-100);
            color: var(--indigo-600);
        }
        
        .tag.purple {
            background-color: var(--purple-100);
            color: var(--purple-600);
        }
        
        .tag.pink {
            background-color: var(--pink-100);
            color: var(--pink-600);
        }
        
        .tag.teal {
            background-color: var(--teal-100);
            color: var(--teal-600);
        }
        
        .tag.cyan {
            background-color: var(--cyan-100);
            color: var(--cyan-600);
        }
        
        .vital-signs {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }
        
        .vital-card {
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .vital-icon-container {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--white);
            flex-shrink: 0;
        }
        
        .vital-icon-container.anxiety {
            background-color: var(--red);
        }
        
        .vital-icon-container.sleep {
            background-color: var(--blue);
        }
        
        .vital-icon-container.risk {
            background-color: var(--amber);
        }
        
        .vital-info h4 {
            font-size: 14px;
            color: var(--gray-600);
            margin-bottom: 5px;
        }
        
        .vital-value {
            font-size: 24px;
            font-weight: 600;
            color: var(--dark);
        }
        
        .vital-meta {
            font-size: 12px;
            color: var(--gray-500);
        }
        
        .text-content {
            color: var(--gray-700);
            line-height: 1.7;
            margin-bottom: 20px;
        }
        
        .text-content p {
            margin-bottom: 15px;
        }
        
        .text-content strong {
            color: var(--dark);
        }
        
        .text-content:last-child {
            margin-bottom: 0;
        }
        
        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .chart-container {
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 20px;
            height: 100%;
        }
        
        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .chart-title-icon {
            width: 24px;
            height: 24px;
            border-radius: 6px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--white);
        }
        
        .chart-title-icon.anxiety {
            background-color: var(--red);
        }
        
        .chart-title-icon.sleep {
            background-color: var(--blue);
        }
        
        .chart-canvas {
            width: 100%;
            height: 250px;
        }
        
        .checklist {
            list-style-type: none;
            margin-top: 15px;
        }
        
        .checklist-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .checklist-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .checklist-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: var(--green-100);
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--green);
            margin-right: 15px;
            flex-shrink: 0;
        }
        
        .checklist-content {
            flex-grow: 1;
        }
        
        .checklist-content h4 {
            font-size: 16px;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 5px;
        }
        
        .medication-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 15px;
        }
        
        .medication-table th {
            background-color: var(--gray-100);
            padding: 12px 15px;
            text-align: left;
            color: var(--gray-700);
            font-weight: 600;
            font-size: 14px;
        }
        
        .medication-table th:first-child {
            border-top-left-radius: 8px;
        }
        
        .medication-table th:last-child {
            border-top-right-radius: 8px;
        }
        
        .medication-table td {
            padding: 12px 15px;
            border-bottom: 1px solid var(--gray-200);
            color: var(--gray-700);
        }
        
        .medication-table tr:last-child td {
            border-bottom: none;
        }
        
        .medication-table tr:last-child td:first-child {
            border-bottom-left-radius: 8px;
        }
        
        .medication-table tr:last-child td:last-child {
            border-bottom-right-radius: 8px;
        }
        
        .medication-name {
            font-weight: 500;
            color: var(--dark);
        }
        
        .dosage {
            color: var(--gray-700);
            font-size: 14px;
        }
        
        .effectiveness-bar-container {
            width: 120px;
            height: 8px;
            background-color: var(--gray-200);
            border-radius: 4px;
            overflow: hidden;
        }
        
        .effectiveness-bar {
            height: 100%;
            background-color: var(--green);
        }
        
        .effectiveness-value {
            margin-left: 10px;
            color: var(--gray-700);
        }
        
        .timeline {
            position: relative;
            margin: 30px 0 0 10px;
        }
        
        .timeline:before {
            content: '';
            position: absolute;
            top: 0;
            left: 10px;
            width: 2px;
            height: 100%;
            background-color: var(--gray-200);
        }
        
        .timeline-item {
            position: relative;
            padding-left: 40px;
            margin-bottom: 30px;
        }
        
        .timeline-item:last-child {
            margin-bottom: 0;
        }
        
        .timeline-marker {
            position: absolute;
            top: 0;
            left: 0;
            width: 22px;
            height: 22px;
            border-radius: 50%;
            background-color: var(--white);
            border: 2px solid var(--primary);
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--primary);
            font-size: 10px;
        }
        
        .timeline-date {
            font-size: 14px;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 5px;
        }
        
        .timeline-content {
            background-color: var(--white);
            border-radius: 10px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        
        .timeline-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 10px;
        }
        
        .next-appointment {
            background-color: var(--primary-light);
            border-radius: 10px;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
        }
        
        .next-appointment-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .appointment-icon {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            background-color: var(--primary);
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--white);
            flex-shrink: 0;
        }
        
        .appointment-details h4 {
            font-size: 18px;
            font-weight: 600;
            color: var(--dark);
            margin-bottom: 5px;
        }
        
        .appointment-meta {
            color: var(--gray-600);
        }
        
        .editable {
            transition: all 0.2s ease;
            border: 1px solid transparent;
            border-radius: 4px;
            padding: 5px;
        }
        
        .editable:hover {
            background-color: var(--gray-100);
            border: 1px solid var(--gray-300);
            cursor: text;
        }
        
        .editable:focus {
            outline: none;
            background-color: var(--gray-100);
            border: 1px solid var(--primary);
            box-shadow: 0 0 0 2px rgba(15, 111, 255, 0.2);
        }
        
        .signature-section {
            display: flex;
            justify-content: space-between;
            margin-top: 50px;
            padding-top: 30px;
            border-top: 1px solid var(--gray-200);
        }
        
        .signature-box {
            width: 45%;
        }
        
        .signature-line {
            margin-top: 60px;
            border-top: 1px solid var(--gray-400);
            padding-top: 5px;
            font-weight: 500;
            color: var(--gray-700);
        }
        
        .signature-meta {
            color: var(--gray-500);
            font-size: 12px;
            margin-top: 2px;
        }
        
        .report-footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
            color: var(--gray-500);
            font-size: 12px;
        }
        
        .report-footer img {
            height: 24px;
            opacity: 0.5;
        }
        
        .floating-actions {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .floating-button {
            width: 56px;
            height: 56px;
            border-radius: 28px;
            background-color: var(--white);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--gray-700);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .floating-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .floating-button.primary {
            background-color: var(--primary);
            color: var(--white);
        }
        
        @media print {
            .floating-toc,
            .toc-expand-btn,
            .header-actions,
            .section-tools,
            .floating-actions {
                display: none !important;
            }
            
            .report-container {
                margin-left: 0 !important;
                box-shadow: none !important;
                padding: 0 !important;
            }
        }
        
        @media (max-width: 1200px) {
            .vital-signs {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        @media (max-width: 992px) {
            .patient-info-card {
                grid-template-columns: 1fr;
            }
            
            .chart-grid {
                grid-template-columns: 1fr;
            }
            
            .card-grid {
                grid-template-columns: 1fr;
            }
            
            .signature-section {
                flex-direction: column;
                gap: 30px;
            }
            
            .signature-box {
                width: 100%;
            }
        }
        
        @media (max-width: 768px) {
            .floating-toc {
                display: none;
            }
            
            .report-container {
                margin-left: 0;
            }
            
            .vital-signs {
                grid-template-columns: 1fr;
            }
            
            .vital-card {
                padding: 15px;
            }
            
            .vital-icon-container {
                width: 40px;
                height: 40px;
            }
            
            .vital-value {
                font-size: 20px;
            }
            
            .patient-avatar {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }
            
            .medication-table {
                display: block;
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo-container">
                    <img src="../../elpscpv2rd.png" alt="Wellora Logo" class="logo">
                    <div class="brand-text">
                        <h1>Wellora Comprehensive Report</h1>
                        <p>Integrated Patient Care Documentation</p>
                    </div>
                </div>
                <div class="header-actions">
                    <button class="secondary-button" id="edit-toggle">
                        <i class="fas fa-edit"></i>
                        Edit Mode
                    </button>
                    <button class="secondary-button" onclick="window.print()">
                        <i class="fas fa-print"></i>
                        Print
                    </button>
                    <button class="primary-button" id="save-report">
                        <i class="fas fa-save"></i>
                        Save Report
                    </button>
                </div>
            </div>
        </div>
    </header>
    
    <div class="main-content">
        <div class="container">
            <nav class="floating-toc" id="toc">
                <div class="toc-title">
                    Table of Contents
                    <span class="toc-collapse-btn" id="toc-collapse">
                        <i class="fas fa-chevron-left"></i>
                    </span>
                </div>
                <ul class="toc-list">
                    <li class="toc-item">
                        <a href="#patient-information" class="toc-link active">
                            <i class="fas fa-user"></i> Patient Information
                        </a>
                    </li>
                    <li class="toc-item">
                        <a href="#clinical-summary" class="toc-link">
                            <i class="fas fa-clipboard-check"></i> Clinical Summary
                        </a>
                    </li>
                    <li class="toc-item">
                        <a href="#vital-signs" class="toc-link">
                            <i class="fas fa-heartbeat"></i> Vital Signs & Metrics
                        </a>
                    </li>
                    <li class="toc-item">
                        <a href="#subjective" class="toc-link">
                            <i class="fas fa-comment"></i> Subjective Findings
                        </a>
                    </li>
                    <li class="toc-item">
                        <a href="#objective" class="toc-link">
                            <i class="fas fa-eye"></i> Objective Findings
                        </a>
                    </li>
                    <li class="toc-item">
                        <a href="#assessment" class="toc-link">
                            <i class="fas fa-stethoscope"></i> Assessment
                        </a>
                    </li>
                    <li class="toc-item">
                        <a href="#plan" class="toc-link">
                            <i class="fas fa-clipboard-list"></i> Treatment Plan
                        </a>
                    </li>
                    <li class="toc-item">
                        <a href="#progress" class="toc-link">
                            <i class="fas fa-chart-line"></i> Progress Trends
                        </a>
                    </li>
                    <li class="toc-item">
                        <a href="#medications" class="toc-link">
                            <i class="fas fa-pills"></i> Medications
                        </a>
                    </li>
                    <li class="toc-item">
                        <a href="#recommendations" class="toc-link">
                            <i class="fas fa-lightbulb"></i> Recommendations
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="toc-expand-btn" id="toc-expand">
                <i class="fas fa-chevron-right"></i>
            </div>
            
            <div class="report-container" id="report">
                <!-- Patient Information Section -->
                <section id="patient-information" class="section">
                    <div class="section-heading">
                        <div class="section-icon patient-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <h2 class="section-title">Patient Information</h2>
                        <div class="section-tools">
                            <div class="tool-button">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="tool-button">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="patient-info-card">
                        <div class="patient-profile">
                            <div class="patient-avatar">EW</div>
                            <div class="patient-details">
                                <h3 class="editable" contenteditable="true">Emma Wilson</h3>
                                <div class="patient-meta editable" contenteditable="true">34 years old • Female</div>
                                <div class="patient-meta editable" contenteditable="true">Patient ID: PAT-20240538</div>
                            </div>
                        </div>
                        
                        <div class="contact-info">
                            <div class="title">Contact Information</div>
                            <div class="contact-detail">
                                <div class="contact-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <span class="editable" contenteditable="true"><EMAIL></span>
                            </div>
                            <div class="contact-detail">
                                <div class="contact-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <span class="editable" contenteditable="true">(*************</span>
                            </div>
                            <div class="contact-detail">
                                <div class="contact-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <span class="editable" contenteditable="true">123 Main Street, Anytown, CA 94107</span>
                            </div>
                        </div>
                        
                        <div class="medical-info">
                            <div class="title">Medical Information</div>
                            <div class="info-row">
                                <span class="info-label">Primary Care Provider:</span>
                                <span class="info-value editable" contenteditable="true">Dr. James Smith</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Insurance:</span>
                                <span class="info-value editable" contenteditable="true">HealthPlus Insurance</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Policy Number:</span>
                                <span class="info-value editable" contenteditable="true">HP-**********</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Blood Type:</span>
                                <span class="info-value editable" contenteditable="true">B+</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">Allergies:</span>
                                <span class="info-value editable" contenteditable="true">Penicillin, Pollen</span>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Clinical Summary Section -->
                <section id="clinical-summary" class="section">
                    <div class="section-heading">
                        <div class="section-icon summary-icon">
                            <i class="fas fa-clipboard-check"></i>
                        </div>
                        <h2 class="section-title">Clinical Summary</h2>
                        <div class="section-tools">
                            <div class="tool-button">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="tool-button">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-grid">
                        <div class="info-card">
                            <h3 class="info-card-title">
                                <div class="info-card-icon diagnosis">
                                    <i class="fas fa-file-medical"></i>
                                </div>
                                Diagnosis
                            </h3>
                            <div class="info-card-content">
                                <p class="editable" contenteditable="true">Emma continues to meet criteria for Generalized Anxiety Disorder (F41.1) with notable symptoms of excessive worry, sleep disturbance, and difficulty controlling anxious thoughts. Comorbid Insomnia Disorder (G47.00) characterized by sleep onset difficulties.</p>
                                <div class="tag-list">
                                    <div class="tag blue">Generalized Anxiety Disorder (F41.1)</div>
                                    <div class="tag indigo">Insomnia Disorder (G47.00)</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="info-card">
                            <h3 class="info-card-title">
                                <div class="info-card-icon risk">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                Risk Assessment
                            </h3>
                            <div class="info-card-content">
                                <p class="editable" contenteditable="true">Patient is currently assessed at a medium risk level due to moderate anxiety symptoms and sleep disturbances. Work environment remains a significant trigger for anxiety symptoms, with particular stress around performance expectations and social evaluation. No suicidal ideation present.</p>
                                <div class="tag-list">
                                    <div class="tag amber">Medium Risk</div>
                                    <div class="tag purple">Work Stressors</div>
                                    <div class="tag teal">Perfectionism</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Vital Signs Section -->
                <section id="vital-signs" class="section">
                    <div class="section-heading">
                        <div class="section-icon vital-icon">
                            <i class="fas fa-heartbeat"></i>
                        </div>
                        <h2 class="section-title">Vital Signs & Metrics</h2>
                        <div class="section-tools">
                            <div class="tool-button">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="tool-button">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="vital-signs">
                        <div class="vital-card">
                            <div class="vital-icon-container anxiety">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="vital-info">
                                <h4>Anxiety Score</h4>
                                <div class="vital-value editable" contenteditable="true">12/21</div>
                                <div class="vital-meta">GAD-7 Assessment</div>
                            </div>
                        </div>
                        <div class="vital-card">
                            <div class="vital-icon-container sleep">
                                <i class="fas fa-moon"></i>
                            </div>
                            <div class="vital-info">
                                <h4>Sleep Efficiency</h4>
                                <div class="vital-value editable" contenteditable="true">72%</div>
                                <div class="vital-meta">Sleep Quality Index</div>
                            </div>
                        </div>
                        <div class="vital-card">
                            <div class="vital-icon-container risk">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="vital-info">
                                <h4>Risk Level</h4>
                                <div class="vital-value editable" contenteditable="true">Medium</div>
                                <div class="vital-meta">Clinical Assessment</div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Subjective Section -->
                <section id="subjective" class="section">
                    <div class="section-heading">
                        <div class="section-icon subjective-icon">
                            <i class="fas fa-comment"></i>
                        </div>
                        <h2 class="section-title">Subjective Findings</h2>
                        <div class="section-tools">
                            <div class="tool-button">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="tool-button">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-content editable" contenteditable="true">
                        <p>Patient reports continued anxiety symptoms, but notes a slight improvement this week. She describes feeling anxious predominantly during work hours (9am-5pm) with peak anxiety occurring during team meetings and deadline discussions. Sleep has slightly improved with implementation of the evening mindfulness routine, though she still requires approximately 45 minutes to fall asleep (improved from 65 minutes).</p>
                        
                        <p>Patient reports completing the mindfulness exercise 4 out of 7 recommended days. She states: <strong>"I still have racing thoughts at bedtime, but they're somewhat less intense when I do the breathing exercises. Work is still triggering my anxiety, especially when my boss schedules last-minute meetings."</strong></p>
                        
                        <p>Patient describes continued difficulty with perfectionist tendencies at work, often spending extra hours to ensure tasks are completed to high standards. This pattern interferes with work-life balance and contributes to evening anxiety. Patient expresses motivation to implement workplace boundaries but concerns about how those changes might be perceived by colleagues and supervisors.</p>
                    </div>
                    
                    <div class="tag-list">
                        <div class="tag amber">Anxiety</div>
                        <div class="tag blue">Sleep Disturbance</div>
                        <div class="tag purple">Work Stress</div>
                        <div class="tag red">Racing Thoughts</div>
                        <div class="tag teal">Perfectionism</div>
                    </div>
                </section>
                
                <!-- Objective Section -->
                <section id="objective" class="section">
                    <div class="section-heading">
                        <div class="section-icon objective-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h2 class="section-title">Objective Findings</h2>
                        <div class="section-tools">
                            <div class="tool-button">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="tool-button">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-content editable" contenteditable="true">
                        <p>Patient presented with appropriate appearance and hygiene. Speech was at normal rate and volume. Affect was anxious but improved from previous session. Maintained good eye contact throughout the session.</p>
                        
                        <p>GAD-7 score of 12/21 (reduction from 14/21 at previous session). Sleep diary indicates average of 6.2 hours of sleep per night (previously 5.8) with sleep efficiency of 72%. Mindfulness exercise compliance rate of 57% (4/7 days).</p>
                        
                        <p>Linguistic analysis of session recording showed decreased hesitation when discussing coping strategies (15% fewer pauses than previous session). Nonverbal observations include reduced psychomotor agitation, though still fidgeting with hands when discussing work stressors.</p>
                    </div>
                    
                    <div class="chart-grid">
                        <div class="chart-container">
                            <h3 class="chart-title">
                                <div class="chart-title-icon anxiety">
                                    <i class="fas fa-brain"></i>
                                </div>
                                Anxiety Trend (GAD-7 Scores)
                            </h3>
                            <canvas id="anxietyChart" class="chart-canvas"></canvas>
                        </div>
                        <div class="chart-container">
                            <h3 class="chart-title">
                                <div class="chart-title-icon sleep">
                                    <i class="fas fa-moon"></i>
                                </div>
                                Sleep Efficiency Trend
                            </h3>
                            <canvas id="sleepChart" class="chart-canvas"></canvas>
                        </div>
                    </div>
                </section>
                
                <!-- Assessment Section -->
                <section id="assessment" class="section">
                    <div class="section-heading">
                        <div class="section-icon assessment-icon">
                            <i class="fas fa-stethoscope"></i>
                        </div>
                        <h2 class="section-title">Assessment</h2>
                        <div class="section-tools">
                            <div class="tool-button">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="tool-button">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-content editable" contenteditable="true">
                        <p>Emma continues to meet criteria for Generalized Anxiety Disorder (F41.1) with notable symptoms of excessive worry, sleep disturbance, and difficulty controlling anxious thoughts. Comorbid Insomnia Disorder (G47.00) characterized by sleep onset difficulties. Work environment remains a significant trigger for anxiety symptoms, with particular stress around performance expectations and social evaluation.</p>
                        
                        <p>Patient is showing positive response to treatment as evidenced by modest improvements in GAD-7 scores, sleep efficiency, and reduced hesitation when discussing coping mechanisms. Partial compliance with recommended mindfulness practice has likely contributed to the observed improvements. Perfectionist thought patterns continue to exacerbate anxiety symptoms, particularly in workplace contexts.</p>
                        
                        <p>Current risk level is assessed as moderate with no suicidal ideation or intent present. Patient demonstrates good insight into her condition and positive engagement with treatment recommendations despite challenges with consistent implementation. Prognosis is favorable with continued therapy and improved compliance with recommendations.</p>
                    </div>
                </section>
                
                <!-- Treatment Plan Section -->
                <section id="plan" class="section">
                    <div class="section-heading">
                        <div class="section-icon plan-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <h2 class="section-title">Treatment Plan</h2>
                        <div class="section-tools">
                            <div class="tool-button">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="tool-button">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-content editable" contenteditable="true">
                        <p>Continue weekly CBT sessions with increased focus on perfectionist thought patterns and workplace boundary setting. Maintain current medication regimen (Sertraline 50mg daily) with evaluation of effectiveness at next medication review appointment. Consider adjustment of Melatonin dosage (currently 3mg) based on continued sleep monitoring.</p>
                    </div>
                    
                    <ul class="checklist">
                        <li class="checklist-item">
                            <div class="checklist-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="checklist-content">
                                <h4>Mindfulness Practice</h4>
                                <p class="editable" contenteditable="true">Continue guided mindfulness practice with emphasis on consistent daily implementation (increase from 4/7 days to 6/7 days). Utilize Calm app's "Sleep Stories" program to specifically target sleep onset difficulties.</p>
                            </div>
                        </li>
                        <li class="checklist-item">
                            <div class="checklist-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="checklist-content">
                                <h4>Workplace Boundaries</h4>
                                <p class="editable" contenteditable="true">Implement workplace boundaries exercise: structured protocol for limiting after-hours email checking and work activity. Begin with 30-minute work-free buffer before bedtime, gradually extending to 90 minutes over three weeks.</p>
                            </div>
                        </li>
                        <li class="checklist-item">
                            <div class="checklist-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="checklist-content">
                                <h4>Cognitive Restructuring</h4>
                                <p class="editable" contenteditable="true">Cognitive restructuring homework focused on perfectionist thought patterns and catastrophizing about work performance, with daily thought record focusing on alternative perspectives.</p>
                            </div>
                        </li>
                        <li class="checklist-item">
                            <div class="checklist-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="checklist-content">
                                <h4>Sleep Monitoring</h4>
                                <p class="editable" contenteditable="true">Continue sleep diary for next two weeks. Schedule next appointment for May 15, 2024, 10:00 AM.</p>
                            </div>
                        </li>
                    </ul>
                </section>
                
                <!-- Progress Trends Section -->
                <section id="progress" class="section">
                    <div class="section-heading">
                        <div class="section-icon progress-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h2 class="section-title">Progress Trends</h2>
                        <div class="section-tools">
                            <div class="tool-button">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="tool-button">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-content editable" contenteditable="true">
                        <p>Patient is showing modest but consistent improvement across multiple domains. Anxiety symptoms as measured by GAD-7 have decreased from initial score of 18/21 to current score of 12/21 over 12 sessions, representing a 33% improvement overall. The most significant improvements have been observed in reduction of physical symptoms of anxiety and improved ability to control worry in specific situations.</p>
                        
                        <p>Sleep quality metrics have improved with sleep efficiency increasing from 59% to 72% and sleep onset latency decreasing from 90 minutes to 45 minutes. Progress with workplace stressors has been slower, though patient reports increased awareness of cognitive distortions related to perfectionism and has begun implementing small boundaries around work hours.</p>
                    </div>
                    
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-date">May 1, 2024 - Session #12</div>
                            <div class="timeline-content">
                                <h3 class="timeline-title">Anxiety management techniques and sleep hygiene</h3>
                                <p class="editable" contenteditable="true">Patient reported 15% improvement in anxiety symptoms with consistent application of breathing techniques. Sleep onset delay reduced to 45 minutes (from 65). Work stressors remain significant but emotional response has moderated.</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-date">April 17, 2024 - Session #11</div>
                            <div class="timeline-content">
                                <h3 class="timeline-title">Work stress and cognitive distortions</h3>
                                <p class="editable" contenteditable="true">Identified significant perfectionist thought patterns affecting work performance and sleep quality. Introduced thought recording exercise and began challenging catastrophic thinking about work outcomes.</p>
                            </div>
                        </div>
                        
                        <div class="timeline-item">
                            <div class="timeline-marker">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-date">April 3, 2024 - Session #10</div>
                            <div class="timeline-content">
                                <h3 class="timeline-title">Sleep assessment and initial strategies</h3>
                                <p class="editable" contenteditable="true">Comprehensive sleep assessment completed. Identified racing thoughts at bedtime as primary impediment to sleep onset. Began sleep diary and introduced basic relaxation techniques.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="next-appointment">
                        <div class="next-appointment-info">
                            <div class="appointment-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="appointment-details">
                                <h4>Next Appointment</h4>
                                <div class="appointment-meta editable" contenteditable="true">May 15, 2024 • 10:00 AM - 11:00 AM • CBT Session</div>
                            </div>
                        </div>
                        <button class="secondary-button">
                            <i class="fas fa-edit"></i>
                            Reschedule
                        </button>
                    </div>
                </section>
                
                <!-- Medications Section -->
                <section id="medications" class="section">
                    <div class="section-heading">
                        <div class="section-icon medication-icon">
                            <i class="fas fa-pills"></i>
                        </div>
                        <h2 class="section-title">Medications</h2>
                        <div class="section-tools">
                            <div class="tool-button">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="tool-button">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-content editable" contenteditable="true">
                        <p>Current medication regimen shows positive response. Consider possible dosage adjustment for Melatonin based on continued sleep quality monitoring. No adverse effects reported with current medications.</p>
                    </div>
                    
                    <table class="medication-table">
                        <thead>
                            <tr>
                                <th>Medication</th>
                                <th>Dosage</th>
                                <th>Purpose</th>
                                <th>Effectiveness</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="medication-name editable" contenteditable="true">Sertraline</div>
                                    <div class="dosage editable" contenteditable="true">50mg daily</div>
                                </td>
                                <td class="editable" contenteditable="true">SSRI for anxiety management</td>
                                <td>
                                    <div style="display: flex; align-items: center;">
                                        <div class="effectiveness-bar-container">
                                            <div class="effectiveness-bar" style="width: 85%;"></div>
                                        </div>
                                        <span class="effectiveness-value editable" contenteditable="true">85%</span>
                                    </div>
                                </td>
                                <td class="editable" contenteditable="true">Continue current dosage</td>
                            </tr>
                            <tr>
                                <td>
                                    <div class="medication-name editable" contenteditable="true">Melatonin</div>
                                    <div class="dosage editable" contenteditable="true">3mg at bedtime</div>
                                </td>
                                <td class="editable" contenteditable="true">Sleep aid</td>
                                <td>
                                    <div style="display: flex; align-items: center;">
                                        <div class="effectiveness-bar-container">
                                            <div class="effectiveness-bar" style="width: 62%;"></div>
                                        </div>
                                        <span class="effectiveness-value editable" contenteditable="true">62%</span>
                                    </div>
                                </td>
                                <td class="editable" contenteditable="true">Consider dosage adjustment</td>
                            </tr>
                        </tbody>
                    </table>
                </section>
                
                <!-- Recommendations Section -->
                <section id="recommendations" class="section">
                    <div class="section-heading">
                        <div class="section-icon recommendation-icon">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <h2 class="section-title">Recommendations</h2>
                        <div class="section-tools">
                            <div class="tool-button">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="tool-button">
                                <i class="fas fa-ellipsis-h"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-content editable" contenteditable="true">
                        <p>Based on comprehensive assessment of current symptoms, treatment progress, and evidence-based practices, the following recommendations are provided:</p>
                        
                        <p>1. <strong>Continue weekly CBT sessions</strong> with focus on perfectionism and workplace boundaries. Research indicates CBT is highly effective for GAD with response rates of 60-70% when consistently applied.</p>
                        
                        <p>2. <strong>Maintain current Sertraline dosage</strong> (50mg daily) with evaluation at 12-week mark. SSRI efficacy typically plateaus at 12 weeks, with maximum therapeutic benefit often requiring 12-16 weeks of consistent use.</p>
                        
                        <p>3. <strong>Consider Melatonin dosage increase</strong> to 5mg if sleep onset latency remains >30 minutes after 2 weeks of consistent mindfulness practice. Recent meta-analyses suggest optimal dosage for sleep onset insomnia may be 5mg rather than 3mg for some patients.</p>
                        
                        <p>4. <strong>Increase mindfulness practice compliance</strong> to at least 6 days per week. Studies indicate dose-response relationship between mindfulness practice frequency and anxiety reduction, with significant improvements at ≥6 days/week.</p>
                        
                        <p>5. <strong>Implement gradual workplace boundary setting</strong> with structured plan and cognitive restructuring around perfectionism. Workplace stress reduction techniques show 28-40% efficacy in reducing anxiety symptoms when implemented consistently.</p>
                    </div>
                </section>
                
                <!-- Signature Section -->
                <div class="signature-section">
                    <div class="signature-box">
                        <div class="signature-line editable" contenteditable="true">Dr. Sarah Matthews, MD</div>
                        <div class="signature-meta editable" contenteditable="true">Licensed Psychiatrist • NPI: **********</div>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line editable" contenteditable="true">Generated: May 12, 2024</div>
                        <div class="signature-meta editable" contenteditable="true">Report ID: COMP-EW-202405121132 • Wellora Health Platform</div>
                    </div>
                </div>
                
                <!-- Footer -->
                <div class="report-footer">
                    <div>
                        <p>© 2024 Wellora Health Technologies, Inc. All Rights Reserved.</p>
                        <p>This report is generated using Wellora's AI-assisted documentation platform. All information should be reviewed by a qualified healthcare provider.</p>
                    </div>
                    <img src="../../elpscpv2rd.png" alt="Wellora Logo">
                </div>
            </div>
        </div>
    </div>
    
    <div class="floating-actions">
        <div class="floating-button" onclick="window.print()">
            <i class="fas fa-print"></i>
        </div>
        <div class="floating-button primary" id="save-floating">
            <i class="fas fa-save"></i>
        </div>
    </div>
    
    <script>
        // Initialize charts
        document.addEventListener('DOMContentLoaded', function() {
            // Anxiety Chart
            const anxietyCtx = document.getElementById('anxietyChart').getContext('2d');
            const anxietyChart = new Chart(anxietyCtx, {
                type: 'line',
                data: {
                    labels: ['Initial', 'Session 3', 'Session 6', 'Session 9', 'Session 12'],
                    datasets: [{
                        label: 'GAD-7 Score',
                        data: [18, 17, 15, 14, 12],
                        backgroundColor: 'rgba(239, 68, 68, 0.2)',
                        borderColor: 'rgba(239, 68, 68, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(239, 68, 68, 1)',
                        pointRadius: 4,
                        tension: 0.3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 0,
                            max: 21,
                            title: {
                                display: true,
                                text: 'GAD-7 Score'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Treatment Timeline'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `GAD-7 Score: ${context.raw}/21`;
                                }
                            }
                        }
                    }
                }
            });
            
            // Sleep Efficiency Chart
            const sleepCtx = document.getElementById('sleepChart').getContext('2d');
            const sleepChart = new Chart(sleepCtx, {
                type: 'line',
                data: {
                    labels: ['Initial', 'Session 3', 'Session 6', 'Session 9', 'Session 12'],
                    datasets: [{
                        label: 'Sleep Efficiency',
                        data: [59, 62, 65, 68, 72],
                        backgroundColor: 'rgba(59, 130, 246, 0.2)',
                        borderColor: 'rgba(59, 130, 246, 1)',
                        borderWidth: 2,
                        pointBackgroundColor: 'rgba(59, 130, 246, 1)',
                        pointRadius: 4,
                        tension: 0.3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            min: 50,
                            max: 100,
                            title: {
                                display: true,
                                text: 'Sleep Efficiency (%)'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Treatment Timeline'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `Sleep Efficiency: ${context.raw}%`;
                                }
                            }
                        }
                    }
                }
            });
            
            // TOC Functionality
            const toc = document.getElementById('toc');
            const tocCollapseBtn = document.getElementById('toc-collapse');
            const tocExpandBtn = document.getElementById('toc-expand');
            const reportContainer = document.getElementById('report');
            
            tocCollapseBtn.addEventListener('click', function() {
                toc.classList.add('toc-collapsed');
                tocExpandBtn.classList.add('show-expand-btn');
                reportContainer.classList.add('full-width');
            });
            
            tocExpandBtn.addEventListener('click', function() {
                toc.classList.remove('toc-collapsed');
                tocExpandBtn.classList.remove('show-expand-btn');
                reportContainer.classList.remove('full-width');
            });
            
            // Active TOC link
            const tocLinks = document.querySelectorAll('.toc-link');
            const sections = document.querySelectorAll('.section');
            
            window.addEventListener('scroll', function() {
                let current = '';
                
                sections.forEach(section => {
                    const sectionTop = section.offsetTop - 100;
                    const sectionHeight = section.clientHeight;
                    if(pageYOffset >= sectionTop) {
                        current = section.getAttribute('id');
                    }
                });
                
                tocLinks.forEach(link => {
                    link.classList.remove('active');
                    if(link.getAttribute('href').substring(1) === current) {
                        link.classList.add('active');
                    }
                });
            });
            
            // Edit Mode Toggle
            const editToggleBtn = document.getElementById('edit-toggle');
            const editableElements = document.querySelectorAll('.editable');
            
            editToggleBtn.addEventListener('click', function() {
                const isEditMode = editToggleBtn.classList.contains('primary-button');
                
                if(isEditMode) {
                    // Turning off edit mode
                    editableElements.forEach(el => {
                        el.setAttribute('contenteditable', 'false');
                        el.classList.remove('editable-active');
                    });
                    editToggleBtn.classList.remove('primary-button');
                    editToggleBtn.classList.add('secondary-button');
                    editToggleBtn.innerHTML = '<i class="fas fa-edit"></i> Edit Mode';
                } else {
                    // Turning on edit mode
                    editableElements.forEach(el => {
                        el.setAttribute('contenteditable', 'true');
                        el.classList.add('editable-active');
                    });
                    editToggleBtn.classList.remove('secondary-button');
                    editToggleBtn.classList.add('primary-button');
                    editToggleBtn.innerHTML = '<i class="fas fa-check"></i> Finish Editing';
                }
            });
            
            // Save Report
            const saveReportBtn = document.getElementById('save-report');
            const saveFloatingBtn = document.getElementById('save-floating');
            
            function saveReport() {
                // Simulate saving with a notification
                const notification = document.createElement('div');
                notification.style.position = 'fixed';
                notification.style.bottom = '20px';
                notification.style.left = '50%';
                notification.style.transform = 'translateX(-50%)';
                notification.style.backgroundColor = '#10B981';
                notification.style.color = 'white';
                notification.style.padding = '10px 20px';
                notification.style.borderRadius = '4px';
                notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
                notification.style.zIndex = '1000';
                notification.textContent = 'Report saved successfully!';
                
                document.body.appendChild(notification);
                
                setTimeout(() => {
                    notification.style.opacity = '0';
                    notification.style.transition = 'opacity 0.5s ease';
                    
                    setTimeout(() => {
                        document.body.removeChild(notification);
                    }, 500);
                }, 3000);
            }
            
            saveReportBtn.addEventListener('click', saveReport);
            saveFloatingBtn.addEventListener('click', saveReport);
        });
    </script>
</body>
</html>