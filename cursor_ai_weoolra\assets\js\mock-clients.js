// Mock data for therapist dashboard
const mockClients = [
    {
        id: 1,
        firstName: "<PERSON>",
        lastName: "<PERSON>",
        email: "emma<PERSON><EMAIL>",
        phone: "+31 6 12345678",
        dateOfBirth: "1985-04-12",
        gender: "Female",
        address: "Keizersgracht 123, 1015 CW Amsterdam",
        profileImage: "https://cdn.pixabay.com/photo/2018/01/15/07/51/woman-3083383_1280.jpg",
        therapyType: "Cognitive Behavioral Therapy",
        nextAppointment: "2023-06-15T14:30:00",
        status: "Active",
        notes: "Emma has been making good progress with her anxiety management techniques.",
        documents: [
            { id: 1, name: "Initial Assessment", date: "2023-01-15", uploadDate: "2023-01-15", lastEdited: "2023-01-15", type: "pdf", size: "1.2 MB", preview: "https://cdn.pixabay.com/photo/2017/06/10/07/21/chat-2389223_1280.png" },
            { id: 2, name: "Treatment Plan", date: "2023-02-01", uploadDate: "2023-02-01", lastEdited: "2023-03-05", type: "docx", size: "845 KB", preview: "https://cdn.pixabay.com/photo/2017/06/10/07/22/service-2389225_1280.png" },
            { id: 3, name: "Progress Notes - Q1", date: "2023-04-10", uploadDate: "2023-04-10", lastEdited: "2023-04-15", type: "pdf", size: "980 KB", preview: "https://cdn.pixabay.com/photo/2017/06/10/07/21/chat-2389223_1280.png" }
        ],
        recordings: [
            { id: 1, name: "Session #12", date: "2023-05-22", duration: "48:22", size: "24.5 MB" },
            { id: 2, name: "Session #11", date: "2023-05-08", duration: "52:16", size: "26.8 MB" },
            { id: 3, name: "Session #10", date: "2023-04-24", duration: "45:03", size: "22.1 MB" }
        ],
        reports: [
            { id: 1, name: "Quarterly Review", date: "2023-04-15", type: "pdf", size: "1.5 MB" },
            { id: 2, name: "Anxiety Assessment", date: "2023-03-10", type: "pdf", size: "2.1 MB" }
        ],
        aiInsights: [
            { id: 1, title: "Speech Pattern Analysis", date: "2023-05-22", confidence: 87 },
            { id: 2, title: "Emotional Tone Assessment", date: "2023-05-22", confidence: 92 },
            { id: 3, title: "Treatment Effectiveness", date: "2023-05-08", confidence: 76 }
        ]
    },
    {
        id: 2,
        firstName: "Michael",
        lastName: "Rodriguez",
        email: "<EMAIL>",
        phone: "+31 6 23456789",
        dateOfBirth: "1992-11-28",
        gender: "Male",
        address: "Overtoom 45, 1054 HK Amsterdam",
        profileImage: "https://cdn.pixabay.com/photo/2016/11/21/12/42/beard-1845166_1280.jpg",
        therapyType: "Dialectical Behavior Therapy",
        nextAppointment: "2023-06-14T10:00:00",
        status: "Active",
        notes: "Michael is working on mindfulness practices and emotional regulation skills.",
        documents: [
            { id: 1, name: "Initial Assessment", date: "2023-02-20", type: "pdf", size: "1.3 MB" },
            { id: 2, name: "Treatment Plan", date: "2023-03-05", type: "docx", size: "788 KB" }
        ],
        recordings: [
            { id: 1, name: "Session #8", date: "2023-05-24", duration: "56:14", size: "28.7 MB" },
            { id: 2, name: "Session #7", date: "2023-05-10", duration: "49:52", size: "25.3 MB" }
        ],
        reports: [
            { id: 1, name: "DBT Progress Report", date: "2023-05-12", type: "pdf", size: "1.2 MB" }
        ],
        aiInsights: [
            { id: 1, title: "Speech Pattern Analysis", date: "2023-05-24", confidence: 81 },
            { id: 2, title: "Emotional Tone Assessment", date: "2023-05-24", confidence: 85 }
        ]
    },
    {
        id: 3,
        firstName: "Sarah",
        lastName: "Johnson",
        email: "<EMAIL>",
        phone: "+31 6 34567890",
        dateOfBirth: "1988-07-15",
        gender: "Female",
        address: "Herengracht 78, 1015 BD Amsterdam",
        profileImage: "https://cdn.pixabay.com/photo/2018/03/06/22/57/portrait-3204843_1280.jpg",
        therapyType: "Psychodynamic Therapy",
        nextAppointment: "2023-06-16T16:00:00",
        status: "Active",
        notes: "Sarah is exploring childhood patterns and their influence on current relationships.",
        documents: [
            { id: 1, name: "Initial Assessment", date: "2022-11-08", type: "pdf", size: "1.4 MB" },
            { id: 2, name: "Treatment Plan", date: "2022-11-22", type: "docx", size: "910 KB" },
            { id: 3, name: "Progress Notes - Q1", date: "2023-02-15", type: "pdf", size: "1.1 MB" },
            { id: 4, name: "Progress Notes - Q2", date: "2023-05-18", type: "pdf", size: "1.3 MB" }
        ],
        recordings: [
            { id: 1, name: "Session #24", date: "2023-05-26", duration: "58:33", size: "29.8 MB" },
            { id: 2, name: "Session #23", date: "2023-05-19", duration: "61:05", size: "31.2 MB" },
            { id: 3, name: "Session #22", date: "2023-05-12", duration: "54:21", size: "27.7 MB" }
        ],
        reports: [
            { id: 1, name: "Biannual Assessment", date: "2023-05-20", type: "pdf", size: "2.4 MB" },
            { id: 2, name: "Relationship Pattern Analysis", date: "2023-04-05", type: "pdf", size: "1.8 MB" }
        ],
        aiInsights: [
            { id: 1, title: "Speech Pattern Analysis", date: "2023-05-26", confidence: 93 },
            { id: 2, title: "Emotional Tone Assessment", date: "2023-05-26", confidence: 88 },
            { id: 3, title: "Narrative Consistency", date: "2023-05-19", confidence: 78 },
            { id: 4, title: "Treatment Effectiveness", date: "2023-05-20", confidence: 84 }
        ]
    },
    {
        id: 4,
        firstName: "David",
        lastName: "Wilson",
        email: "<EMAIL>",
        phone: "+31 6 45678901",
        dateOfBirth: "1975-09-02",
        gender: "Male",
        address: "Prinsengracht 456, 1016 KH Amsterdam",
        profileImage: "https://cdn.pixabay.com/photo/2016/11/18/19/07/happy-1836445_1280.jpg",
        therapyType: "Cognitive Processing Therapy",
        nextAppointment: "2023-06-13T09:00:00",
        status: "Active",
        notes: "David is processing past trauma and developing coping mechanisms for PTSD symptoms.",
        documents: [
            { id: 1, name: "Initial Assessment", date: "2023-01-05", type: "pdf", size: "1.5 MB" },
            { id: 2, name: "Treatment Plan", date: "2023-01-18", type: "docx", size: "875 KB" },
            { id: 3, name: "Progress Notes - Q1", date: "2023-04-02", type: "pdf", size: "1.2 MB" }
        ],
        recordings: [
            { id: 1, name: "Session #20", date: "2023-05-23", duration: "62:44", size: "32.1 MB" },
            { id: 2, name: "Session #19", date: "2023-05-16", duration: "58:17", size: "29.6 MB" }
        ],
        reports: [
            { id: 1, name: "PTSD Symptom Tracking", date: "2023-05-10", type: "pdf", size: "1.9 MB" },
            { id: 2, name: "Sleep Pattern Analysis", date: "2023-04-12", type: "pdf", size: "1.1 MB" }
        ],
        aiInsights: [
            { id: 1, title: "Speech Pattern Analysis", date: "2023-05-23", confidence: 79 },
            { id: 2, title: "Emotional Tone Assessment", date: "2023-05-23", confidence: 83 },
            { id: 3, title: "Treatment Effectiveness", date: "2023-05-10", confidence: 72 }
        ]
    },
    {
        id: 5,
        firstName: "Lisa",
        lastName: "Chen",
        email: "<EMAIL>",
        phone: "+31 6 56789012",
        dateOfBirth: "1990-03-25",
        gender: "Female",
        address: "Westerstraat 87, 1015 LW Amsterdam",
        profileImage: "https://cdn.pixabay.com/photo/2017/04/06/19/34/girl-2209147_1280.jpg",
        therapyType: "Acceptance and Commitment Therapy",
        nextAppointment: "2023-06-12T15:30:00",
        status: "Active",
        notes: "Lisa is working on acceptance strategies and values-based goal setting.",
        documents: [
            { id: 1, name: "Initial Assessment", date: "2022-12-10", type: "pdf", size: "1.3 MB" },
            { id: 2, name: "Treatment Plan", date: "2022-12-22", type: "docx", size: "830 KB" },
            { id: 3, name: "Progress Notes - Q1", date: "2023-03-15", type: "pdf", size: "1.0 MB" }
        ],
        recordings: [
            { id: 1, name: "Session #15", date: "2023-05-22", duration: "53:12", size: "27.1 MB" },
            { id: 2, name: "Session #14", date: "2023-05-15", duration: "55:48", size: "28.4 MB" },
            { id: 3, name: "Session #13", date: "2023-05-08", duration: "51:33", size: "26.2 MB" }
        ],
        reports: [
            { id: 1, name: "Values Assessment", date: "2023-04-28", type: "pdf", size: "1.2 MB" },
            { id: 2, name: "Psychological Flexibility Measure", date: "2023-03-05", type: "pdf", size: "980 KB" }
        ],
        aiInsights: [
            { id: 1, title: "Speech Pattern Analysis", date: "2023-05-22", confidence: 91 },
            { id: 2, title: "Emotional Tone Assessment", date: "2023-05-22", confidence: 87 },
            { id: 3, title: "Values Consistency", date: "2023-04-28", confidence: 85 }
        ]
    },
    {
        id: 6,
        firstName: "James",
        lastName: "Taylor",
        email: "<EMAIL>",
        phone: "+31 6 67890123",
        dateOfBirth: "1982-12-08",
        gender: "Male",
        address: "Van Woustraat 120, 1073 LT Amsterdam",
        profileImage: "https://cdn.pixabay.com/photo/2018/04/27/03/50/portrait-3353699_1280.jpg",
        therapyType: "Solution-Focused Brief Therapy",
        nextAppointment: "2023-06-19T11:00:00",
        status: "Active",
        notes: "James is focusing on specific, achievable goals to address work-related stress.",
        documents: [
            { id: 1, name: "Initial Assessment", date: "2023-03-18", type: "pdf", size: "1.2 MB" },
            { id: 2, name: "Treatment Plan", date: "2023-03-25", type: "docx", size: "795 KB" }
        ],
        recordings: [
            { id: 1, name: "Session #6", date: "2023-05-29", duration: "45:21", size: "23.0 MB" },
            { id: 2, name: "Session #5", date: "2023-05-22", duration: "47:38", size: "24.2 MB" }
        ],
        reports: [
            { id: 1, name: "Goal Progress Report", date: "2023-05-25", type: "pdf", size: "1.0 MB" }
        ],
        aiInsights: [
            { id: 1, title: "Speech Pattern Analysis", date: "2023-05-29", confidence: 82 },
            { id: 2, title: "Emotional Tone Assessment", date: "2023-05-29", confidence: 88 },
            { id: 3, title: "Goal Alignment", date: "2023-05-25", confidence: 91 }
        ]
    },
    {
        id: 7,
        firstName: "Anna",
        lastName: "Müller",
        email: "<EMAIL>",
        phone: "+31 6 78901234",
        dateOfBirth: "1979-05-30",
        gender: "Female",
        address: "Jordaan 34, 1016 GV Amsterdam",
        profileImage: "https://cdn.pixabay.com/photo/2016/11/29/13/14/attractive-1869761_1280.jpg",
        therapyType: "Interpersonal Therapy",
        nextAppointment: "2023-06-15T13:00:00",
        status: "Active",
        notes: "Anna is addressing relationship patterns and improving communication skills.",
        documents: [
            { id: 1, name: "Initial Assessment", date: "2022-10-12", type: "pdf", size: "1.4 MB" },
            { id: 2, name: "Treatment Plan", date: "2022-10-25", type: "docx", size: "865 KB" },
            { id: 3, name: "Progress Notes - Q1", date: "2023-01-15", type: "pdf", size: "1.1 MB" },
            { id: 4, name: "Progress Notes - Q2", date: "2023-04-20", type: "pdf", size: "1.2 MB" }
        ],
        recordings: [
            { id: 1, name: "Session #32", date: "2023-05-25", duration: "59:48", size: "30.5 MB" },
            { id: 2, name: "Session #31", date: "2023-05-18", duration: "57:22", size: "29.2 MB" },
            { id: 3, name: "Session #30", date: "2023-05-11", duration: "60:15", size: "30.7 MB" }
        ],
        reports: [
            { id: 1, name: "Interpersonal Relationship Assessment", date: "2023-05-02", type: "pdf", size: "1.7 MB" },
            { id: 2, name: "Communication Pattern Analysis", date: "2023-03-15", type: "pdf", size: "1.5 MB" }
        ],
        aiInsights: [
            { id: 1, title: "Speech Pattern Analysis", date: "2023-05-25", confidence: 89 },
            { id: 2, title: "Emotional Tone Assessment", date: "2023-05-25", confidence: 92 },
            { id: 3, title: "Interpersonal Dynamics", date: "2023-05-02", confidence: 87 },
            { id: 4, title: "Treatment Effectiveness", date: "2023-04-20", confidence: 83 }
        ]
    },
    {
        id: 8,
        firstName: "Oliver",
        lastName: "Smith",
        email: "<EMAIL>",
        phone: "+31 6 89012345",
        dateOfBirth: "1995-08-17",
        gender: "Male",
        address: "Leidseplein 12, 1017 PT Amsterdam",
        profileImage: "https://cdn.pixabay.com/photo/2015/07/20/12/57/man-852766_1280.jpg",
        therapyType: "Gestalt Therapy",
        nextAppointment: "2023-06-14T09:00:00",
        status: "Active",
        notes: "Oliver is developing greater self-awareness and improving present-moment focus.",
        documents: [
            { id: 1, name: "Initial Assessment", date: "2023-02-03", type: "pdf", size: "1.3 MB" },
            { id: 2, name: "Treatment Plan", date: "2023-02-15", type: "docx", size: "810 KB" },
            { id: 3, name: "Progress Notes - Q1", date: "2023-05-05", type: "pdf", size: "990 KB" }
        ],
        recordings: [
            { id: 1, name: "Session #10", date: "2023-05-24", duration: "52:33", size: "26.8 MB" },
            { id: 2, name: "Session #9", date: "2023-05-17", duration: "49:15", size: "25.0 MB" }
        ],
        reports: [
            { id: 1, name: "Awareness Assessment", date: "2023-04-22", type: "pdf", size: "1.1 MB" }
        ],
        aiInsights: [
            { id: 1, title: "Speech Pattern Analysis", date: "2023-05-24", confidence: 84 },
            { id: 2, title: "Emotional Tone Assessment", date: "2023-05-24", confidence: 89 },
            { id: 3, title: "Present Focus Measure", date: "2023-04-22", confidence: 77 }
        ]
    },
    {
        id: 9,
        firstName: "Sofia",
        lastName: "Garcia",
        email: "<EMAIL>",
        phone: "+31 6 90123456",
        dateOfBirth: "1986-02-11",
        gender: "Female",
        address: "Rokin 75, 1012 KL Amsterdam",
        profileImage: "https://cdn.pixabay.com/photo/2017/08/06/15/13/woman-2593366_1280.jpg",
        therapyType: "Trauma-Focused Therapy",
        nextAppointment: "2023-06-16T10:30:00",
        status: "Active",
        notes: "Sofia is working through traumatic experiences using EMDR techniques.",
        documents: [
            { id: 1, name: "Initial Assessment", date: "2022-11-30", type: "pdf", size: "1.5 MB" },
            { id: 2, name: "Treatment Plan", date: "2022-12-12", type: "docx", size: "920 KB" },
            { id: 3, name: "Progress Notes - Q1", date: "2023-03-10", type: "pdf", size: "1.2 MB" }
        ],
        recordings: [
            { id: 1, name: "Session #18", date: "2023-05-26", duration: "65:12", size: "33.2 MB" },
            { id: 2, name: "Session #17", date: "2023-05-19", duration: "60:48", size: "31.0 MB" },
            { id: 3, name: "Session #16", date: "2023-05-12", duration: "62:33", size: "31.8 MB" }
        ],
        reports: [
            { id: 1, name: "Trauma Symptom Inventory", date: "2023-05-05", type: "pdf", size: "2.1 MB" },
            { id: 2, name: "EMDR Progress Report", date: "2023-04-15", type: "pdf", size: "1.5 MB" }
        ],
        aiInsights: [
            { id: 1, title: "Speech Pattern Analysis", date: "2023-05-26", confidence: 86 },
            { id: 2, title: "Emotional Tone Assessment", date: "2023-05-26", confidence: 91 },
            { id: 3, title: "Trauma Narrative Changes", date: "2023-05-05", confidence: 82 },
            { id: 4, title: "Treatment Effectiveness", date: "2023-04-15", confidence: 78 }
        ]
    },
    {
        id: 10,
        firstName: "Thomas",
        lastName: "Brown",
        email: "<EMAIL>",
        phone: "+31 6 01234567",
        dateOfBirth: "1973-11-04",
        gender: "Male",
        address: "De Pijp 58, 1072 LB Amsterdam",
        profileImage: "https://cdn.pixabay.com/photo/2019/10/22/13/43/portrait-4568762_1280.jpg",
        therapyType: "Mindfulness-Based Cognitive Therapy",
        nextAppointment: "2023-06-13T14:00:00",
        status: "Active",
        notes: "Thomas is developing mindfulness practices to manage depression and prevent relapse.",
        documents: [
            { id: 1, name: "Initial Assessment", date: "2022-09-15", type: "pdf", size: "1.4 MB" },
            { id: 2, name: "Treatment Plan", date: "2022-09-28", type: "docx", size: "880 KB" },
            { id: 3, name: "Progress Notes - Q1", date: "2022-12-20", type: "pdf", size: "1.1 MB" },
            { id: 4, name: "Progress Notes - Q2", date: "2023-03-22", type: "pdf", size: "1.2 MB" },
            { id: 5, name: "Progress Notes - Q3", date: "2023-05-18", type: "pdf", size: "1.3 MB" }
        ],
        recordings: [
            { id: 1, name: "Session #36", date: "2023-05-30", duration: "58:45", size: "30.0 MB" },
            { id: 2, name: "Session #35", date: "2023-05-23", duration: "56:12", size: "28.7 MB" },
            { id: 3, name: "Session #34", date: "2023-05-16", duration: "59:27", size: "30.3 MB" }
        ],
        reports: [
            { id: 1, name: "Depression Scale Assessment", date: "2023-05-12", type: "pdf", size: "1.8 MB" },
            { id: 2, name: "Mindfulness Practice Review", date: "2023-04-08", type: "pdf", size: "1.3 MB" },
            { id: 3, name: "Sleep Quality Assessment", date: "2023-03-05", type: "pdf", size: "1.2 MB" }
        ],
        aiInsights: [
            { id: 1, title: "Speech Pattern Analysis", date: "2023-05-30", confidence: 88 },
            { id: 2, title: "Emotional Tone Assessment", date: "2023-05-30", confidence: 85 },
            { id: 3, title: "Depression Markers", date: "2023-05-12", confidence: 79 },
            { id: 4, title: "Treatment Effectiveness", date: "2023-05-18", confidence: 81 }
        ]
    },
    {
        id: 11,
        firstName: "Maria",
        lastName: "Lopez",
        email: "<EMAIL>",
        phone: "+31 6 12345678",
        dateOfBirth: "1988-03-21",
        gender: "Female",
        address: "Utrechtsestraat 45, 1017 VH Amsterdam",
        profileImage: "https://cdn.pixabay.com/photo/2017/08/01/01/33/beanie-2562646_1280.jpg",
        therapyType: "Narrative Therapy",
        nextAppointment: "2023-06-15T10:15:00",
        status: "Active",
        notes: "Maria is exploring her personal narrative and reconstructing her life story in a more empowering way.",
        documents: [
            { id: 1, name: "Initial Assessment", date: "2023-02-12", type: "pdf", size: "1.2 MB" },
            { id: 2, name: "Treatment Plan", date: "2023-02-25", type: "docx", size: "920 KB" },
            { id: 3, name: "Progress Notes - Q1", date: "2023-05-02", type: "pdf", size: "1.1 MB" }
        ],
        recordings: [
            { id: 1, name: "Session #9", date: "2023-05-28", duration: "54:13", size: "27.6 MB" },
            { id: 2, name: "Session #8", date: "2023-05-21", duration: "52:46", size: "26.9 MB" }
        ],
        reports: [
            { id: 1, name: "Narrative Analysis", date: "2023-05-15", type: "pdf", size: "1.3 MB" }
        ],
        aiInsights: [
            { id: 1, title: "Speech Pattern Analysis", date: "2023-05-28", confidence: 88 },
            { id: 2, title: "Emotional Tone Assessment", date: "2023-05-28", confidence: 85 },
            { id: 3, title: "Narrative Structure", date: "2023-05-15", confidence: 92 }
        ]
    },
    {
        id: 12,
        firstName: "Joshua",
        lastName: "Kim",
        email: "<EMAIL>",
        phone: "+31 6 23456789",
        dateOfBirth: "1994-07-18",
        gender: "Male",
        address: "Vondelstraat 28, 1054 GE Amsterdam",
        profileImage: "https://cdn.pixabay.com/photo/2017/11/02/14/26/model-2911330_1280.jpg",
        therapyType: "Existential Therapy",
        nextAppointment: "2023-06-18T14:45:00",
        status: "Active",
        notes: "Joshua is working through existential questions related to purpose and meaning in his life path.",
        documents: [
            { id: 1, name: "Initial Assessment", date: "2023-03-05", type: "pdf", size: "1.1 MB" },
            { id: 2, name: "Treatment Plan", date: "2023-03-18", type: "docx", size: "875 KB" }
        ],
        recordings: [
            { id: 1, name: "Session #7", date: "2023-05-28", duration: "58:21", size: "29.7 MB" },
            { id: 2, name: "Session #6", date: "2023-05-21", duration: "56:08", size: "28.6 MB" }
        ],
        reports: [
            { id: 1, name: "Existential Concerns Report", date: "2023-05-12", type: "pdf", size: "1.4 MB" }
        ],
        aiInsights: [
            { id: 1, title: "Speech Pattern Analysis", date: "2023-05-28", confidence: 84 },
            { id: 2, title: "Emotional Tone Assessment", date: "2023-05-28", confidence: 89 },
            { id: 3, title: "Existential Themes", date: "2023-05-12", confidence: 91 }
        ]
    },
    {
        id: 13,
        firstName: "Nadia",
        lastName: "Patel",
        email: "<EMAIL>",
        phone: "+31 6 34567890",
        dateOfBirth: "1990-11-12",
        gender: "Female",
        address: "Ferdinand Bolstraat 67, 1072 LC Amsterdam",
        profileImage: "https://cdn.pixabay.com/photo/2018/01/13/19/39/fashion-3080644_1280.jpg",
        therapyType: "Schema Therapy",
        nextAppointment: "2023-06-16T09:30:00",
        status: "Active",
        notes: "Nadia is identifying and addressing early maladaptive schemas affecting her current relationships.",
        documents: [
            { id: 1, name: "Initial Assessment", date: "2022-10-30", type: "pdf", size: "1.3 MB" },
            { id: 2, name: "Treatment Plan", date: "2022-11-12", type: "docx", size: "940 KB" },
            { id: 3, name: "Progress Notes - Q1", date: "2023-02-08", type: "pdf", size: "1.0 MB" },
            { id: 4, name: "Progress Notes - Q2", date: "2023-05-10", type: "pdf", size: "1.2 MB" }
        ],
        recordings: [
            { id: 1, name: "Session #28", date: "2023-05-30", duration: "62:15", size: "31.7 MB" },
            { id: 2, name: "Session #27", date: "2023-05-23", duration: "58:42", size: "29.9 MB" },
            { id: 3, name: "Session #26", date: "2023-05-16", duration: "61:09", size: "31.2 MB" }
        ],
        reports: [
            { id: 1, name: "Schema Assessment", date: "2023-04-28", type: "pdf", size: "1.9 MB" },
            { id: 2, name: "Relationship Patterns", date: "2023-03-15", type: "pdf", size: "1.5 MB" }
        ],
        aiInsights: [
            { id: 1, title: "Speech Pattern Analysis", date: "2023-05-30", confidence: 87 },
            { id: 2, title: "Emotional Tone Assessment", date: "2023-05-30", confidence: 90 },
            { id: 3, title: "Schema Identification", date: "2023-04-28", confidence: 85 },
            { id: 4, title: "Treatment Effectiveness", date: "2023-05-10", confidence: 82 }
        ]
    },
    {
        id: 14,
        firstName: "Marcus",
        lastName: "Andersson",
        email: "<EMAIL>",
        phone: "+31 6 45678901",
        dateOfBirth: "1986-04-05",
        gender: "Male",
        address: "Nieuwezijds Voorburgwal 104, 1012 SG Amsterdam",
        profileImage: "https://cdn.pixabay.com/photo/2016/11/21/14/53/man-1845814_1280.jpg",
        therapyType: "Integrative Therapy",
        nextAppointment: "2023-06-19T16:30:00",
        status: "Active",
        notes: "Marcus is benefiting from an integrative approach combining CBT, mindfulness, and psychodynamic techniques.",
        documents: [
            { id: 1, name: "Initial Assessment", date: "2023-01-22", type: "pdf", size: "1.4 MB" },
            { id: 2, name: "Treatment Plan", date: "2023-02-04", type: "docx", size: "915 KB" },
            { id: 3, name: "Progress Notes - Q1", date: "2023-04-25", type: "pdf", size: "1.1 MB" }
        ],
        recordings: [
            { id: 1, name: "Session #12", date: "2023-05-29", duration: "55:37", size: "28.3 MB" },
            { id: 2, name: "Session #11", date: "2023-05-22", duration: "53:50", size: "27.4 MB" },
            { id: 3, name: "Session #10", date: "2023-05-15", duration: "57:22", size: "29.2 MB" }
        ],
        reports: [
            { id: 1, name: "Integrative Assessment", date: "2023-05-05", type: "pdf", size: "1.6 MB" },
            { id: 2, name: "Technique Effectiveness", date: "2023-04-10", type: "pdf", size: "1.3 MB" }
        ],
        aiInsights: [
            { id: 1, title: "Speech Pattern Analysis", date: "2023-05-29", confidence: 90 },
            { id: 2, title: "Emotional Tone Assessment", date: "2023-05-29", confidence: 86 },
            { id: 3, title: "Treatment Effectiveness", date: "2023-05-05", confidence: 88 },
            { id: 4, title: "Mindfulness Indicators", date: "2023-04-10", confidence: 84 }
        ]
    }
];

// Function to get a client by ID
function getClientById(id) {
    return mockClients.find(client => client.id === parseInt(id));
}

// Function to get all clients, optionally sorted
function getAllClients(sortAlphabetically = true) {
    if (sortAlphabetically) {
        return [...mockClients].sort((a, b) => {
            const nameA = `${a.firstName} ${a.lastName}`.toLowerCase();
            const nameB = `${b.firstName} ${b.lastName}`.toLowerCase();
            return nameA.localeCompare(nameB);
        });
    }
    return mockClients;
}

// Function to filter clients by name
function filterClientsByName(searchTerm, sortAlphabetically = true) {
    const term = searchTerm.toLowerCase();
    let filtered = mockClients.filter(client => 
        client.firstName.toLowerCase().includes(term) || 
        client.lastName.toLowerCase().includes(term)
    );
    
    if (sortAlphabetically) {
        filtered = filtered.sort((a, b) => {
            const nameA = `${a.firstName} ${a.lastName}`.toLowerCase();
            const nameB = `${b.firstName} ${b.lastName}`.toLowerCase();
            return nameA.localeCompare(nameB);
        });
    }
    
    return filtered;
}

// Function to filter clients by therapy type
function filterClientsByTherapyType(therapyType) {
    return mockClients.filter(client => 
        client.therapyType === therapyType
    );
}

// Function to filter clients by upcoming appointments
function getUpcomingAppointments(days = 7) {
    const currentDate = new Date();
    const futureDate = new Date();
    futureDate.setDate(currentDate.getDate() + days);

    return mockClients.filter(client => {
        const appointmentDate = new Date(client.nextAppointment);
        return appointmentDate >= currentDate && appointmentDate <= futureDate;
    }).sort((a, b) => new Date(a.nextAppointment) - new Date(b.nextAppointment));
}

// Export functions if using ES modules
// export { mockClients, getClientById, getAllClients, filterClientsByName, filterClientsByTherapyType, getUpcomingAppointments };
