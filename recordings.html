<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wellora - Voice Recordings</title>
    <!-- Remove CDN Tailwind -->
    <!-- <script src="https://cdn.tailwindcss.com"></script> -->
    <link rel="stylesheet" href="styles.css">
    <!-- Remove Font Awesome CDN -->
    <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"> -->
    <link rel="stylesheet" href="node_modules/@fortawesome/fontawesome-free/css/all.min.css">
    <link rel="stylesheet" href="dark-theme.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0F6FFF',
                        secondary: '#00B0B6',
                        tertiary: '#00D695',
                        dark: '#0A2540',
                        light: '#F7F9FC',
                    },
                    fontFamily: {
                        'sans': ['Open Sans', 'sans-serif'],
                    },
                    animation: {
                        'pulse-slow': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'wave': 'wave 1.2s infinite ease-in-out',
                        'float': 'float 6s ease-in-out infinite',
                    },
                    keyframes: {
                        wave: {
                            '0%, 100%': { transform: 'scaleY(0.3)' },
                            '50%': { transform: 'scaleY(1)' },
                        },
                        float: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-10px)' }
                        },
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Open Sans', sans-serif;
            background-color: #f0f4f8;
        }
        
        .recording-wave {
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .recording-wave span {
            display: inline-block;
            width: 3px;
            height: 20px;
            margin: 0 1px;
            background-color: #0F6FFF;
            animation: wave 1.2s infinite ease-in-out;
            border-radius: 2px;
        }
        
        .recording-wave span:nth-child(2) {
            animation-delay: 0.1s;
        }
        
        .recording-wave span:nth-child(3) {
            animation-delay: 0.2s;
        }
        
        .recording-wave span:nth-child(4) {
            animation-delay: 0.3s;
        }
        
        .recording-wave span:nth-child(5) {
            animation-delay: 0.4s;
        }
        
        .recording-wave span:nth-child(6) {
            animation-delay: 0.5s;
        }
        
        .recording-wave span:nth-child(7) {
            animation-delay: 0.6s;
        }
        
        .recording-wave span:nth-child(8) {
            animation-delay: 0.7s;
        }
        
        @keyframes wave {
            0%, 100% {
                height: 10px;
            }
            50% {
                height: 40px;
            }
        }
        
        .visualizer {
            height: 60px;
            width: 100%;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            gap: 2px;
        }
        
        .visualizer-bar {
            width: 6px;
            background: linear-gradient(to top, #0F6FFF, #00D695);
            border-radius: 2px;
        }
        
        .timer-circle {
            stroke-dasharray: 283;
            transition: stroke-dashoffset 1s linear;
            transform: rotate(-90deg);
            transform-origin: 50% 50%;
        }

        @keyframes pulse {
            0%, 100% {
                box-shadow: 0 0 0 0 rgba(15, 111, 255, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(15, 111, 255, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(15, 111, 255, 0);
            }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        .data-pulse {
            position: relative;
        }
        
        .data-pulse::after {
            content: "";
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #00D695;
            top: 0px;
            right: 0px;
            animation: pulse 2s infinite;
        }
        
        /* Search highlighting */
        .highlight-search {
            background-color: rgba(255, 255, 0, 0.4);
            padding: 0 2px;
            border-radius: 2px;
        }
        
        /* Audio segment player */
        .audio-segment {
            cursor: pointer;
            position: relative;
        }
        
        .audio-segment:hover {
            background-color: rgba(15, 111, 255, 0.1);
        }
        
        .audio-segment::after {
            content: "▶";
            font-size: 10px;
            position: absolute;
            top: -8px;
            right: -2px;
            background-color: #0F6FFF;
            color: white;
            border-radius: 50%;
            width: 14px;
            height: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.2s;
        }
        
        .audio-segment:hover::after {
            opacity: 1;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
    </style>
</head>
<body class="dark-theme">
    <!-- Script for theme toggle functionality -->
    <!-- Include html2pdf.js library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js" integrity="sha512-GsLlZN/3F2ErC5ifS5QtgpiJtWd43JWSuIgh7mbzZ8zBps+dvLusV+eNQATqgA/HdeKFVgA5v3S/cIrLF7QnIg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const themeToggle = document.getElementById('themeToggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', () => {
                    document.body.classList.toggle('dark-theme');
                    const isDark = document.body.classList.contains('dark-theme');
                    localStorage.setItem('theme', isDark ? 'dark' : 'light');
                });
            }
            
            // Check for saved theme preference or use device preference
            const savedTheme = localStorage.getItem('theme');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            
            if (savedTheme === 'dark' || (!savedTheme && prefersDark)) {
                document.body.classList.add('dark-theme');
            } else {
                document.body.classList.remove('dark-theme');
            }

            // Initialize visualization and recording functions
            initializeRecordingFunctions();
        });

        // Recording functions
        let recorder;
        let audioChunks = [];
        let audioBlob;
        let audioUrl;
        let audio;
        let recordingTimer;
        let recordingSeconds = 0;
        let visualizer;
        let isRecording = false;

        function initializeRecordingFunctions() {
            const startBtn = document.getElementById('startRecordingBtn');
            const stopBtn = document.getElementById('stopRecordingBtn');
            const visualizerContainer = document.getElementById('audioVisualizer');
            
            if (startBtn) {
                startBtn.addEventListener('click', async () => {
                    await startRecording();
                    updateUIForRecording(true);
                });
            }
            
            if (stopBtn) {
                stopBtn.addEventListener('click', () => {
                    stopRecording();
                    updateUIForRecording(false);
                });
            }

            // Create the visualizer bars
            if (visualizerContainer) {
                visualizer = visualizerContainer;
                for (let i = 0; i < 30; i++) {
                    const bar = document.createElement('div');
                    bar.className = 'visualizer-bar';
                    bar.style.height = '3px';
                    visualizer.appendChild(bar);
                }
            }
            
            // Setup recorder and timer elements
            setupTimerCircle();
        }

        function setupTimerCircle() {
            const circle = document.getElementById('timerCircle');
            if (circle) {
                const circumference = 283; // 2 * Math.PI * 45
                circle.style.strokeDashoffset = circumference;
            }
        }

        function updateTimerCircle(seconds, maxSeconds = 300) {
            const circle = document.getElementById('timerCircle');
            if (circle) {
                const circumference = 283; // 2 * Math.PI * 45
                const offset = circumference - (seconds / maxSeconds) * circumference;
                circle.style.strokeDashoffset = offset;
            }
        }

        async function startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                recorder = new MediaRecorder(stream);
                audioChunks = [];
                
                recorder.ondataavailable = event => audioChunks.push(event.data);
                recorder.onstop = () => {
                    audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    audioUrl = URL.createObjectURL(audioBlob);
                    audio = new Audio(audioUrl);
                    saveRecording();
                };
                
                recorder.start();
                isRecording = true;
                
                // Start timer
                recordingSeconds = 0;
                recordingTimer = setInterval(() => {
                    recordingSeconds++;
                    updateRecordingTimer();
                    updateTimerCircle(recordingSeconds);
                    updateVisualizer();
                }, 1000);
                
                // Connect to audio analyzer for visualization
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const analyser = audioContext.createAnalyser();
                const source = audioContext.createMediaStreamSource(stream);
                source.connect(analyser);
                analyser.fftSize = 64;
                
                const bufferLength = analyser.frequencyBinCount;
                const dataArray = new Uint8Array(bufferLength);
                
                // Animation function to update visualizer
                function updateVisualizerAnimation() {
                    if (!isRecording) return;
                    
                    requestAnimationFrame(updateVisualizerAnimation);
                    analyser.getByteFrequencyData(dataArray);
                    
                    const bars = document.querySelectorAll('.visualizer-bar');
                    const step = Math.floor(dataArray.length / bars.length);
                    
                    for (let i = 0; i < bars.length; i++) {
                        const value = dataArray[i * step];
                        const height = Math.max(3, value / 2); // Scale value to reasonable height
                        bars[i].style.height = height + 'px';
                    }
                }
                
                updateVisualizerAnimation();
                
            } catch (error) {
                console.error('Error starting recording:', error);
                alert('Could not access the microphone. Please ensure you have granted permission.');
            }
        }

        function stopRecording() {
            if (recorder && recorder.state !== 'inactive') {
                recorder.stop();
                isRecording = false;
                clearInterval(recordingTimer);
                
                const tracks = recorder.stream.getTracks();
                tracks.forEach(track => track.stop());
            }
        }

        function updateRecordingTimer() {
            const minutes = Math.floor(recordingSeconds / 60).toString().padStart(2, '0');
            const seconds = (recordingSeconds % 60).toString().padStart(2, '0');
            document.getElementById('recordingTime').textContent = `${minutes}:${seconds}`;
        }

        function updateVisualizer() {
            if (!isRecording) {
                // Reset visualizer when not recording
                const bars = document.querySelectorAll('.visualizer-bar');
                bars.forEach(bar => {
                    bar.style.height = '3px';
                });
                return;
            }
            
            // Random visualization when we don't have real audio data
            const bars = document.querySelectorAll('.visualizer-bar');
            bars.forEach(bar => {
                const randomHeight = Math.floor(Math.random() * 30) + 5;
                bar.style.height = `${randomHeight}px`;
            });
        }

        function updateUIForRecording(isRecording) {
            const startButton = document.getElementById('startRecordingBtn');
            const stopButton = document.getElementById('stopRecordingBtn');
            const status = document.getElementById('recordingStatus');
            const waveAnimation = document.getElementById('recordingWave');
            
            if (isRecording) {
                startButton.classList.add('hidden');
                stopButton.classList.remove('hidden');
                status.textContent = 'Recording...';
                status.classList.add('text-red-500');
                waveAnimation.classList.remove('hidden');
            } else {
                startButton.classList.remove('hidden');
                stopButton.classList.add('hidden');
                status.textContent = 'Ready to record';
                status.classList.remove('text-red-500');
                waveAnimation.classList.add('hidden');
            }
        }

        function saveRecording() {
            // Create a timestamp for the file name
            const now = new Date();
            const timestamp = now.toISOString().replace(/[:.]/g, '-');
            const fileName = `recording-${timestamp}.wav`;
            
            // In a real application, you would upload this file to your server
            // For now, we'll simulate adding it to the UI

            // Format duration
            const minutes = Math.floor(recordingSeconds / 60).toString().padStart(2, '0');
            const seconds = (recordingSeconds % 60).toString().padStart(2, '0');
            const duration = `${minutes}:${seconds}`;
            
            // Estimate file size (rough approximation)
            const fileSizeMB = (audioBlob.size / (1024 * 1024)).toFixed(2);
            
            // Add to recordings table
            addRecordingToTable({
                id: Date.now(),
                name: fileName,
                date: now.toLocaleDateString(),
                time: now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}),
                duration: duration,
                size: `${fileSizeMB} MB`,
                url: audioUrl
            });
            
            // Show success message
            const successAlert = document.getElementById('recordingSuccessAlert');
            if (successAlert) {
                successAlert.classList.remove('hidden');
                setTimeout(() => {
                    successAlert.classList.add('hidden');
                }, 5000);
            }
        }

        function addRecordingToTable(recording) {
            const recordingsList = document.getElementById('recordingsList');
            if (!recordingsList) return;
            
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50 dark:hover:bg-gray-800';
            row.dataset.recordingId = recording.id;
            
            row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-8 w-8 rounded bg-blue-100 flex items-center justify-center mr-3">
                            <i class="fas fa-microphone text-primary"></i>
                        </div>
                        <div>
                            <p class="font-medium text-gray-900 dark:text-white">${recording.name}</p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">${recording.date} • ${recording.time}</p>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    ${recording.duration}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    ${recording.size}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                    <button class="play-recording-btn text-primary hover:text-primary/80 mx-1" data-url="${recording.url}">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="download-recording-btn text-primary hover:text-primary/80 mx-1" data-url="${recording.url}" data-name="${recording.name}">
                        <i class="fas fa-download"></i>
                    </button>
                    <button class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 mx-1 delete-recording-btn" data-id="${recording.id}">
                        <i class="fas fa-trash-alt"></i>
                    </button>
                </td>
            `;
            
            recordingsList.insertBefore(row, recordingsList.firstChild);
            
            // Add event listeners
            const playBtn = row.querySelector('.play-recording-btn');
            const downloadBtn = row.querySelector('.download-recording-btn');
            const deleteBtn = row.querySelector('.delete-recording-btn');
            
            playBtn.addEventListener('click', function() {
                const url = this.dataset.url;
                playRecording(url);
            });
            
            downloadBtn.addEventListener('click', function() {
                const url = this.dataset.url;
                const name = this.dataset.name;
                downloadRecording(url, name);
            });
            
            deleteBtn.addEventListener('click', function() {
                const id = this.dataset.id;
                deleteRecording(id);
            });
        }

        function playRecording(url) {
            // Stop any currently playing audio
            if (audio && !audio.paused) {
                audio.pause();
            }
            
            // Play the selected recording
            audio = new Audio(url);
            audio.play();
        }
        
        // Function to show the selected tab and hide others
        function showTab(tabId) {
            // Hide all tab panes
            document.querySelectorAll('.tab-pane').forEach(tab => {
                tab.classList.add('hidden');
            });
            
            // Reset all tab buttons
            document.querySelectorAll('.border-b-2').forEach(button => {
                button.classList.remove('border-primary', 'text-primary');
                button.classList.add('border-transparent', 'text-gray-500', 'dark:text-gray-400');
            });
            
            // Show the selected tab
            const selectedTab = document.getElementById(tabId);
            if (selectedTab) {
                selectedTab.classList.remove('hidden');
            }
            
            // Highlight the selected tab button
            const selectedButton = document.querySelector(`button[onclick="showTab('${tabId}')"]`);
            if (selectedButton) {
                selectedButton.classList.remove('border-transparent', 'text-gray-500', 'dark:text-gray-400');
                selectedButton.classList.add('border-primary', 'text-primary', 'dark:text-primary');
            }
        }
        
        function openRecordingAnalysisModal(recordingId, recordingName, isTranscribed) {
            const modal = document.getElementById('recordingAnalysisModal');
            const recordingTitle = document.getElementById('analysisRecordingTitle');
            const transcriptionSection = document.getElementById('transcriptionSection');
            const transcribeButton = document.getElementById('transcribeButton');
            const analysisContent = document.getElementById('analysisContent');
            const analysisResults = document.getElementById('analysisResults');
            const transcriptionText = document.getElementById('transcriptionText');
            const transcriptionSummary = document.getElementById('transcriptionSummary');
            
            // Set recording title
            recordingTitle.textContent = recordingName;
            
            // Show/hide appropriate sections based on transcription status
            if (isTranscribed) {
                transcribeButton.classList.add('hidden');
                analysisContent.classList.remove('hidden');
                
                // For demonstration, show mock transcription data
                transcriptionText.innerHTML = generateMockTranscription();
                transcriptionSummary.innerHTML = generateMockSummary();
                
                // Display analysis results
                analysisResults.innerHTML = generateMockAnalysis();
                
                // Default to showing the transcription tab
                showTab('transcriptionTab');
            } else {
                transcribeButton.classList.remove('hidden');
                analysisContent.classList.add('hidden');
                
                // Reset analysis content
                transcriptionText.innerHTML = '';
                transcriptionSummary.innerHTML = '';
                analysisResults.innerHTML = '';
            }
            
            // Show the modal
            modal.classList.remove('hidden');
            setTimeout(() => {
                const modalContent = modal.querySelector('.modal-content');
                modalContent.classList.remove('scale-95', 'opacity-0');
                modalContent.classList.add('scale-100', 'opacity-100');
            }, 10);
        }
        
        // Function to open Issue Tracking modal
        function openIssueTrackingModal(recordingId, recordingName) {
            const modal = document.getElementById('issueTrackingModal');
            const patientName = document.getElementById('issueTrackingPatientName');
            const recordingNameElement = document.getElementById('issueTrackingRecordingName');
            const lastUpdated = document.getElementById('issueTrackingLastUpdated');
            
            // Set patient info
            recordingNameElement.textContent = recordingName || 'No recording selected';
            
            // Update last updated date
            lastUpdated.textContent = new Date().toLocaleDateString();
            
            // Load patient issues
            loadPatientIssues();
            
            // Show the modal
            modal.classList.remove('hidden');
        }
        
        function startTranscription(recordingId) {
            // Show loading state
            const transcribeButton = document.getElementById('transcribeButton');
            const transcriptionLoading = document.getElementById('transcriptionLoading');
            const analysisContent = document.getElementById('analysisContent');
            
            transcribeButton.classList.add('hidden');
            transcriptionLoading.classList.remove('hidden');
            
            // Simulate API call to OpenAI
            setTimeout(() => {
                // Hide loading, show results
                transcriptionLoading.classList.add('hidden');
                analysisContent.classList.remove('hidden');
                
                // Display mock data
                document.getElementById('transcriptionText').innerHTML = generateMockTranscription();
                document.getElementById('transcriptionSummary').innerHTML = generateMockSummary();
                document.getElementById('analysisResults').innerHTML = generateMockAnalysis();
                
                // Update the table row to show as transcribed
                const row = document.querySelector(`tr[data-id="${recordingId}"]`);
                if (row) {
                    row.dataset.transcribed = "true";
                    row.dataset.aiProcessed = "true";
                    
                    // Add AI processed badge
                    const nameElement = row.querySelector('.font-medium');
                    const badgesContainer = row.querySelector('.flex.items-center');
                    
                    if (!badgesContainer.querySelector('.bg-green-100')) {
                        const badge = document.createElement('span');
                        badge.className = 'ml-2 px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full dark:bg-green-900 dark:text-green-200';
                        badge.textContent = 'AI Processed';
                        badgesContainer.appendChild(badge);
                    }
                }
            }, 3000);
        }
        
        function generateMockTranscription() {
            // Sample conversation between therapist and patient
            return `
                <div class="space-y-4">
                    <div class="flex">
                        <div class="w-24 flex-shrink-0 font-medium text-primary">Therapist:</div>
                        <div class="flex-1">Hello Emily, it's good to see you again. How have you been feeling since our last session?</div>
                    </div>
                    <div class="flex">
                        <div class="w-24 flex-shrink-0 font-medium text-purple-600">Patient:</div>
                        <div class="flex-1" data-topic="headaches">Hi Dr. Matthews. I've been having some ups and downs. The <span class="bg-yellow-100 dark:bg-yellow-800/30 px-1">headaches</span> have been less frequent, but I'm still feeling tired most days.</div>
                    </div>
                    <div class="flex">
                        <div class="w-24 flex-shrink-0 font-medium text-primary">Therapist:</div>
                        <div class="flex-1" data-topic="medication">I'm glad to hear the headaches are improving. Have you been taking the <span class="bg-blue-100 dark:bg-blue-800/30 px-1">Amitriptyline</span> regularly as prescribed?</div>
                    </div>
                    <div class="flex">
                        <div class="w-24 flex-shrink-0 font-medium text-purple-600">Patient:</div>
                        <div class="flex-1" data-topic="sleep">Yes, every night before bed. I think it's helping with the <span class="bg-yellow-100 dark:bg-yellow-800/30 px-1">sleep problems</span> too, but I still feel <span class="bg-yellow-100 dark:bg-yellow-800/30 px-1">anxious</span> during the day, especially at work.</div>
                    </div>
                    <div class="flex">
                        <div class="w-24 flex-shrink-0 font-medium text-primary">Therapist:</div>
                        <div class="flex-1" data-topic="coping">Have you been practicing the breathing exercises we discussed?</div>
                    </div>
                    <div class="flex">
                        <div class="w-24 flex-shrink-0 font-medium text-purple-600">Patient:</div>
                        <div class="flex-1" data-topic="coping anxiety">I tried them a few times, but it's hard to remember when I'm in the middle of feeling <span class="bg-yellow-100 dark:bg-yellow-800/30 px-1">overwhelmed</span>. I've been thinking I might need something else to help with the <span class="bg-yellow-100 dark:bg-yellow-800/30 px-1">anxiety</span>.</div>
                    </div>
                    <div class="flex">
                        <div class="w-24 flex-shrink-0 font-medium text-primary">Therapist:</div>
                        <div class="flex-1" data-topic="medication">I understand. Let's discuss some additional strategies today, and we might consider adjusting your medication. Have you experienced any side effects from the <span class="bg-blue-100 dark:bg-blue-800/30 px-1">Amitriptyline</span>?</div>
                    </div>
                </div>
            `;
        }
        
        function generateMockSummary() {
            return `
                <p class="text-gray-700 dark:text-gray-300 mb-4">This follow-up session with Emily R. focused on assessing the progress of her treatment for headaches, sleep issues, and anxiety.</p>
                
                <div class="space-y-3">
                    <!-- Card 1 - Coping Techniques (Most severe) -->
                    <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-t-4 border-t-red-500 p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="highlightTranscriptionSegment('coping')" data-topic="coping">
                        <div class="absolute -top-3 -left-3">
                            <button class="severity-indicator h-8 w-8 rounded-full bg-red-500 text-white font-bold flex items-center justify-center shadow-md hover:shadow-lg transition blink-severe" data-severity="severe" data-issue="Coping Techniques" onclick="toggleSeverityMenu(this); event.stopPropagation();">
                                1
                            </button>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-red-600 dark:text-red-400 text-xs font-medium uppercase tracking-wider">Severe Pain</p>
                                <h3 class="text-gray-800 dark:text-white font-semibold">Coping Techniques</h3>
                            </div>
                            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" onclick="toggleCardDetails(this); event.stopPropagation();">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <div class="card-details mt-3 hidden">
                            <p class="text-gray-600 dark:text-gray-300 text-sm">Difficulty implementing breathing techniques during anxiety episodes.</p>
                        </div>
                    </div>
                    
                    <!-- Card 2 - Anxiety -->
                    <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-t-4 border-t-orange-500 p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="highlightTranscriptionSegment('anxiety')" data-topic="anxiety">
                        <div class="absolute -top-3 -left-3">
                            <button class="severity-indicator h-8 w-8 rounded-full bg-orange-500 text-white font-bold flex items-center justify-center shadow-md hover:shadow-lg transition" data-severity="persistent" data-issue="Anxiety" onclick="toggleSeverityMenu(this); event.stopPropagation();">
                                2
                            </button>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-orange-600 dark:text-orange-400 text-xs font-medium uppercase tracking-wider">Persistent Pain</p>
                                <h3 class="text-gray-800 dark:text-white font-semibold">Anxiety</h3>
                            </div>
                            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" onclick="toggleCardDetails(this); event.stopPropagation();">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <div class="card-details mt-3 hidden">
                            <p class="text-gray-600 dark:text-gray-300 text-sm">Ongoing anxiety issues, particularly in workplace settings.</p>
                        </div>
                    </div>
                    
                    <!-- Card 3 - Headaches -->
                    <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-t-4 border-t-yellow-500 p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="highlightTranscriptionSegment('headaches')" data-topic="headaches">
                        <div class="absolute -top-3 -left-3">
                            <button class="severity-indicator h-8 w-8 rounded-full bg-yellow-500 text-white font-bold flex items-center justify-center shadow-md hover:shadow-lg transition" data-severity="moderate" data-issue="Headaches" onclick="toggleSeverityMenu(this); event.stopPropagation();">
                                3
                            </button>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-yellow-600 dark:text-yellow-400 text-xs font-medium uppercase tracking-wider">Moderate Discomfort</p>
                                <h3 class="text-gray-800 dark:text-white font-semibold">Headaches</h3>
                            </div>
                            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" onclick="toggleCardDetails(this); event.stopPropagation();">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <div class="card-details mt-3 hidden">
                            <p class="text-gray-600 dark:text-gray-300 text-sm">Patient reports improvement in headache frequency, but still experiences occasional episodes.</p>
                        </div>
                    </div>
                    
                    <!-- Card 4 - Medication -->
                    <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-t-4 border-t-blue-500 p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="highlightTranscriptionSegment('medication')" data-topic="medication">
                        <div class="absolute -top-3 -left-3">
                            <button class="severity-indicator h-8 w-8 rounded-full bg-blue-500 text-white font-bold flex items-center justify-center shadow-md hover:shadow-lg transition" data-severity="moderate" data-issue="Medication" onclick="toggleSeverityMenu(this); event.stopPropagation();">
                                4
                            </button>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-blue-600 dark:text-blue-400 text-xs font-medium uppercase tracking-wider">Moderate Discomfort</p>
                                <h3 class="text-gray-800 dark:text-white font-semibold">Medication</h3>
                            </div>
                            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" onclick="toggleCardDetails(this); event.stopPropagation();">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <div class="card-details mt-3 hidden">
                            <p class="text-gray-600 dark:text-gray-300 text-sm">Discussion about possible medication adjustments.</p>
                        </div>
                    </div>
                    
                    <!-- Card 5 - Sleep Issues -->
                    <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-t-4 border-t-green-500 p-4 hover:shadow-md transition-shadow cursor-pointer" onclick="highlightTranscriptionSegment('sleep')" data-topic="sleep">
                        <div class="absolute -top-3 -left-3">
                            <button class="severity-indicator h-8 w-8 rounded-full bg-green-500 text-white font-bold flex items-center justify-center shadow-md hover:shadow-lg transition" data-severity="mild" data-issue="Sleep Issues" onclick="toggleSeverityMenu(this); event.stopPropagation();">
                                5
                            </button>
                        </div>
                        <div class="flex justify-between items-center">
                            <div>
                                <p class="text-green-600 dark:text-green-400 text-xs font-medium uppercase tracking-wider">Mild Discomfort</p>
                                <h3 class="text-gray-800 dark:text-white font-semibold">Sleep Issues</h3>
                            </div>
                            <button class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" onclick="toggleCardDetails(this); event.stopPropagation();">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <div class="card-details mt-3 hidden">
                            <p class="text-gray-600 dark:text-gray-300 text-sm">Sleep quality has improved with Amitriptyline medication.</p>
                        </div>
                    </div>
                </div>
                
                <style>
                    @keyframes blink-severe {
                        0%, 100% { opacity: 1; }
                        50% { opacity: 0.6; }
                    }
                    .blink-severe {
                        animation: blink-severe 500ms infinite;
                    }
                    /* Hover effect for cards */
                    .relative:hover .card-details {
                        display: block;
                    }
                </style>
                
                <!-- Issue Tracking Modal -->
                <div id="issueTrackingModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
                        <!-- Modal Header -->
                        <div class="border-b border-gray-200 dark:border-gray-700 p-5 flex justify-between items-center bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-850">
                            <div>
                                <h3 class="text-xl font-bold text-gray-800 dark:text-white flex items-center">
                                    <i class="fas fa-chart-line text-blue-600 dark:text-blue-400 mr-2"></i>
                                    Patient Issue Tracking
                                </h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Track and manage patient issues over time</p>
                            </div>
                            <button id="closeIssueTrackingModal" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400 transition-colors">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    
                        <!-- Modal Body -->
                        <div class="overflow-y-auto p-5">
                            <div id="patientIssueInfo" class="bg-blue-50 dark:bg-gray-800 rounded-lg p-4 mb-5 flex flex-wrap">
                                <div class="w-full md:w-1/3 mb-3 md:mb-0">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Patient</p>
                                    <p class="font-semibold text-gray-800 dark:text-white text-lg" id="issueTrackingPatientName">Emily R.</p>
                                </div>
                                <div class="w-full md:w-1/3 mb-3 md:mb-0">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Latest Recording</p>
                                    <p class="font-semibold text-gray-800 dark:text-white" id="issueTrackingRecordingName">--</p>
                                </div>
                                <div class="w-full md:w-1/3">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Last Updated</p>
                                    <p class="font-semibold text-gray-800 dark:text-white" id="issueTrackingLastUpdated">${new Date().toLocaleDateString()}</p>
                                </div>
                            </div>

                            <!-- Action buttons -->
                            <div class="flex justify-between items-center mb-6">
                                <h4 class="text-lg font-semibold text-gray-800 dark:text-white">Issue Severity Tracking</h4>
                                <button id="addNewIssueBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center text-sm transition-colors">
                                    <i class="fas fa-plus mr-2"></i> Add New Issue
                                </button>
                            </div>
                            
                            <!-- Issues List -->
                            <div id="issueTrackingList" class="space-y-6">
                                <!-- Dynamically populated by JavaScript -->
                            </div>
                        </div>
                        
                        <!-- Modal Footer -->
                        <div class="border-t border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-800 flex justify-between items-center">
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                Changes are saved automatically
                            </div>
                            <button id="generateIssueReportBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors text-sm">
                                <i class="fas fa-file-pdf mr-2"></i> Generate Tracking Report
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Add New Issue Modal -->
                <div id="addIssueModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                    <div class="bg-white dark:bg-gray-900 rounded-xl shadow-2xl w-full max-w-lg overflow-hidden flex flex-col">
                        <div class="border-b border-gray-200 dark:border-gray-700 p-4">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white">Add New Issue</h3>
                        </div>
                        <div class="p-5">
                            <div class="mb-4">
                                <label for="newIssueTitle" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Issue Title</label>
                                <input type="text" id="newIssueTitle" class="w-full px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="e.g., Headaches, Anxiety, Sleep issues">
                            </div>
                            
                            <div class="mb-4">
                                <label for="newIssueCategory" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category</label>
                                <select id="newIssueCategory" class="w-full px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="pain">Pain</option>
                                    <option value="mental">Mental Health</option>
                                    <option value="sleep">Sleep</option>
                                    <option value="physical">Physical Symptom</option>
                                    <option value="medication">Medication Effect</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            
                            <div class="mb-4">
                                <label for="newIssueSeverity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Initial Severity (1-10)
                                </label>
                                <div class="flex items-center">
                                    <span class="text-gray-500 dark:text-gray-400 text-sm">1</span>
                                    <input type="range" id="newIssueSeverity" min="1" max="10" value="5" class="mx-3 flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-blue-600">
                                    <span class="text-gray-500 dark:text-gray-400 text-sm">10</span>
                                    <span id="newIssueSeverityValue" class="ml-3 text-blue-600 dark:text-blue-400 font-semibold">5</span>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="newIssueNotes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Notes</label>
                                <textarea id="newIssueNotes" rows="3" class="w-full px-3 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="Add any relevant details about this issue"></textarea>
                            </div>
                        </div>
                        <div class="border-t border-gray-200 dark:border-gray-700 p-4 flex justify-end space-x-3 bg-gray-50 dark:bg-gray-800">
                            <button id="cancelAddIssue" class="px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                                Cancel
                            </button>
                            <button id="saveNewIssue" class="px-4 py-2 rounded-lg bg-blue-600 text-white hover:bg-blue-700 transition-colors">
                                Add Issue
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Issue Template (Hidden, used by JavaScript to create new entries) -->
                <template id="issueTrackingTemplate">
                    <div class="issue-card bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
                        <div class="p-4">
                            <div class="flex justify-between items-start">
                                <div>
                                    <div class="flex items-center">
                                        <h4 class="font-semibold text-gray-800 dark:text-white text-lg issue-title">Headaches</h4>
                                        <span class="ml-2 px-2 py-0.5 text-xs rounded-full issue-category bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">Pain</span>
                                    </div>
                                    <div class="flex items-center mt-1 text-sm">
                                        <span class="text-gray-500 dark:text-gray-400">Tracking since:</span>
                                        <span class="ml-1 issue-start-date">Feb 15, 2024</span>
                                    </div>
                                </div>
                                <div class="flex">
                                    <button class="issue-notes-btn p-1.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400">
                                        <i class="fas fa-sticky-note"></i>
                                    </button>
                                    <button class="issue-history-btn p-1.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400 ml-1">
                                        <i class="fas fa-history"></i>
                                    </button>
                                    <button class="issue-delete-btn p-1.5 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-500 dark:text-gray-400 ml-1">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Severity tracking section -->
                            <div class="mt-4 border-t border-gray-100 dark:border-gray-700 pt-4">
                                <div class="flex justify-between items-center mb-2">
                                    <div class="flex items-center">
                                        <span class="text-gray-700 dark:text-gray-300 font-medium">Current Severity:</span>
                                        <span class="ml-2 font-bold text-lg issue-current-value">7</span>
                                        <span class="issue-trend ml-2 text-sm font-medium text-red-500">▲ Increased</span>
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        <span class="issue-previous-date">Feb 28, 2024</span>: <span class="issue-previous-value">5</span>
                                    </div>
                                </div>
                                
                                <!-- Severity slider -->
                                <div class="flex items-center mt-2">
                                    <span class="text-xs text-gray-500 dark:text-gray-400">1</span>
                                    <input type="range" class="issue-severity-slider mx-3 flex-1 h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer accent-blue-600" min="1" max="10" value="7">
                                    <span class="text-xs text-gray-500 dark:text-gray-400">10</span>
                                </div>
                                
                                <!-- Mini chart showing severity over time -->
                                <div class="issue-chart h-24 w-full mt-4 mb-2 bg-gray-50 dark:bg-gray-900 rounded border border-gray-200 dark:border-gray-700 p-2">
                                    <!-- Chart will be drawn here by JavaScript -->
                                </div>
                                
                                <!-- Notes area (collapsed by default) -->
                                <div class="issue-notes-area hidden mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                                    <div class="flex justify-between mb-2">
                                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Notes</span>
                                        <button class="add-note-btn text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                            + Add note
                                        </button>
                                    </div>
                                    <div class="issue-notes-list space-y-2 text-sm text-gray-600 dark:text-gray-400">
                                        <!-- Notes will be added here dynamically -->
                                    </div>
                                    <div class="issue-add-note-form hidden mt-2">
                                        <textarea class="issue-new-note w-full px-3 py-2 text-sm rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100" rows="2" placeholder="Add a new note..."></textarea>
                                        <div class="flex justify-end mt-2 space-x-2">
                                            <button class="issue-cancel-note px-3 py-1 text-xs rounded border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                                Cancel
                                            </button>
                                            <button class="issue-save-note px-3 py-1 text-xs rounded bg-blue-600 text-white hover:bg-blue-700">
                                                Save Note
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- History panel (collapsed by default) -->
                                <div class="issue-history-panel hidden mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                                    <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Severity History</div>
                                    <div class="issue-history-list max-h-40 overflow-y-auto text-sm">
                                        <!-- History entries will be added here by JavaScript -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>

                <!-- Severity Selection Menu (hidden by default) -->
                <div id="severityMenu" class="hidden fixed bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-2 w-48 z-50">
                    <div class="space-y-1">
                        <button class="severity-option w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center" data-value="mild" data-color="green-500">
                            <span class="h-3 w-3 rounded-full bg-green-500 mr-2"></span> 
                            <span class="text-sm">Mild Discomfort</span>
                        </button>
                        <button class="severity-option w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center" data-value="occasional" data-color="teal-500">
                            <span class="h-3 w-3 rounded-full bg-teal-500 mr-2"></span> 
                            <span class="text-sm">Occasional Pain</span>
                        </button>
                        <button class="severity-option w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center" data-value="moderate" data-color="yellow-500">
                            <span class="h-3 w-3 rounded-full bg-yellow-500 mr-2"></span> 
                            <span class="text-sm">Moderate Discomfort</span>
                        </button>
                        <button class="severity-option w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center" data-value="persistent" data-color="orange-500">
                            <span class="h-3 w-3 rounded-full bg-orange-500 mr-2"></span> 
                            <span class="text-sm">Persistent Pain</span>
                        </button>
                        <button class="severity-option w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center" data-value="severe" data-color="red-500">
                            <span class="h-3 w-3 rounded-full bg-red-500 mr-2"></span> 
                            <span class="text-sm">Severe Pain</span>
                        </button>
                        <button class="severity-option w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center" data-value="intense" data-color="pink-500">
                            <span class="h-3 w-3 rounded-full bg-pink-500 mr-2"></span> 
                            <span class="text-sm">Intense Discomfort</span>
                        </button>
                        <button class="severity-option w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center" data-value="excruciating" data-color="purple-500">
                            <span class="h-3 w-3 rounded-full bg-purple-500 mr-2"></span> 
                            <span class="text-sm">Excruciating Pain</span>
                        </button>
                        <button class="severity-option w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center" data-value="urgent" data-color="amber-500">
                            <span class="h-3 w-3 rounded-full bg-amber-500 mr-2"></span> 
                            <span class="text-sm">Urgent Concern</span>
                        </button>
                        <button class="severity-option w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center" data-value="emergency" data-color="rose-500">
                            <span class="h-3 w-3 rounded-full bg-rose-500 mr-2"></span> 
                            <span class="text-sm">Medical Emergency</span>
                        </button>
                        <button class="severity-option w-full text-left px-3 py-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center" data-value="critical" data-color="red-700">
                            <span class="h-3 w-3 rounded-full bg-red-700 mr-2"></span> 
                            <span class="text-sm">Critical Condition</span>
                        </button>
                    </div>
                </div>
            `;
        }
        
        // Function to play audio segment when clicking on highlighted text
        function playAudioSegment(element) {
            // In a real implementation, this would play the corresponding audio segment
            // Here we'll simulate playing with a visual indication
            element.style.backgroundColor = 'rgba(15, 111, 255, 0.3)';
            
            // Show a play indicator
            const playIndicator = document.createElement('div');
            playIndicator.className = 'fixed bottom-4 right-4 bg-primary text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center';
            playIndicator.innerHTML = '<i class="fas fa-play-circle mr-2"></i> Playing audio segment...';
            document.body.appendChild(playIndicator);
            
            // Remove the indicator after 3 seconds
            setTimeout(() => {
                element.style.backgroundColor = '';
                playIndicator.classList.add('animate-fade-out');
                setTimeout(() => {
                    document.body.removeChild(playIndicator);
                }, 500);
            }, 3000);
        }
        
        // Function to show/hide severity selection menu
        function toggleSeverityMenu(button) {
            const menu = document.getElementById('severityMenu');
            const rect = button.getBoundingClientRect();
            
            // Position the menu next to the button
            menu.style.top = (rect.top + window.scrollY) + 'px';
            menu.style.left = (rect.left + rect.width + 10 + window.scrollX) + 'px';
            
            // Toggle menu visibility
            if (menu.classList.contains('hidden')) {
                menu.classList.remove('hidden');
                
                // Save reference to current button
                menu.dataset.targetButton = button.dataset.issue;
                
                // Add click event listeners to severity options
                document.querySelectorAll('.severity-option').forEach(option => {
                    option.onclick = function() {
                        changeSeverity(button, this.dataset.value, this.dataset.color);
                        menu.classList.add('hidden');
                    };
                });
                
                // Close menu when clicking outside
                document.addEventListener('click', closeMenuOnClickOutside);
            } else {
                menu.classList.add('hidden');
                document.removeEventListener('click', closeMenuOnClickOutside);
            }
        }
        
        // Helper function to close menu when clicking outside
        function closeMenuOnClickOutside(event) {
            const menu = document.getElementById('severityMenu');
            if (!menu.contains(event.target) && !event.target.classList.contains('severity-indicator')) {
                menu.classList.add('hidden');
                document.removeEventListener('click', closeMenuOnClickOutside);
            }
        }
        
        // Function to change severity level
        function changeSeverity(button, severityValue, colorClass) {
            // Remove blinking effect from all buttons
            document.querySelectorAll('.blink-severe').forEach(el => {
                el.classList.remove('blink-severe');
            });
            
            // Update button appearance
            const oldColor = button.className.match(/bg-[a-z]+-[0-9]+/)[0];
            button.classList.remove(oldColor);
            button.classList.add(`bg-${colorClass}`);
            
            // Update card border color
            const card = button.closest('.relative');
            const oldBorderColor = card.querySelector('.border-t-4').className.match(/border-t-[a-z]+-[0-9]+/)[0];
            card.querySelector('.border-t-4').classList.remove(oldBorderColor);
            card.querySelector('.border-t-4').classList.add(`border-t-${colorClass}`);
            
            // Update severity text
            const severityText = card.querySelector('[class*="text-xs font-medium uppercase"]');
            const oldTextColor = severityText.className.match(/text-[a-z]+-[0-9]+/)[0];
            severityText.classList.remove(oldTextColor);
            severityText.classList.add(`text-${colorClass.replace('500', '600')}`);
            
            // Get severity display name from the clicked option
            const severityDisplayName = Array.from(document.querySelectorAll('.severity-option'))
                .find(option => option.dataset.value === severityValue)
                .querySelector('span:last-child').textContent;
                
            // Update the severity text
            severityText.textContent = severityDisplayName;
            
            // Update the data attribute
            button.dataset.severity = severityValue;
            
            // Reorder cards by severity
            reorderCardsBySeverity();
            
            // Make most severe button blink
            const allButtons = document.querySelectorAll('.severity-indicator');
            const severityLevels = {
                'mild': 1,
                'occasional': 2,
                'moderate': 3,
                'persistent': 4,
                'severe': 5,
                'intense': 6,
                'excruciating': 7,
                'urgent': 8,
                'emergency': 9,
                'critical': 10
            };
            
            // Find the button with highest severity
            let highestSeverity = 0;
            let mostSevereButton = null;
            allButtons.forEach(btn => {
                const severityLevel = severityLevels[btn.dataset.severity] || 0;
                if (severityLevel > highestSeverity) {
                    highestSeverity = severityLevel;
                    mostSevereButton = btn;
                }
            });
            
            // Add blinking effect to the most severe button
            if (mostSevereButton) {
                mostSevereButton.classList.add('blink-severe');
            }
            
            // Show success notification
            showNotification(`Updated ${button.dataset.issue} to ${severityDisplayName}`);
        }
        
        function toggleCardDetails(button) {
            const card = button.closest('.relative');
            const details = card.querySelector('.card-details');
            const icon = button.querySelector('i');
            
            if (details.classList.contains('hidden')) {
                details.classList.remove('hidden');
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            } else {
                details.classList.add('hidden');
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            }
            
            // Prevent card hover from showing details
            event.stopPropagation();
        }
        
        function highlightTranscriptionSegment(topic) {
            // First, reset any previous highlights
            document.querySelectorAll('#transcriptionText [data-topic]').forEach(element => {
                element.classList.remove('bg-green-300', 'dark:bg-green-700/50', 'text-lg');
                element.style.fontSize = '';
            });
            
            // Find all elements with matching topic and highlight them
            const transcriptionTab = document.getElementById('transcriptionTab');
            if (transcriptionTab) {
                // Make sure transcription tab is visible
                showTab('transcriptionTab');
                
                // Find and highlight matching elements
                const elements = document.querySelectorAll(`#transcriptionText [data-topic*="${topic}"]`);
                elements.forEach(element => {
                    element.classList.add('bg-green-300', 'dark:bg-green-700/50');
                    
                    // Increase font size by 2px
                    const currentSize = window.getComputedStyle(element).fontSize;
                    const currentSizeValue = parseFloat(currentSize);
                    element.style.fontSize = (currentSizeValue + 2) + 'px';
                    
                    // Scroll to first match if needed
                    if (element === elements[0]) {
                        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                });
                
                // Show success notification
                showNotification(`Highlighted ${elements.length} mentions of "${topic}"`);
            }
        }
        
        function reorderCardsBySeverity() {
            const container = document.querySelector('.space-y-3');
            const cards = Array.from(container.querySelectorAll('.relative'));
            
            const severityLevels = {
                'mild': 1,
                'occasional': 2,
                'moderate': 3,
                'persistent': 4,
                'severe': 5,
                'intense': 6,
                'excruciating': 7,
                'urgent': 8,
                'emergency': 9,
                'critical': 10
            };
            
            // Sort cards by severity (highest first)
            cards.sort((a, b) => {
                const aButton = a.querySelector('.severity-indicator');
                const bButton = b.querySelector('.severity-indicator');
                const aSeverity = severityLevels[aButton.dataset.severity] || 0;
                const bSeverity = severityLevels[bButton.dataset.severity] || 0;
                return bSeverity - aSeverity;
            });
            
            // Update button numbers based on new order
            cards.forEach((card, index) => {
                const button = card.querySelector('.severity-indicator');
                button.textContent = (index + 1).toString();
            });
            
            // Remove all cards from container
            cards.forEach(card => card.remove());
            
            // Add cards back in sorted order
            cards.forEach(card => {
                container.appendChild(card);
            });
        }
        
        // Function to show a notification
        function showNotification(message, type = 'success') {
            const notification = document.createElement('div');
            let className = 'fixed bottom-4 left-4 px-4 py-2 rounded-lg shadow-lg z-50 flex items-center transform transition-all duration-300 translate-y-0 opacity-100';
            let icon = 'check-circle';
            
            // Apply styles based on notification type
            switch(type) {
                case 'success':
                    className += ' bg-green-500 text-white';
                    icon = 'check-circle';
                    break;
                case 'error':
                    className += ' bg-red-500 text-white';
                    icon = 'exclamation-circle';
                    break;
                case 'info':
                    className += ' bg-blue-500 text-white';
                    icon = 'info-circle';
                    break;
                default:
                    className += ' bg-green-500 text-white';
                    icon = 'check-circle';
            }
            
            notification.className = className;
            notification.innerHTML = `<i class="fas fa-${icon} mr-2"></i> ${message}`;
            document.body.appendChild(notification);
            
            // Remove the notification after 5 seconds
            console.log(`Showing notification: ${message} (${type})`);
            setTimeout(() => {
                notification.classList.add('translate-y-10', 'opacity-0');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        }
        
        // Function to generate downloadable report
        function generateReport(downloadHTML = true) {
            console.log('Generating report, downloadHTML =', downloadHTML);
            
            // Get selected options
            const includeTranscript = document.getElementById('includeTranscript')?.checked ?? true;
            const includeSummary = document.getElementById('includeSummary')?.checked ?? true;
            const includeAnalysis = document.getElementById('includeAnalysis')?.checked ?? true;
            const includeNotes = document.getElementById('includeNotes')?.checked ?? true;
            const includeFollowUp = document.getElementById('includeFollowUp')?.checked ?? true;
            
            // Get content
            let content = `
                <html>
                <head>
                    <title>Wellora Medical Analysis Report</title>
                    <style>
                        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@400;500;600;700&display=swap');
                        
                        :root {
                            --primary-color: #0F6FFF;
                            --secondary-color: #00B0B6;
                            --tertiary-color: #00D695;
                            --accent-color: #8B5CF6;
                            --warning-color: #F59E0B;
                            --success-color: #10B981;
                            --danger-color: #EF4444;
                            --text-dark: #1F2937;
                            --text-medium: #4B5563;
                            --text-light: #6B7280;
                            --bg-light: #F9FAFB;
                            --border-light: #E5E7EB;
                            --shadow-color: rgba(0, 0, 0, 0.05);
                        }
                        
                        * {
                            box-sizing: border-box;
                            margin: 0;
                            padding: 0;
                        }
                        
                        body {
                            font-family: 'Inter', sans-serif;
                            line-height: 1.6;
                            max-width: 100%;
                            width: 210mm; /* A4 width */
                            margin: 0 auto;
                            padding: 20px;
                            background-color: white;
                            color: var(--text-dark);
                        }
                        
                        .report-container {
                            display: flex;
                            flex-direction: column;
                            gap: 24px;
                        }
                        
                        .header {
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            margin-bottom: 32px;
                            position: relative;
                        }
                        
                        .header:after {
                            content: '';
                            position: absolute;
                            bottom: -16px;
                            left: 0;
                            right: 0;
                            height: 4px;
                            background: linear-gradient(to right, var(--primary-color), var(--tertiary-color));
                            border-radius: 2px;
                        }
                        
                        .logo-container {
                            display: flex;
                            align-items: center;
                            margin-bottom: 12px;
                        }
                        
                        .logo {
                            font-family: 'Montserrat', sans-serif;
                            font-weight: 700;
                            color: var(--primary-color);
                            font-size: 28px;
                            letter-spacing: -0.5px;
                            margin-left: 8px;
                        }
                        
                        .report-title {
                            font-family: 'Montserrat', sans-serif;
                            font-size: 24px;
                            font-weight: 600;
                            color: var(--text-dark);
                            margin-bottom: 6px;
                            letter-spacing: -0.5px;
                        }
                        
                        .report-subtitle {
                            font-size: 14px;
                            color: var(--text-medium);
                        }
                        
                        .meta-info {
                            background-color: var(--bg-light);
                            border-radius: 10px;
                            padding: 16px;
                            display: flex;
                            justify-content: space-between;
                            margin-bottom: 24px;
                            box-shadow: 0 2px 8px var(--shadow-color);
                        }
                        
                        .meta-info-group {
                            display: flex;
                            flex-direction: column;
                            gap: 8px;
                        }
                        
                        .meta-info-item {
                            display: flex;
                            align-items: center;
                            gap: 6px;
                        }
                        
                        .meta-info-label {
                            font-size: 12px;
                            font-weight: 500;
                            color: var(--text-medium);
                        }
                        
                        .meta-info-value {
                            font-size: 14px;
                            font-weight: 600;
                            color: var(--text-dark);
                        }
                        
                        .section {
                            margin-bottom: 24px;
                            padding: 0;
                            position: relative;
                        }
                        
                        .section-header {
                            display: flex;
                            align-items: center;
                            margin-bottom: 16px;
                            gap: 12px;
                        }
                        
                        .section-icon {
                            width: 32px;
                            height: 32px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            border-radius: 50%;
                            color: white;
                        }
                        
                        .section-title {
                            font-family: 'Montserrat', sans-serif;
                            font-size: 18px;
                            font-weight: 600;
                            color: var(--text-dark);
                            flex: 1;
                            letter-spacing: -0.3px;
                        }
                        
                        .section-content {
                            background-color: white;
                            border-radius: 10px;
                            overflow: hidden;
                            box-shadow: 0 2px 10px var(--shadow-color);
                        }
                        
                        .transcript-section .section-icon {
                            background-color: var(--primary-color);
                        }
                        
                        .summary-section .section-icon {
                            background-color: var(--secondary-color);
                        }
                        
                        .analysis-section .section-icon {
                            background-color: var(--accent-color);
                        }
                        
                        .notes-section .section-icon {
                            background-color: var(--warning-color);
                        }
                        
                        .followup-section .section-icon {
                            background-color: var(--success-color);
                        }
                        
                        .transcript-section .section-content {
                            border-top: 4px solid var(--primary-color);
                        }
                        
                        .summary-section .section-content {
                            border-top: 4px solid var(--secondary-color);
                        }
                        
                        .analysis-section .section-content {
                            border-top: 4px solid var(--accent-color);
                        }
                        
                        .notes-section .section-content {
                            border-top: 4px solid var(--warning-color);
                        }
                        
                        .followup-section .section-content {
                            border-top: 4px solid var(--success-color);
                        }
                        
                        .content-padding {
                            padding: 20px;
                        }
                        
                        .card {
                            background-color: white;
                            border-radius: 8px;
                            border: 1px solid var(--border-light);
                            margin-bottom: 16px;
                            overflow: hidden;
                        }
                        
                        .card-header {
                            padding: 12px 16px;
                            border-bottom: 1px solid var(--border-light);
                            background-color: var(--bg-light);
                            display: flex;
                            align-items: center;
                            gap: 8px;
                        }
                        
                        .card-title {
                            font-weight: 600;
                            font-size: 16px;
                            color: var(--text-dark);
                        }
                        
                        .card-body {
                            padding: 16px;
                        }
                        
                        .card-footer {
                            padding: 12px 16px;
                            border-top: 1px solid var(--border-light);
                            background-color: var(--bg-light);
                            font-size: 13px;
                            color: var(--text-light);
                        }
                        
                        .transcript-speaker {
                            font-weight: 600;
                            color: var(--primary-color);
                            margin-right: 8px;
                            display: inline-block;
                            width: 80px;
                        }
                        
                        .transcript-line {
                            margin-bottom: 10px;
                        }
                        
                        .highlight {
                            background-color: rgba(245, 158, 11, 0.2);
                            padding: 0 3px;
                            border-radius: 2px;
                        }
                        
                        .chart-wrapper {
                            margin-top: 16px;
                            margin-bottom: 16px;
                            padding: 16px;
                            background-color: var(--bg-light);
                            border-radius: 8px;
                        }
                        
                        .chart-title {
                            font-weight: 600;
                            margin-bottom: 16px;
                            font-size: 15px;
                            color: var(--text-medium);
                            text-align: center;
                        }
                        
                        .footer {
                            margin-top: 40px;
                            border-top: 1px solid var(--border-light);
                            padding-top: 16px;
                            display: flex;
                            justify-content: space-between;
                            font-size: 12px;
                            color: var(--text-light);
                        }
                        
                        .footer-logo {
                            font-weight: 600;
                            color: var(--primary-color);
                        }
                        
                        @media print {
                            body {
                                width: 100%;
                                max-width: 100%;
                                padding: 0;
                                margin: 0;
                            }
                            
                            .report-container {
                                padding: 20px;
                            }
                            
                            .card, .section-content {
                                break-inside: avoid;
                                page-break-inside: avoid;
                            }
                            
                            .section {
                                page-break-inside: avoid;
                                break-inside: avoid;
                            }
                        }
                        
                        /* Progress indicators */
                        .improvement-indicator {
                            display: inline-flex;
                            align-items: center;
                            padding: 3px 8px;
                            border-radius: 12px;
                            font-size: 12px;
                            font-weight: 600;
                            gap: 4px;
                        }
                        
                        .improved {
                            background-color: rgba(16, 185, 129, 0.1);
                            color: #10B981;
                        }
                        
                        .declined {
                            background-color: rgba(239, 68, 68, 0.1);
                            color: #EF4444;
                        }
                        
                        /* Time indicators */
                        .timestamp {
                            color: var(--text-light);
                            font-size: 12px;
                            margin-left: 8px;
                        }
                    </style>
                </head>
                <body>
                    <div class="report-container">
                        <div class="header">
                            <div class="logo-container">
                                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M16 32C24.8366 32 32 24.8366 32 16C32 7.16344 24.8366 0 16 0C7.16344 0 0 7.16344 0 16C0 24.8366 7.16344 32 16 32Z" fill="#0F6FFF"/>
                                    <path d="M20.5 11.5C20.5 14.2614 18.2614 16.5 15.5 16.5C12.7386 16.5 10.5 14.2614 10.5 11.5C10.5 8.73858 12.7386 6.5 15.5 6.5C18.2614 6.5 20.5 8.73858 20.5 11.5Z" stroke="white" stroke-width="2"/>
                                    <path d="M23.5 22.5C23.5 24.1569 19.9706 25.5 15.5 25.5C11.0294 25.5 7.5 24.1569 7.5 22.5C7.5 20.8431 11.0294 19.5 15.5 19.5C19.9706 19.5 23.5 20.8431 23.5 22.5Z" stroke="white" stroke-width="2"/>
                                    <path d="M23 15.5H27" stroke="white" stroke-width="2" stroke-linecap="round"/>
                                    <path d="M22.5 11.5L25.5 9.5" stroke="white" stroke-width="2" stroke-linecap="round"/>
                                    <path d="M22.5 19.5L25.5 21.5" stroke="white" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                                <div class="logo">Wellora</div>
                            </div>
                            <h1 class="report-title">Medical Analysis Report</h1>
                            <p class="report-subtitle">Generated on ${new Date().toLocaleDateString("en-US", { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</p>
                        </div>
            `;
            
            // Add recording details
            const recordingTitle = document.getElementById('analysisRecordingTitle').textContent;
            const clientNameEl = document.querySelector('.text-xl.font-bold.text-gray-800') || {};
            const clientName = clientNameEl.textContent || 'Patient';
            
            content += `
                <div class="meta-info">
                    <div class="meta-info-group">
                        <div class="meta-info-item">
                            <span class="meta-info-label">Patient:</span>
                            <span class="meta-info-value">${clientName}</span>
                        </div>
                        <div class="meta-info-item">
                            <span class="meta-info-label">Recording:</span>
                            <span class="meta-info-value">${recordingTitle}</span>
                        </div>
                    </div>
                    <div class="meta-info-group">
                        <div class="meta-info-item">
                            <span class="meta-info-label">Date:</span>
                            <span class="meta-info-value">${new Date().toLocaleDateString()}</span>
                        </div>
                        <div class="meta-info-item">
                            <span class="meta-info-label">Report ID:</span>
                            <span class="meta-info-value">WLR-${Date.now().toString().substr(-6)}</span>
                        </div>
                    </div>
                </div>
            `;
            
            // Add sections based on options
            if (includeTranscript) {
                const transcriptionText = document.getElementById('transcriptionText').innerHTML
                    .replace(/<span class="highlight-search audio-segment"[^>]*>(.*?)<\/span>/gi, '$1');
                content += `
                    <div class="section transcript-section">
                        <div class="section-header">
                            <div class="section-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <h2 class="section-title">Full Transcript</h2>
                        </div>
                        <div class="section-content">
                            <div class="content-padding">
                                ${transcriptionText.replace(/<div class="flex">/g, '<div class="transcript-line">')
                                  .replace(/<div class="w-24 flex-shrink-0[^>]*>(.*?)<\/div>/g, '<span class="transcript-speaker">$1</span>')
                                  .replace(/<span class="bg-yellow-100[^>]*>(.*?)<\/span>/g, '<span class="highlight">$1</span>')}
                            </div>
                        </div>
                    </div>
                `;
            }
            
            if (includeSummary) {
                const summaryText = document.getElementById('transcriptionSummary').innerHTML;
                content += `
                    <div class="section summary-section">
                        <div class="section-header">
                            <div class="section-icon">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <h2 class="section-title">Session Summary</h2>
                        </div>
                        <div class="section-content">
                            <div class="content-padding">
                                ${summaryText.replace(/div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-t-4([^>]*)>/g, 
                                   'div class="card">')}
                            </div>
                        </div>
                    </div>
                `;
            }
            
            if (includeAnalysis) {
                const analysisText = document.getElementById('analysisResults').innerHTML;
                content += `
                    <div class="section analysis-section">
                        <div class="section-header">
                            <div class="section-icon">
                                <i class="fas fa-brain"></i>
                            </div>
                            <h2 class="section-title">AI Analysis</h2>
                        </div>
                        <div class="section-content">
                            <div class="content-padding">
                                <!-- Health Concerns Card -->
                                <div class="card">
                                    <div class="card-header">
                                        <i class="fas fa-heartbeat text-red-500 mr-2"></i>
                                        <span class="card-title">Health Concerns Detected</span>
                                    </div>
                                    <div class="card-body">
                                        <div class="space-y-4">
                                            <div>
                                                <h5 class="font-medium text-gray-800 mb-1">Headaches</h5>
                                                <div class="flex items-center mb-2">
                                                    <span class="improvement-indicator improved">
                                                        <i class="fas fa-arrow-down"></i> 75% Improvement
                                                    </span>
                                                </div>
                                                <p class="text-sm text-gray-600">Recurring symptom, mentioned as improving but still present. Patient reports decreased frequency from daily to once per week.</p>
                                            </div>
                                            <div>
                                                <h5 class="font-medium text-gray-800 mb-1">Anxiety</h5>
                                                <div class="flex items-center mb-2">
                                                    <span class="improvement-indicator declined">
                                                        <i class="fas fa-arrow-up"></i> 25% Increase
                                                    </span>
                                                </div>
                                                <p class="text-sm text-gray-600">Persistent issue affecting daily functioning, especially in workspace. Recent work presentation caused spike in symptoms.</p>
                                            </div>
                                            <div>
                                                <h5 class="font-medium text-gray-800 mb-1">Sleep Quality</h5>
                                                <div class="flex items-center mb-2">
                                                    <span class="improvement-indicator improved">
                                                        <i class="fas fa-arrow-up"></i> 133% Improvement
                                                    </span>
                                                </div>
                                                <p class="text-sm text-gray-600">Sleep quality has improved with medication and new bedtime routine. Duration increased from 5 to 7+ hours per night.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Symptom Progression Chart -->
                                <div class="chart-wrapper">
                                    <div class="chart-title">Symptom Progression Timeline</div>
                                    <div style="height: 250px; position: relative;">
                                        <!-- Simplified static visualization -->
                                        <svg width="100%" height="200" viewBox="0 0 700 200" xmlns="http://www.w3.org/2000/svg">
                                            <!-- Axes -->
                                            <line x1="50" y1="170" x2="650" y2="170" stroke="#ccc" stroke-width="1"/>
                                            <line x1="50" y1="10" x2="50" y2="170" stroke="#ccc" stroke-width="1"/>
                                            
                                            <!-- X-axis labels -->
                                            <text x="100" y="190" font-size="12" text-anchor="middle">Jan 5</text>
                                            <text x="200" y="190" font-size="12" text-anchor="middle">Jan 19</text>
                                            <text x="300" y="190" font-size="12" text-anchor="middle">Feb 2</text>
                                            <text x="400" y="190" font-size="12" text-anchor="middle">Feb 16</text>
                                            <text x="500" y="190" font-size="12" text-anchor="middle">Mar 1</text>
                                            <text x="600" y="190" font-size="12" text-anchor="middle">Mar 15</text>
                                            
                                            <!-- Y-axis labels -->
                                            <text x="40" y="170" font-size="12" text-anchor="end">0</text>
                                            <text x="40" y="130" font-size="12" text-anchor="end">2.5</text>
                                            <text x="40" y="90" font-size="12" text-anchor="end">5</text>
                                            <text x="40" y="50" font-size="12" text-anchor="end">7.5</text>
                                            <text x="40" y="10" font-size="12" text-anchor="end">10</text>
                                            
                                            <!-- Headaches line (blue) -->
                                            <polyline points="100,90 200,70 300,50 400,60 500,40 600,30" 
                                                    stroke="#3B82F6" stroke-width="2" fill="none"/>
                                                    
                                            <!-- Dots for Headaches -->
                                            <circle cx="100" cy="90" r="5" fill="#3B82F6"/>
                                            <circle cx="200" cy="70" r="5" fill="#3B82F6"/>
                                            <circle cx="300" cy="50" r="5" fill="#3B82F6"/>
                                            <circle cx="400" cy="60" r="5" fill="#3B82F6"/>
                                            <circle cx="500" cy="40" r="5" fill="#3B82F6"/>
                                            <circle cx="600" cy="30" r="5" fill="#3B82F6"/>
                                            
                                            <!-- Anxiety line (purple) -->
                                            <polyline points="100,60 200,70 300,60 400,80 500,70 600,80" 
                                                    stroke="#8B5CF6" stroke-width="2" fill="none"/>
                                                    
                                            <!-- Dots for Anxiety -->
                                            <circle cx="100" cy="60" r="5" fill="#8B5CF6"/>
                                            <circle cx="200" cy="70" r="5" fill="#8B5CF6"/>
                                            <circle cx="300" cy="60" r="5" fill="#8B5CF6"/>
                                            <circle cx="400" cy="80" r="5" fill="#8B5CF6"/>
                                            <circle cx="500" cy="70" r="5" fill="#8B5CF6"/>
                                            <circle cx="600" cy="80" r="5" fill="#8B5CF6"/>
                                            
                                            <!-- Sleep Quality line (green) -->
                                            <polyline points="100,140 200,130 300,120 400,120 500,110 600,110" 
                                                    stroke="#10B981" stroke-width="2" fill="none"/>
                                                    
                                            <!-- Dots for Sleep Quality -->
                                            <circle cx="100" cy="140" r="5" fill="#10B981"/>
                                            <circle cx="200" cy="130" r="5" fill="#10B981"/>
                                            <circle cx="300" cy="120" r="5" fill="#10B981"/>
                                            <circle cx="400" cy="120" r="5" fill="#10B981"/>
                                            <circle cx="500" cy="110" r="5" fill="#10B981"/>
                                            <circle cx="600" cy="110" r="5" fill="#10B981"/>

                                            <!-- Legend -->
                                            <rect x="50" y="5" width="10" height="10" fill="#3B82F6"/>
                                            <text x="65" y="15" font-size="12">Headaches</text>
                                            
                                            <rect x="150" y="5" width="10" height="10" fill="#8B5CF6"/>
                                            <text x="165" y="15" font-size="12">Anxiety</text>
                                            
                                            <rect x="250" y="5" width="10" height="10" fill="#10B981"/>
                                            <text x="265" y="15" font-size="12">Sleep Quality</text>
                                        </svg>
                                    </div>
                                </div>
                                
                                <!-- Medications Mentioned -->
                                <div class="card">
                                    <div class="card-header">
                                        <i class="fas fa-pills text-blue-500 mr-2"></i>
                                        <span class="card-title">Medications Mentioned</span>
                                    </div>
                                    <div class="card-body">
                                        <h5 class="font-medium text-gray-800 mb-2">Amitriptyline</h5>
                                        <p class="text-sm text-gray-600 mb-2">Tricyclic antidepressant, being used for headache prevention and sleep improvement</p>
                                        <p class="text-xs text-gray-500">Common dosage: 10-50mg daily, typically taken at bedtime</p>
                                    </div>
                                    <div class="card-footer">
                                        Reported effectiveness: High for sleep, Moderate for headaches
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            if (includeNotes) {
                const notesContainer = document.getElementById('savedCaretakerNotes');
                let notesContent = '<p>No notes available.</p>';
                if (notesContainer && notesContainer.children.length > 0 && !notesContainer.textContent.includes('No notes have been added')) {
                    notesContent = notesContainer.innerHTML;
                }
                content += `
                    <div class="section notes-section">
                        <div class="section-header">
                            <div class="section-icon">
                                <i class="fas fa-sticky-note"></i>
                            </div>
                            <h2 class="section-title">Caretaker Notes</h2>
                        </div>
                        <div class="section-content">
                            <div class="content-padding">
                                ${notesContent.replace(/class="bg-blue-50[^"]*"/g, 'class="card card-body mb-3"')}
                            </div>
                        </div>
                    </div>
                `;
            }
            
            if (includeFollowUp) {
                const tasksContainer = document.getElementById('followUpTasksList');
                let tasksContent = '<p>No follow-up tasks available.</p>';
                if (tasksContainer && tasksContainer.children.length > 0 && !tasksContainer.textContent.includes('No tasks have been added')) {
                    tasksContent = tasksContainer.innerHTML;
                }
                content += `
                    <div class="section followup-section">
                        <div class="section-header">
                            <div class="section-icon">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <h2 class="section-title">Follow-up Tasks</h2>
                        </div>
                        <div class="section-content">
                            <div class="content-padding">
                                <div class="card">
                                    <div class="card-header">
                                        <i class="fas fa-clipboard-list text-green-500 mr-2"></i>
                                        <span class="card-title">Recommended Follow-up Actions</span>
                                    </div>
                                    <div class="card-body">
                                        ${tasksContent}
                                    </div>
                                    <div class="card-footer">
                                        Next follow-up appointment: ${new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toLocaleDateString()}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            content += `
                    <div class="footer">
                        <span>© ${new Date().getFullYear()} <span class="footer-logo">Wellora</span> Health Technologies</span>
                        <span>Report ID: WLR-${Date.now().toString().substr(-6)}</span>
                    </div>
                </div>
                </body>
                </html>
            `;
            
            // If downloadHTML is true, download as HTML file, otherwise return content for PDF conversion
            if (downloadHTML) {
                // Create a blob and download the file
                const blob = new Blob([content], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${recordingTitle.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_report.html`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'fixed top-4 right-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4 rounded shadow-lg z-50';
                successMessage.innerHTML = `
                    <div class="flex items-center">
                        <div class="mr-3">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div>
                            <p class="font-medium">Report generated successfully!</p>
                        </div>
                    </div>
                `;
                document.body.appendChild(successMessage);
                
                // Remove the success message after 3 seconds
                setTimeout(() => {
                    document.body.removeChild(successMessage);
                }, 3000);
            } else {
                // Return the content for PDF conversion
                return content;
            }
        }
        
        // Function to initialize the symptom progression chart
        function initSymptomProgressionChart() {
            const ctx = document.getElementById('symptomProgressionChart');
            if (!ctx) return;
            
            // Define chart data
            const chartData = {
                labels: ['Jan 5', 'Jan 19', 'Feb 2', 'Feb 16', 'Mar 1', 'Mar 15', 'Current'],
                datasets: [
                    {
                        label: 'Headaches',
                        data: [8, 7, 5, 6, 4, 3, 2],
                        borderColor: 'rgba(59, 130, 246, 0.8)',
                        backgroundColor: 'rgba(59, 130, 246, 0.3)',
                        borderWidth: 3,
                        tension: 0,
                        fill: false,
                        stepped: 'before',
                        pointRadius: 0,
                        pointHoverRadius: 5,
                        pointHitRadius: 10,
                        pointHoverBackgroundColor: 'white',
                        pointHoverBorderWidth: 2,
                        pointHoverBorderColor: 'rgba(59, 130, 246, 1)',
                        pointStyle: 'circle'
                    },
                    {
                        label: 'Anxiety',
                        data: [6, 7, 6, 8, 7, 8, 6],
                        borderColor: 'rgba(147, 51, 234, 0.8)',
                        backgroundColor: 'rgba(147, 51, 234, 0.3)',
                        borderWidth: 3,
                        tension: 0,
                        fill: false,
                        stepped: 'before',
                        pointRadius: 0,
                        pointHoverRadius: 5,
                        pointHitRadius: 10,
                        pointHoverBackgroundColor: 'white',
                        pointHoverBorderWidth: 2,
                        pointHoverBorderColor: 'rgba(147, 51, 234, 1)',
                        pointStyle: 'circle'
                    },
                    {
                        label: 'Sleep Quality',
                        data: [3, 4, 5, 5, 6, 6, 7],
                        borderColor: 'rgba(16, 185, 129, 0.8)',
                        backgroundColor: 'rgba(16, 185, 129, 0.3)',
                        borderWidth: 3,
                        tension: 0,
                        fill: false,
                        stepped: 'before',
                        pointRadius: 0,
                        pointHoverRadius: 5,
                        pointHitRadius: 10,
                        pointHoverBackgroundColor: 'white',
                        pointHoverBorderWidth: 2,
                        pointHoverBorderColor: 'rgba(16, 185, 129, 1)',
                        pointStyle: 'circle'
                    },
                    {
                        label: 'Dizziness',
                        data: [9, 8, 8, 6, 7, 4, 2],
                        borderColor: 'rgba(245, 158, 11, 0.8)',
                        backgroundColor: 'rgba(245, 158, 11, 0.3)',
                        borderWidth: 3,
                        tension: 0,
                        fill: false,
                        stepped: 'before',
                        pointRadius: 0,
                        pointHoverRadius: 5,
                        pointHitRadius: 10,
                        pointHoverBackgroundColor: 'white',
                        pointHoverBorderWidth: 2,
                        pointHoverBorderColor: 'rgba(245, 158, 11, 1)',
                        pointStyle: 'circle'
                    },
                    {
                        label: 'Fatigue',
                        data: [8, 6, 5, 7, 8, 5, 3],
                        borderColor: 'rgba(236, 72, 153, 0.8)',
                        backgroundColor: 'rgba(236, 72, 153, 0.3)',
                        borderWidth: 3,
                        tension: 0,
                        fill: false,
                        stepped: 'before',
                        pointRadius: 0,
                        pointHoverRadius: 5,
                        pointHitRadius: 10,
                        pointHoverBackgroundColor: 'white',
                        pointHoverBorderWidth: 2,
                        pointHoverBorderColor: 'rgba(236, 72, 153, 1)',
                        pointStyle: 'circle'
                    },
                    {
                        label: 'Pain Management',
                        data: [7, 5, 6, 4, 3, 4, 2],
                        borderColor: 'rgba(6, 182, 212, 0.8)',
                        backgroundColor: 'rgba(6, 182, 212, 0.3)',
                        borderWidth: 3,
                        tension: 0,
                        fill: false,
                        stepped: 'before',
                        pointRadius: 0,
                        pointHoverRadius: 5,
                        pointHitRadius: 10,
                        pointHoverBackgroundColor: 'white',
                        pointHoverBorderWidth: 2,
                        pointHoverBorderColor: 'rgba(6, 182, 212, 1)',
                        pointStyle: 'circle'
                    }
                ]
            };
            
            // Add special points
            // For headaches (downward progression = improvement in symptoms)
            addSpecialPoint(chartData.datasets[0], 2, 'blue', 'Headache frequency decreased after starting medication');
            addSpecialPoint(chartData.datasets[0], 5, 'blue', 'Headaches down to once per week from daily occurrences');
            
            // For anxiety (upward = worse)
            addSpecialPoint(chartData.datasets[1], 3, 'red', 'Reported increased anxiety due to work presentation');
            addSpecialPoint(chartData.datasets[1], 5, 'red', 'Still experiencing workplace anxiety despite breathing exercises');
            
            // For sleep (upward = better)
            addSpecialPoint(chartData.datasets[2], 2, 'blue', 'Sleep duration increased from 5h to 6.5h per night');
            addSpecialPoint(chartData.datasets[2], 6, 'blue', 'Now sleeping 7+ hours consistently with fewer interruptions');
            
            // For dizziness (downward = improvement)
            addSpecialPoint(chartData.datasets[3], 3, 'blue', 'Dizziness improved after medication adjustment');
            addSpecialPoint(chartData.datasets[3], 4, 'red', 'Reported dizzy spell after skipping medication for 2 days');
            addSpecialPoint(chartData.datasets[3], 5, 'blue', 'Significant improvement in dizziness after consistent medication use');
            
            // For fatigue (downward = improvement)
            addSpecialPoint(chartData.datasets[4], 1, 'blue', 'Energy levels improved after starting B12 supplements');
            addSpecialPoint(chartData.datasets[4], 3, 'red', 'Fatigue worsened due to poor sleep after work stress');
            addSpecialPoint(chartData.datasets[4], 5, 'blue', 'Fatigue reduced after implementing better sleep routine');
            
            // For pain management (downward = improvement)
            addSpecialPoint(chartData.datasets[5], 1, 'blue', 'Pain reduced after starting physical therapy');
            addSpecialPoint(chartData.datasets[5], 2, 'red', 'Temporary pain increase after overexertion');
            addSpecialPoint(chartData.datasets[5], 6, 'blue', 'Pain significantly reduced with consistent exercise routine');
            
            // Create chart
            const chart = new Chart(ctx, {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            min: 0,
                            max: 10,
                            grid: {
                                color: 'rgba(200, 200, 200, 0.2)'
                            },
                            title: {
                                display: true,
                                text: 'Severity (0-10)'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: true,
                            callbacks: {
                                title: function(tooltipItems) {
                                    return tooltipItems[0].label;
                                },
                                label: function(tooltipItem) {
                                    let datasetLabel = chartData.datasets[tooltipItem.datasetIndex].label;
                                    let value = tooltipItem.raw;
                                    
                                    // Get note if exists
                                    let note = '';
                                    const specialPoints = chartData.datasets[tooltipItem.datasetIndex].specialPoints;
                                    if (specialPoints && specialPoints[tooltipItem.dataIndex]) {
                                        note = specialPoints[tooltipItem.dataIndex].note;
                                    }
                                    
                                    if (note) {
                                        return [`${datasetLabel}: ${value}/10`, `Note: ${note}`];
                                    } else {
                                        return `${datasetLabel}: ${value}/10`;
                                    }
                                }
                            }
                        }
                    }
                }
            });
            
            // Create overlay info elements for special points
            createSpecialPointsOverlay(ctx, chartData, chart);
            
            // Setup interactive legend and data point details
            setupInteractiveElements(chartData, chart);
        }
        
        // Setup the interactive elements for the enhanced chart
        function setupInteractiveElements(chartData, chart) {
            // Handle legend item clicks to toggle datasets
            const legendItems = document.querySelectorAll('.symptom-legend');
            legendItems.forEach((item, index) => {
                item.addEventListener('click', function() {
                    // Toggle dataset visibility
                    const meta = chart.getDatasetMeta(index);
                    meta.hidden = !meta.hidden;
                    
                    // Update the legend item appearance
                    if (meta.hidden) {
                        item.classList.add('opacity-50');
                    } else {
                        item.classList.remove('opacity-50');
                    }
                    
                    // Update the chart
                    chart.update();
                });
            });
            
            // Setup view toggle button
            const toggleViewBtn = document.getElementById('toggleChartViewBtn');
            const viewModeText = document.getElementById('viewModeText');
            let isLineView = true;
            
            if (toggleViewBtn) {
                toggleViewBtn.addEventListener('click', function() {
                    isLineView = !isLineView;
                    
                    // Update all datasets
                    chartData.datasets.forEach((dataset, i) => {
                        // Toggle between line and bar charts
                        if (isLineView) {
                            dataset.stepped = 'before';
                            viewModeText.textContent = 'Line View';
                        } else {
                            dataset.stepped = false;
                            viewModeText.textContent = 'Point View';
                        }
                    });
                    
                    // Update the chart
                    chart.update();
                });
            }
            
            // Setup notes toggle button
            const toggleNotesBtn = document.getElementById('toggleNotesBtn');
            let showingAllNotes = false;
            
            if (toggleNotesBtn) {
                toggleNotesBtn.addEventListener('click', function() {
                    showingAllNotes = !showingAllNotes;
                    
                    // Show/hide all notes
                    if (showingAllNotes) {
                        toggleNotesBtn.innerHTML = '<i class="fas fa-sticky-note mr-1"></i> Hide Notes';
                        showAllDataPointNotes(chartData);
                    } else {
                        toggleNotesBtn.innerHTML = '<i class="fas fa-sticky-note mr-1"></i> Show All Notes';
                        hideAllDataPointNotes();
                    }
                });
            }
            
            // Setup collapse legends button
            const collapseLegendsBtn = document.getElementById('collapseLegendsBtn');
            const symptomLegends = document.getElementById('symptomLegends');
            let legendsCollapsed = false;
            
            if (collapseLegendsBtn && symptomLegends) {
                collapseLegendsBtn.addEventListener('click', function() {
                    legendsCollapsed = !legendsCollapsed;
                    
                    if (legendsCollapsed) {
                        symptomLegends.classList.add('hidden');
                        collapseLegendsBtn.innerHTML = '<i class="fas fa-chevron-down"></i>';
                    } else {
                        symptomLegends.classList.remove('hidden');
                        collapseLegendsBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
                    }
                });
            }
            
            // Setup close data point details button
            const closeDataPointBtn = document.getElementById('closeDataPointBtn');
            const dataPointDetails = document.getElementById('dataPointDetails');
            
            if (closeDataPointBtn && dataPointDetails) {
                closeDataPointBtn.addEventListener('click', function() {
                    dataPointDetails.classList.add('hidden');
                });
            }
            
            // Setup event handlers for timeline milestones
            setupTimelineEvents();
        }
        
        // Helper to add special points (red/blue dots with notes)
        function addSpecialPoint(dataset, index, color, note) {
            // Initialize if needed
            if (!dataset.specialPoints) {
                dataset.specialPoints = {};
                dataset.pointBackgroundColor = Array(dataset.data.length).fill(dataset.backgroundColor);
                dataset.pointBorderColor = Array(dataset.data.length).fill(dataset.borderColor);
                dataset.pointRadius = Array(dataset.data.length).fill(0);
                dataset.pointBorderWidth = Array(dataset.data.length).fill(1);
            }
            
            // Store the note information and set the point
            dataset.specialPoints[index] = { color: color, note: note };
            dataset.pointBackgroundColor[index] = color === 'red' ? 'rgba(239, 68, 68, 1)' : 'rgba(37, 99, 235, 1)';
            dataset.pointBorderColor[index] = 'white';
            dataset.pointRadius[index] = 4;
            dataset.pointBorderWidth[index] = 1;
        }
        
        // Creates overlay elements for better display when clicking special points
        function createSpecialPointsOverlay(ctx, chartData, chart) {
            const overlay = document.createElement('div');
            overlay.className = 'absolute inset-0 pointer-events-none';
            ctx.parentNode.appendChild(overlay);
            
            // Add arrow markers to the chart
            chart.options.plugins.afterDraw = (chart) => {
                const ctx = chart.ctx;
                const datasets = chartData.datasets;
                
                // Process each dataset
                datasets.forEach((dataset, datasetIndex) => {
                    const meta = chart.getDatasetMeta(datasetIndex);
                    if (!meta.hidden) {
                        const data = dataset.data;
                        
                        // Go through points and draw arrows
                        for (let i = 1; i < data.length; i++) {
                            // Get current and previous point
                            const currentPoint = meta.data[i];
                            const prevPoint = meta.data[i-1];
                            
                            // Only draw for points that exist
                            if (!currentPoint || !prevPoint) continue;
                            
                            // Get coordinates
                            const xCurrent = currentPoint.x;
                            const yCurrent = currentPoint.y;
                            const xPrev = prevPoint.x;
                            const yPrev = prevPoint.y;
                            
                            // Determine if it's an up or down movement
                            const isDownward = yCurrent > yPrev; // Note: Y increases downward in Canvas
                            
                            // If significant vertical change, add an arrow
                            const yDiff = Math.abs(yCurrent - yPrev);
                            if (yDiff > 5) {
                                // Calculate middle of vertical line
                                const arrowX = xPrev;
                                const arrowY = (yPrev + yCurrent) / 2;
                                
                                // Draw arrow
                                ctx.save();
                                ctx.beginPath();
                                ctx.fillStyle = isDownward ? 'rgba(239, 68, 68, 0.9)' : 'rgba(37, 99, 235, 0.9)';
                                
                                // Arrow pointing down or up
                                if (isDownward) {
                                    // Down arrow (worsening)
                                    ctx.moveTo(arrowX - 5, arrowY - 5);
                                    ctx.lineTo(arrowX + 5, arrowY - 5);
                                    ctx.lineTo(arrowX, arrowY + 5);
                                } else {
                                    // Up arrow (improvement)
                                    ctx.moveTo(arrowX - 5, arrowY + 5);
                                    ctx.lineTo(arrowX + 5, arrowY + 5);
                                    ctx.lineTo(arrowX, arrowY - 5);
                                }
                                
                                ctx.closePath();
                                ctx.fill();
                                ctx.restore();
                            }
                        }
                    }
                });
            };
            
            // Handle clicks on chart area to show info popup
            ctx.addEventListener('click', function(evt) {
                const activePoints = chart.getElementsAtEventForMode(evt, 'nearest', { intersect: true }, false);
                if (activePoints.length > 0) {
                    const datasetIndex = activePoints[0].datasetIndex;
                    const pointIndex = activePoints[0].index;
                    const dataset = chartData.datasets[datasetIndex];
                    
                    if (dataset.specialPoints && dataset.specialPoints[pointIndex]) {
                        const note = dataset.specialPoints[pointIndex].note;
                        const color = dataset.specialPoints[pointIndex].color;
                        const label = dataset.label;
                        
                        // Instead of popup, update the detailed panel below the chart
                        showDataPointDetails(label, chartData.labels[pointIndex], note, 
                                            dataset.data[pointIndex > 0 ? pointIndex-1 : 0], 
                                            dataset.data[pointIndex], 
                                            color);
                    }
                }
            });
        }
        
        // Show data point details in the interactive panel 
        function showDataPointDetails(label, date, note, beforeValue, afterValue, color) {
            const dataPointDetails = document.getElementById('dataPointDetails');
            if (!dataPointDetails) return;
            
            // Update panel content
            document.getElementById('symptomDetailTitle').textContent = `${label} - ${date}`;
            document.getElementById('symptomDetailDescription').textContent = note || 'No additional notes available';
            
            // Calculate values and change percentage
            document.getElementById('symptomBefore').textContent = `${beforeValue}/10`;
            document.getElementById('symptomAfter').textContent = `${afterValue}/10`;
            
            if (beforeValue > 0) {
                const changePercentage = ((afterValue - beforeValue) / beforeValue * 100).toFixed(1);
                const isImprovement = (label === 'Sleep Quality' && changePercentage > 0) || 
                                     (label !== 'Sleep Quality' && changePercentage < 0);
                
                const changeElement = document.getElementById('symptomChange');
                changeElement.textContent = `${changePercentage > 0 ? '+' : ''}${changePercentage}%`;
                
                if (isImprovement) {
                    changeElement.classList.remove('text-red-600', 'dark:text-red-400');
                    changeElement.classList.add('text-blue-600', 'dark:text-blue-400');
                } else {
                    changeElement.classList.remove('text-blue-600', 'dark:text-blue-400');
                    changeElement.classList.add('text-red-600', 'dark:text-red-400');
                }
            } else {
                document.getElementById('symptomChange').textContent = 'N/A';
            }
            
            // Show the panel
            dataPointDetails.classList.remove('hidden');
        }
        
        // Show all data point notes when toggling view
        function showAllDataPointNotes(chartData) {
            // Clear any existing notes
            hideAllDataPointNotes();
            
            // Create and position notes for all special points
            chartData.datasets.forEach((dataset, datasetIndex) => {
                if (dataset.specialPoints) {
                    Object.keys(dataset.specialPoints).forEach(pointIndex => {
                        const point = dataset.specialPoints[pointIndex];
                        
                        // Create note element
                        const noteDiv = document.createElement('div');
                        noteDiv.className = `absolute z-10 bg-white dark:bg-gray-800 p-2 rounded shadow-md text-xs max-w-[150px] border ${point.color === 'red' ? 'border-red-500' : 'border-blue-500'} data-point-note`;
                        noteDiv.innerHTML = `
                            <div class="font-medium">${dataset.label}</div>
                            <div>${point.note}</div>
                        `;
                        
                        // Add to chart container with position to be set by chart plugin
                        noteDiv.dataset.datasetIndex = datasetIndex;
                        noteDiv.dataset.pointIndex = pointIndex;
                        
                        const chartContainer = document.getElementById('symptomProgressionChart');
                        if (chartContainer) {
                            chartContainer.appendChild(noteDiv);
                        }
                    });
                }
            });
        }
        
        // Hide all data point notes
        function hideAllDataPointNotes() {
            document.querySelectorAll('.data-point-note').forEach(note => {
                note.remove();
            });
        }
        
        // Setup timeline event handlers
        function setupTimelineEvents() {
            // Sample event details implementation
            window.showEventDetails = function(eventId) {
                // Event details data
                const events = {
                    'event1': {
                        title: 'Medication Started',
                        date: 'Jan 5',
                        description: 'Patient started on Amitriptyline 25mg daily for headache prevention and sleep improvement',
                        impact: 'Initial side effects (drowsiness) reported, but headache frequency began decreasing within 10 days'
                    },
                    'event2': {
                        title: 'B12 Supplements Added',
                        date: 'Feb 2', 
                        description: 'Started B12 supplements to address fatigue symptoms',
                        impact: 'Energy levels improved noticeably within 2 weeks'
                    },
                    'event3': {
                        title: 'Work Presentation Stress',
                        date: 'Feb 16',
                        description: 'Patient experienced significant anxiety related to major work presentation',
                        impact: 'Anxiety symptoms spiked and sleep quality temporarily decreased'
                    },
                    'event4': {
                        title: 'Medication Compliance Issue',
                        date: 'Mar 1',
                        description: 'Patient missed medication for 2 days while traveling',
                        impact: 'Temporary increase in dizziness and headache symptoms'
                    },
                    'event5': {
                        title: 'Sleep Routine Improved',
                        date: 'Mar 15',
                        description: 'Implemented consistent sleep schedule and pre-sleep meditation routine',
                        impact: 'Sleep quality improved significantly; reduced fatigue reported'
                    },
                    'event6': {
                        title: 'Current Assessment',
                        date: 'Today',
                        description: 'Overall symptom assessment shows significant improvement across most measures',
                        impact: 'Anxiety remains a concern despite improvements in other areas'
                    }
                };
                
                // Get event data
                const event = events[eventId];
                if (!event) return;
                
                // Create event detail popup
                let popup = document.getElementById('timelineEventPopup');
                if (popup) popup.remove();
                
                popup = document.createElement('div');
                popup.id = 'timelineEventPopup';
                popup.className = 'fixed bottom-8 left-1/2 transform -translate-x-1/2 z-[70] bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 max-w-md border border-gray-200 dark:border-gray-700 w-full mx-4';
                
                popup.innerHTML = `
                    <div class="flex justify-between items-start mb-3">
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-white">${event.title}</h4>
                            <div class="text-xs text-gray-500 dark:text-gray-400">${event.date}</div>
                        </div>
                        <button class="text-gray-500 hover:text-gray-700 text-xs" onclick="this.parentNode.parentNode.remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="space-y-2">
                        <div>
                            <div class="text-xs font-medium text-gray-700 dark:text-gray-300">Description</div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">${event.description}</p>
                        </div>
                        <div>
                            <div class="text-xs font-medium text-gray-700 dark:text-gray-300">Patient Impact</div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">${event.impact}</p>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(popup);
            };
        }

        function generateMockAnalysis() {
            // Initialize chart when analysis is displayed
            setTimeout(() => {
                // Clear any existing chart first
                const chartContainer = document.getElementById('symptomProgressionChart');
                if (chartContainer) {
                    chartContainer.innerHTML = '';
                }
                initSymptomProgressionChart();
            }, 100);
            
            return `
                <div class="space-y-6 h-[500px]">
                    <!-- Health Concerns Section -->
                    <div class="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm">
                        <h4 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                            <i class="fas fa-heartbeat text-red-500 mr-2"></i>
                            Health Concerns Detected
                        </h4>
                        <div class="mt-4 space-y-3">
                            <div class="flex items-start">
                                <div class="bg-red-100 dark:bg-red-800/30 p-1.5 rounded mr-3">
                                    <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400"></i>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-800 dark:text-white">Headaches</h5>
                                    <p class="text-sm text-gray-600 dark:text-gray-300">Recurring symptom, mentioned as improving but still present</p>
                                    <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">Confidence: 92%</div>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-amber-100 dark:bg-amber-800/30 p-1.5 rounded mr-3">
                                    <i class="fas fa-exclamation-triangle text-amber-600 dark:text-amber-400"></i>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-800 dark:text-white">Anxiety</h5>
                                    <p class="text-sm text-gray-600 dark:text-gray-300">Persistent issue affecting daily functioning, especially in workspace</p>
                                    <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">Confidence: 97%</div>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-amber-100 dark:bg-amber-800/30 p-1.5 rounded mr-3">
                                    <i class="fas fa-exclamation-triangle text-amber-600 dark:text-amber-400"></i>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-800 dark:text-white">Sleep Problems</h5>
                                    <p class="text-sm text-gray-600 dark:text-gray-300">Reported as improving with current medication</p>
                                    <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">Confidence: 85%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Medications Section -->
                    <div class="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm">
                        <h4 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                            <i class="fas fa-pills text-blue-500 mr-2"></i>
                            Medications Mentioned
                        </h4>
                        <div class="mt-4">
                            <div class="flex items-start">
                                <div class="bg-blue-100 dark:bg-blue-800/30 p-1.5 rounded mr-3">
                                    <i class="fas fa-capsules text-blue-600 dark:text-blue-400"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h5 class="font-medium text-gray-800 dark:text-white">Amitriptyline</h5>
                                        <button class="text-xs bg-blue-50 hover:bg-blue-100 text-blue-700 px-2 py-1 rounded dark:bg-blue-900/50 dark:hover:bg-blue-800 dark:text-blue-300" onclick="showMedicationInfo('Amitriptyline')">
                                            View Info
                                        </button>
                                    </div>
                                    <p class="text-sm text-gray-600 dark:text-gray-300">Tricyclic antidepressant, being used for headache prevention and sleep improvement</p>
                                    <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">Common dosage: 10-50mg daily, typically taken at bedtime</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Patient Wishes/Requests Section -->
                    <div class="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm">
                        <h4 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                            <i class="fas fa-comment-dots text-violet-500 mr-2"></i>
                            Patient Requests & Wishes
                        </h4>
                        <div class="mt-4 space-y-3">
                            <div class="flex items-start">
                                <div class="bg-violet-100 dark:bg-violet-800/30 p-1.5 rounded mr-3">
                                    <i class="fas fa-lightbulb text-violet-600 dark:text-violet-400"></i>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-800 dark:text-white">Additional anxiety management options</h5>
                                    <p class="text-sm text-gray-600 dark:text-gray-300">Patient expressed interest in exploring other anxiety management approaches</p>
                                    <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">Confidence: 90%</div>
                                </div>
                            </div>
                            <div class="flex items-start">
                                <div class="bg-violet-100 dark:bg-violet-800/30 p-1.5 rounded mr-3">
                                    <i class="fas fa-lightbulb text-violet-600 dark:text-violet-400"></i>
                                </div>
                                <div>
                                    <h5 class="font-medium text-gray-800 dark:text-white">Medication adjustment consideration</h5>
                                    <p class="text-sm text-gray-600 dark:text-gray-300">Implied openness to medication adjustments to better manage anxiety</p>
                                    <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">Confidence: 75%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Treatment Progress Section -->
                    <div class="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm">
                        <h4 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
                            <i class="fas fa-chart-line text-green-500 mr-2"></i>
                            Treatment Progress
                        </h4>
                        <div class="mt-4 space-y-4">
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <h5 class="font-medium text-gray-800 dark:text-white text-sm">Headache Management</h5>
                                    <span class="text-xs text-green-600 dark:text-green-400">Improving</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-600">
                                    <div class="bg-green-500 h-2.5 rounded-full" style="width: 70%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <h5 class="font-medium text-gray-800 dark:text-white text-sm">Sleep Quality</h5>
                                    <span class="text-xs text-green-600 dark:text-green-400">Improving</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-600">
                                    <div class="bg-green-500 h-2.5 rounded-full" style="width: 65%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <h5 class="font-medium text-gray-800 dark:text-white text-sm">Anxiety Management</h5>
                                    <span class="text-xs text-amber-600 dark:text-amber-400">Limited Progress</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-600">
                                    <div class="bg-amber-500 h-2.5 rounded-full" style="width: 30%"></div>
                                </div>
                            </div>
                            <div>
                                <div class="flex justify-between items-center mb-1">
                                    <h5 class="font-medium text-gray-800 dark:text-white text-sm">Coping Techniques Use</h5>
                                    <span class="text-xs text-red-600 dark:text-red-400">Needs Attention</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-600">
                                    <div class="bg-red-500 h-2.5 rounded-full" style="width: 20%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Progression/Regression Chart - Enhanced Interactive Version -->
                        <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                            <div class="flex justify-between items-center mb-4">
                                <h5 class="font-medium text-gray-800 dark:text-white">Symptoms Progression Timeline</h5>
                                <div class="flex space-x-2">
                                    <button id="toggleChartViewBtn" class="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-800/40 text-blue-700 dark:text-blue-300 rounded-md flex items-center hover:bg-blue-200 dark:hover:bg-blue-800">
                                        <i class="fas fa-chart-line mr-1"></i> <span id="viewModeText">Line View</span>
                                    </button>
                                    <button id="toggleNotesBtn" class="text-xs px-2 py-1 bg-purple-100 dark:bg-purple-800/40 text-purple-700 dark:text-purple-300 rounded-md flex items-center hover:bg-purple-200 dark:hover:bg-purple-800">
                                        <i class="fas fa-sticky-note mr-1"></i> Show All Notes
                                    </button>
                                </div>
                            </div>

                            <!-- Interactive Legend with Toggle Abilities -->
                            <div class="mb-4 bg-white dark:bg-gray-700 p-3 rounded-lg shadow-sm border border-gray-100 dark:border-gray-600">
                                <div class="flex justify-between items-center mb-2">
                                    <h6 class="text-sm font-medium text-gray-700 dark:text-gray-300">Symptoms Tracked</h6>
                                    <button id="collapseLegendsBtn" class="text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">
                                        <i class="fas fa-chevron-up"></i>
                                    </button>
                                </div>
                                <div id="symptomLegends" class="grid grid-cols-2 md:grid-cols-3 gap-2">
                                    <div class="symptom-legend flex items-center p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer" data-index="0">
                                        <div class="relative">
                                            <span class="inline-block w-3 h-3 bg-blue-600 dark:bg-blue-500 rounded-full mr-2"></span>
                                            <span class="absolute -top-1 -right-1 text-[8px] w-3 h-3 bg-white dark:bg-gray-700 rounded-full border border-blue-500 flex items-center justify-center">
                                                <i class="fas fa-arrow-down text-blue-600 dark:text-blue-400"></i>
                                            </span>
                                        </div>
                                        <span class="text-xs text-gray-700 dark:text-gray-300">Headaches</span>
                                        <div class="ml-auto flex items-center">
                                            <span class="text-blue-600 dark:text-blue-400 text-[10px] font-medium">-75%</span>
                                            <i class="fas fa-check-circle text-green-500 ml-1 text-xs"></i>
                                        </div>
                                    </div>
                                    <div class="symptom-legend flex items-center p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer" data-index="1">
                                        <div class="relative">
                                            <span class="inline-block w-3 h-3 bg-purple-600 dark:bg-purple-500 rounded-full mr-2"></span>
                                            <span class="absolute -top-1 -right-1 text-[8px] w-3 h-3 bg-white dark:bg-gray-700 rounded-full border border-purple-500 flex items-center justify-center">
                                                <i class="fas fa-arrow-up text-red-500 dark:text-red-400"></i>
                                            </span>
                                        </div>
                                        <span class="text-xs text-gray-700 dark:text-gray-300">Anxiety</span>
                                        <div class="ml-auto flex items-center">
                                            <span class="text-red-500 dark:text-red-400 text-[10px] font-medium">+25%</span>
                                            <i class="fas fa-exclamation-circle text-red-500 ml-1 text-xs"></i>
                                        </div>
                                    </div>
                                    <div class="symptom-legend flex items-center p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer" data-index="2">
                                        <div class="relative">
                                            <span class="inline-block w-3 h-3 bg-green-600 dark:bg-green-500 rounded-full mr-2"></span>
                                            <span class="absolute -top-1 -right-1 text-[8px] w-3 h-3 bg-white dark:bg-gray-700 rounded-full border border-green-500 flex items-center justify-center">
                                                <i class="fas fa-arrow-up text-blue-600 dark:text-blue-400"></i>
                                            </span>
                                        </div>
                                        <span class="text-xs text-gray-700 dark:text-gray-300">Sleep Quality</span>
                                        <div class="ml-auto flex items-center">
                                            <span class="text-blue-600 dark:text-blue-400 text-[10px] font-medium">+133%</span>
                                            <i class="fas fa-check-circle text-green-500 ml-1 text-xs"></i>
                                        </div>
                                    </div>
                                    <div class="symptom-legend flex items-center p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer" data-index="3">
                                        <div class="relative">
                                            <span class="inline-block w-3 h-3 bg-amber-500 dark:bg-amber-400 rounded-full mr-2"></span>
                                            <span class="absolute -top-1 -right-1 text-[8px] w-3 h-3 bg-white dark:bg-gray-700 rounded-full border border-amber-500 flex items-center justify-center">
                                                <i class="fas fa-arrow-down text-blue-600 dark:text-blue-400"></i>
                                            </span>
                                        </div>
                                        <span class="text-xs text-gray-700 dark:text-gray-300">Dizziness</span>
                                        <div class="ml-auto flex items-center">
                                            <span class="text-blue-600 dark:text-blue-400 text-[10px] font-medium">-78%</span>
                                            <i class="fas fa-check-circle text-green-500 ml-1 text-xs"></i>
                                        </div>
                                    </div>
                                    <div class="symptom-legend flex items-center p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer" data-index="4">
                                        <div class="relative">
                                            <span class="inline-block w-3 h-3 bg-pink-500 dark:bg-pink-400 rounded-full mr-2"></span>
                                            <span class="absolute -top-1 -right-1 text-[8px] w-3 h-3 bg-white dark:bg-gray-700 rounded-full border border-pink-500 flex items-center justify-center">
                                                <i class="fas fa-arrow-down text-blue-600 dark:text-blue-400"></i>
                                            </span>
                                        </div>
                                        <span class="text-xs text-gray-700 dark:text-gray-300">Fatigue</span>
                                        <div class="ml-auto flex items-center">
                                            <span class="text-blue-600 dark:text-blue-400 text-[10px] font-medium">-63%</span>
                                            <i class="fas fa-check-circle text-green-500 ml-1 text-xs"></i>
                                        </div>
                                    </div>
                                    <div class="symptom-legend flex items-center p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer" data-index="5">
                                        <div class="relative">
                                            <span class="inline-block w-3 h-3 bg-cyan-500 dark:bg-cyan-400 rounded-full mr-2"></span>
                                            <span class="absolute -top-1 -right-1 text-[8px] w-3 h-3 bg-white dark:bg-gray-700 rounded-full border border-cyan-500 flex items-center justify-center">
                                                <i class="fas fa-arrow-down text-blue-600 dark:text-blue-400"></i>
                                            </span>
                                        </div>
                                        <span class="text-xs text-gray-700 dark:text-gray-300">Pain</span>
                                        <div class="ml-auto flex items-center">
                                            <span class="text-blue-600 dark:text-blue-400 text-[10px] font-medium">-71%</span>
                                            <i class="fas fa-check-circle text-green-500 ml-1 text-xs"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Timeline Visualization -->
                            <div class="timeline-visualization mb-4 pb-2">
                                <div class="w-full">
                                    <div class="flex items-center border-b border-gray-200 dark:border-gray-700 pb-1 mb-2">
                                        <div class="w-24 text-xs text-gray-500 font-medium">DATE</div>
                                        <div class="flex-1 flex justify-between">
                                            <div class="text-xs text-gray-500">Jan 5</div>
                                            <div class="text-xs text-gray-500">Jan 19</div>
                                            <div class="text-xs text-gray-500">Feb 2</div>
                                            <div class="text-xs text-gray-500">Feb 16</div>
                                            <div class="text-xs text-gray-500">Mar 1</div>
                                            <div class="text-xs text-gray-500">Mar 15</div>
                                            <div class="text-xs text-gray-500 font-medium">Current</div>
                                        </div>
                                    </div>
                                    
                                    <!-- Static chart visualization (no JS required) -->
                                    <div id="symptomProgressionChart" class="w-full h-60 relative bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
                                        <div class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">Symptom Progression Chart</div>
                                        <!-- Static visualization with enhanced visibility -->
                                        <div class="static-chart-preview w-full h-full flex flex-col">
                                            <!-- Headache Static Chart Line -->
                                            <div class="relative h-14 mb-2">
                                                <div class="absolute w-full h-2 bg-blue-100 dark:bg-blue-900/30 rounded-full top-6"></div>
                                                <!-- Connected line -->
                                                <div class="absolute top-5 left-0 w-full h-1 z-10">
                                                    <svg height="14" width="100%" class="absolute top-0">
                                                        <polyline 
                                                            points="0,5 16.7%,4 33.4%,2 50%,3 66.7%,1 83.4%,0 100%,0" 
                                                            stroke="#3B82F6" 
                                                            stroke-width="2" 
                                                            fill="none"/>
                                                    </svg>
                                                </div>
                                                <div class="absolute left-0 rounded-full w-4 h-4 bg-blue-600 border-2 border-white dark:border-gray-800 top-5 z-20"></div>
                                                <div class="absolute left-[16.7%] rounded-full w-4 h-4 bg-blue-600 border-2 border-white dark:border-gray-800 top-4 z-20"></div>
                                                <div class="absolute left-[33.4%] rounded-full w-4 h-4 bg-blue-600 border-2 border-white dark:border-gray-800 top-2 z-20"></div>
                                                <div class="absolute left-[50%] rounded-full w-4 h-4 bg-blue-600 border-2 border-white dark:border-gray-800 top-3 z-20"></div>
                                                <div class="absolute left-[66.7%] rounded-full w-4 h-4 bg-blue-600 border-2 border-white dark:border-gray-800 top-1 z-20"></div>
                                                <div class="absolute left-[83.4%] rounded-full w-4 h-4 bg-blue-600 border-2 border-white dark:border-gray-800 top-0 z-20"></div>
                                                <div class="absolute left-[100%] rounded-full w-4 h-4 bg-blue-600 border-2 border-white dark:border-gray-800 top-0 -ml-4 z-20"></div>
                                                <div class="absolute left-[5%] top-[-5px] text-[11px] font-medium text-blue-700 dark:text-blue-400">Headaches 75% ↓</div>
                                                
                                                <!-- Improvement Arrows -->
                                                <div class="absolute left-[24.5%] top-2 w-0 h-0 border-l-5 border-l-transparent border-r-5 border-r-transparent border-b-5 border-b-blue-600 z-20"></div>
                                                <div class="absolute left-[74.5%] top-0 w-0 h-0 border-l-5 border-l-transparent border-r-5 border-r-transparent border-b-5 border-b-blue-600 z-20"></div>
                                            </div>
                                            
                                            <!-- Anxiety Static Chart Line -->
                                            <div class="relative h-14 mb-2">
                                                <div class="absolute w-full h-2 bg-purple-100 dark:bg-purple-900/30 rounded-full top-6"></div>
                                                <!-- Connected line -->
                                                <div class="absolute top-3 left-0 w-full h-1 z-10">
                                                    <svg height="14" width="100%" class="absolute top-0">
                                                        <polyline 
                                                            points="0,3 16.7%,4 33.4%,3 50%,5 66.7%,4 83.4%,5 100%,3" 
                                                            stroke="#8B5CF6" 
                                                            stroke-width="2" 
                                                            fill="none"/>
                                                    </svg>
                                                </div>
                                                <div class="absolute left-0 rounded-full w-4 h-4 bg-purple-600 border-2 border-white dark:border-gray-800 top-3 z-20"></div>
                                                <div class="absolute left-[16.7%] rounded-full w-4 h-4 bg-purple-600 border-2 border-white dark:border-gray-800 top-4 z-20"></div>
                                                <div class="absolute left-[33.4%] rounded-full w-4 h-4 bg-purple-600 border-2 border-white dark:border-gray-800 top-3 z-20"></div>
                                                <div class="absolute left-[50%] rounded-full w-4 h-4 bg-purple-600 border-2 border-white dark:border-gray-800 top-5 z-20"></div>
                                                <div class="absolute left-[66.7%] rounded-full w-4 h-4 bg-purple-600 border-2 border-white dark:border-gray-800 top-4 z-20"></div>
                                                <div class="absolute left-[83.4%] rounded-full w-4 h-4 bg-purple-600 border-2 border-white dark:border-gray-800 top-5 z-20"></div>
                                                <div class="absolute left-[100%] rounded-full w-4 h-4 bg-purple-600 border-2 border-white dark:border-gray-800 top-3 -ml-4 z-20"></div>
                                                <div class="absolute left-[42%] top-[-5px] text-[11px] font-medium text-purple-700 dark:text-purple-400">Anxiety 25% ↑</div>
                                                
                                                <!-- Worsening Arrows -->
                                                <div class="absolute left-[41.7%] top-4 w-0 h-0 border-l-5 border-l-transparent border-r-5 border-r-transparent border-t-5 border-t-red-600 z-20"></div>
                                                <div class="absolute left-[74.5%] top-4 w-0 h-0 border-l-5 border-l-transparent border-r-5 border-r-transparent border-t-5 border-t-red-600 z-20"></div>
                                            </div>
                                            
                                            <!-- Sleep Quality Static Chart Line -->
                                            <div class="relative h-14">
                                                <div class="absolute w-full h-2 bg-green-100 dark:bg-green-900/30 rounded-full top-6"></div>
                                                <!-- Connected line -->
                                                <div class="absolute top-5 left-0 w-full h-1 z-10">
                                                    <svg height="14" width="100%" class="absolute top-0">
                                                        <polyline 
                                                            points="0,9 16.7%,8 33.4%,7 50%,7 66.7%,6 83.4%,6 100%,5" 
                                                            stroke="#10B981" 
                                                            stroke-width="2" 
                                                            fill="none"/>
                                                    </svg>
                                                </div>
                                                <div class="absolute left-0 rounded-full w-4 h-4 bg-green-600 border-2 border-white dark:border-gray-800 top-9 z-20"></div>
                                                <div class="absolute left-[16.7%] rounded-full w-4 h-4 bg-green-600 border-2 border-white dark:border-gray-800 top-8 z-20"></div>
                                                <div class="absolute left-[33.4%] rounded-full w-4 h-4 bg-green-600 border-2 border-white dark:border-gray-800 top-7 z-20"></div>
                                                <div class="absolute left-[50%] rounded-full w-4 h-4 bg-green-600 border-2 border-white dark:border-gray-800 top-7 z-20"></div>
                                                <div class="absolute left-[66.7%] rounded-full w-4 h-4 bg-green-600 border-2 border-white dark:border-gray-800 top-6 z-20"></div>
                                                <div class="absolute left-[83.4%] rounded-full w-4 h-4 bg-green-600 border-2 border-white dark:border-gray-800 top-6 z-20"></div>
                                                <div class="absolute left-[100%] rounded-full w-4 h-4 bg-green-600 border-2 border-white dark:border-gray-800 top-5 -ml-4 z-20"></div>
                                                <div class="absolute left-[70%] top-[-5px] text-[11px] font-medium text-green-700 dark:text-green-400">Sleep Quality 133% ↑</div>
                                                
                                                <!-- Improvement Arrows (for sleep, up is good) -->
                                                <div class="absolute left-[24.5%] top-7 w-0 h-0 border-l-5 border-l-transparent border-r-5 border-r-transparent border-t-5 border-t-blue-600 z-20"></div>
                                                <div class="absolute left-[91.7%] top-5 w-0 h-0 border-l-5 border-l-transparent border-r-5 border-r-transparent border-t-5 border-t-blue-600 z-20"></div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Timeline Events Below Chart -->
                                    <div class="timeline-events mt-2 border-t border-gray-200 dark:border-gray-700 pt-3">
                                        <h6 class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">Key Treatment Events</h6>
                                        <div class="relative">
                                            <div class="absolute top-3 left-1/2 transform -translate-x-1/2 h-0.5 bg-gray-200 dark:bg-gray-700 w-[calc(100%-4rem)]"></div>
                                            <div class="flex justify-between relative h-12">
                                                <div class="timeline-milestone relative" onclick="showEventDetails('event1')">
                                                    <div class="absolute left-0 w-3 h-3 rounded-full bg-blue-500 cursor-pointer"></div>
                                                    <div class="absolute -left-10 -top-6 w-20 text-center text-[10px] text-blue-600 dark:text-blue-400">Medication Started</div>
                                                </div>
                                                <div class="timeline-milestone relative" onclick="showEventDetails('event2')">
                                                    <div class="absolute left-0 w-3 h-3 rounded-full bg-green-500 cursor-pointer"></div>
                                                </div>
                                                <div class="timeline-milestone relative" onclick="showEventDetails('event3')">
                                                    <div class="absolute left-0 w-3 h-3 rounded-full bg-red-500 cursor-pointer"></div>
                                                    <div class="absolute -left-10 -top-6 w-20 text-center text-[10px] text-red-500 dark:text-red-400">Work Stress</div>
                                                </div>
                                                <div class="timeline-milestone relative" onclick="showEventDetails('event4')">
                                                    <div class="absolute left-0 w-3 h-3 rounded-full bg-amber-500 cursor-pointer"></div>
                                                </div>
                                                <div class="timeline-milestone relative" onclick="showEventDetails('event5')">
                                                    <div class="absolute left-0 w-3 h-3 rounded-full bg-green-500 cursor-pointer"></div>
                                                    <div class="absolute -left-10 -top-6 w-20 text-center text-[10px] text-green-500 dark:text-green-400">Sleep Routine</div>
                                                </div>
                                                <div class="timeline-milestone relative" onclick="showEventDetails('event6')">
                                                    <div class="absolute left-0 w-3 h-3 rounded-full bg-blue-500 cursor-pointer"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Interactive Data Point Display -->
                            <div id="dataPointDetails" class="bg-white dark:bg-gray-700 p-3 rounded-lg shadow-sm border border-gray-100 dark:border-gray-600 hidden mb-3">
                                <div class="flex justify-between items-start mb-2">
                                    <h6 id="symptomDetailTitle" class="text-sm font-medium text-gray-800 dark:text-white">Headaches - Feb 2</h6>
                                    <button id="closeDataPointBtn" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <p id="symptomDetailDescription" class="text-xs text-gray-600 dark:text-gray-300 mb-2">
                                    Headache frequency decreased after starting medication
                                </p>
                                <div class="grid grid-cols-3 gap-2 text-xs">
                                    <div class="rounded bg-gray-50 dark:bg-gray-600/30 p-2">
                                        <div class="text-gray-500 dark:text-gray-400">Before</div>
                                        <div id="symptomBefore" class="font-medium text-gray-800 dark:text-white">7/10</div>
                                    </div>
                                    <div class="rounded bg-gray-50 dark:bg-gray-600/30 p-2">
                                        <div class="text-gray-500 dark:text-gray-400">After</div>
                                        <div id="symptomAfter" class="font-medium text-gray-800 dark:text-white">5/10</div>
                                    </div>
                                    <div class="rounded bg-gray-50 dark:bg-gray-600/30 p-2">
                                        <div class="text-gray-500 dark:text-gray-400">Change</div>
                                        <div id="symptomChange" class="font-medium text-blue-600 dark:text-blue-400">-28.6%</div>
                                    </div>
                                </div>
                                <div class="mt-2 flex justify-end">
                                    <button class="text-xs bg-blue-50 hover:bg-blue-100 text-blue-700 px-2 py-1 rounded dark:bg-blue-900/30 dark:hover:bg-blue-800/50 dark:text-blue-300">
                                        <i class="fas fa-plus mr-1"></i> Add Note
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function showMedicationInfo(medicationName) {
            const modal = document.getElementById('medicationInfoModal');
            const medicationTitle = document.getElementById('medicationName');
            const medicationContent = document.getElementById('medicationContent');
            
            medicationTitle.textContent = medicationName;
            
            // Generate medication information based on the name
            if (medicationName === 'Amitriptyline') {
                medicationContent.innerHTML = `
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <div class="bg-blue-100 p-2 rounded-full mr-3 flex-shrink-0">
                                <i class="fas fa-capsules text-blue-700"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-800">General Information</h5>
                                <p class="text-sm text-gray-600 mt-1">
                                    Amitriptyline is a tricyclic antidepressant that affects chemicals in the brain that may be unbalanced in
                                    people with depression. It's also used to treat symptoms of depression, anxiety, and chronic pain conditions.
                                </p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="bg-green-100 p-2 rounded-full mr-3 flex-shrink-0">
                                <i class="fas fa-notes-medical text-green-700"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-800">Common Uses</h5>
                                <ul class="list-disc pl-5 text-sm text-gray-600 mt-1 space-y-1">
                                    <li>Treatment of major depressive disorder</li>
                                    <li>Prevention of chronic tension headaches</li>
                                    <li>Prevention of migraine headaches</li>
                                    <li>Treatment of neuropathic pain</li>
                                    <li>Treatment of fibromyalgia pain</li>
                                    <li>Management of sleep disorders</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="bg-amber-100 p-2 rounded-full mr-3 flex-shrink-0">
                                <i class="fas fa-exclamation-triangle text-amber-700"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-800">Common Side Effects</h5>
                                <ul class="list-disc pl-5 text-sm text-gray-600 mt-1 space-y-1">
                                    <li>Drowsiness, dizziness</li>
                                    <li>Dry mouth</li>
                                    <li>Blurred vision</li>
                                    <li>Constipation</li>
                                    <li>Weight gain</li>
                                    <li>Urinary retention</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="bg-red-100 p-2 rounded-full mr-3 flex-shrink-0">
                                <i class="fas fa-skull-crossbones text-red-700"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-800">Warnings</h5>
                                <p class="text-sm text-gray-600 mt-1">
                                    <span class="font-semibold">Suicidal Thoughts:</span> Amitriptyline may increase the risk of suicidal thinking and behavior in children, adolescents, and young adults with depression.
                                </p>
                                <p class="text-sm text-gray-600 mt-2">
                                    <span class="font-semibold">Heart Issues:</span> Not recommended for patients with recent heart attack or heart rhythm problems.
                                </p>
                            </div>
                        </div>
                        
                        <div class="flex items-start">
                            <div class="bg-indigo-100 p-2 rounded-full mr-3 flex-shrink-0">
                                <i class="fas fa-pills text-indigo-700"></i>
                            </div>
                            <div>
                                <h5 class="font-medium text-gray-800">Dosage Information</h5>
                                <p class="text-sm text-gray-600 mt-1">
                                    <span class="font-semibold">Depression:</span> Initial: 25-75 mg/day orally at bedtime, gradually increased to 150-200 mg/day.
                                </p>
                                <p class="text-sm text-gray-600 mt-1">
                                    <span class="font-semibold">Chronic Pain/Headaches:</span> Initial: 10-25 mg/day orally at bedtime, gradually increased as needed.
                                </p>
                                <p class="text-sm text-gray-600 mt-1">
                                    <span class="font-semibold">Insomnia:</span> 10-50 mg orally at bedtime.
                                </p>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                // Generic message for other medications
                medicationContent.innerHTML = `<p class="text-gray-600">Detailed information for ${medicationName} is not available in the database.</p>`;
            }
            
            // Show modal
            modal.classList.remove('hidden');
            setTimeout(() => {
                const modalContent = modal.querySelector('.modal-content');
                modalContent.classList.remove('scale-95', 'opacity-0');
                modalContent.classList.add('scale-100', 'opacity-100');
            }, 10);
        }
        
        function saveCaretakerNotes() {
            const notesTextarea = document.getElementById('caretakerNotes');
            const notesContent = notesTextarea.value.trim();
            
            if (notesContent) {
                const notesContainer = document.getElementById('savedCaretakerNotes');
                const now = new Date();
                const formattedDate = now.toLocaleDateString() + ' ' + now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                
                // Create note element
                const noteDiv = document.createElement('div');
                noteDiv.className = 'bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg mb-3';
                noteDiv.innerHTML = `
                    <div class="flex justify-between items-start">
                        <p class="text-gray-800 dark:text-gray-200">${notesContent}</p>
                        <button class="text-xs text-red-500 hover:text-red-700" onclick="this.parentNode.parentNode.remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-2">${formattedDate}</div>
                `;
                
                // Add to container
                notesContainer.appendChild(noteDiv);
                
                // Clear textarea
                notesTextarea.value = '';
                
                // Show feedback
                const feedbackEl = document.getElementById('notesSavedFeedback');
                feedbackEl.classList.remove('hidden');
                setTimeout(() => {
                    feedbackEl.classList.add('hidden');
                }, 3000);
            }
        }
        
        function addFollowUpTask() {
            const taskInput = document.getElementById('followUpTaskInput');
            const dateInput = document.getElementById('followUpDateInput');
            const prioritySelect = document.getElementById('taskPrioritySelect');
            
            const taskText = taskInput.value.trim();
            const taskDate = dateInput.value;
            const priority = prioritySelect.value;
            
            if (taskText && taskDate) {
                const tasksContainer = document.getElementById('followUpTasksList');
                
                // Create task element
                const taskDiv = document.createElement('div');
                taskDiv.className = `bg-white dark:bg-gray-700 p-3 rounded-lg mb-3 border-l-4 ${getPriorityColorClass(priority)} shadow-sm`;
                taskDiv.innerHTML = `
                    <div class="flex justify-between items-start">
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-white text-sm">${taskText}</h4>
                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                Due: ${formatDate(taskDate)}
                            </p>
                        </div>
                        <div class="flex space-x-2">
                            <button class="text-xs text-green-500 hover:text-green-700" onclick="markTaskComplete(this)">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="text-xs text-red-500 hover:text-red-700" onclick="this.parentNode.parentNode.parentNode.remove()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;
                
                // Add to container
                tasksContainer.appendChild(taskDiv);
                
                // Clear inputs
                taskInput.value = '';
                dateInput.value = '';
                prioritySelect.value = 'medium';
                
                // Show feedback
                const feedbackEl = document.getElementById('taskAddedFeedback');
                feedbackEl.classList.remove('hidden');
                setTimeout(() => {
                    feedbackEl.classList.add('hidden');
                }, 3000);
            }
        }
        
        function getPriorityColorClass(priority) {
            switch(priority) {
                case 'high':
                    return 'border-red-500';
                case 'medium':
                    return 'border-yellow-500';
                case 'low':
                    return 'border-green-500';
                default:
                    return 'border-blue-500';
            }
        }
        
        function formatDate(dateString) {
            const options = { weekday: 'short', year: 'numeric', month: 'short', day: 'numeric' };
            return new Date(dateString).toLocaleDateString(undefined, options);
        }
        
        function markTaskComplete(btn) {
            const taskElement = btn.closest('div').parentNode.parentNode;
            taskElement.classList.add('opacity-50');
            
            // Add completed styling
            const taskText = taskElement.querySelector('h4');
            taskText.innerHTML = `<span class="line-through">${taskText.textContent}</span> <span class="ml-2 text-green-600 dark:text-green-400">(Completed)</span>`;
            
            // Disable buttons
            btn.disabled = true;
            btn.classList.add('opacity-50', 'cursor-not-allowed');
        }

        function downloadRecording(url, name) {
            const a = document.createElement('a');
            a.href = url;
            a.download = name;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        }

        function deleteRecording(id) {
            if (confirm('Are you sure you want to delete this recording?')) {
                const row = document.querySelector(`tr[data-recording-id="${id}"]`);
                if (row) {
                    row.remove();
                }
            }
        }
    </script>

    <!-- Header / Navigation -->
    <header class="bg-white shadow-sm sticky top-0 z-50 animate-fade-in border-b border-gray-100 dark:bg-gray-800 dark:border-gray-700">
        <div class="container mx-auto">
            <nav class="py-3 px-4 flex justify-between items-center">
                <div class="flex items-center">
                    <a href="index.html" class="flex items-center">
                        <div class="mr-3">
                            <div class="relative">
                                <!-- Wellora logo -->
                                <div class="relative" style="height: 48px; width: 48px;">
                                    <img src="rpz001l3th.png" alt="Logo" style="height: 48px; width: 48px; object-fit: contain;">
                                </div>
                            </div>
                        </div>
                        <h1 class="text-xl font-semibold text-dark hidden sm:block dark:text-white">Wellora</h1>
                    </a>
                </div>
                
                <div class="flex items-center">
                    <div class="relative mx-2">
                        <button class="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 relative">
                            <i class="fas fa-bell text-gray-600 dark:text-gray-300"></i>
                            <span class="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                        </button>
                    </div>
                    
                    <!-- Theme Toggle -->
                    <button id="themeToggle" class="mx-2 p-2 rounded-md bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 transition">
                        <i class="fas fa-sun text-yellow-500 dark:hidden"></i>
                        <i class="fas fa-moon text-blue-300 hidden dark:inline"></i>
                    </button>
                    
                    <div class="flex items-center ml-4">
                        <div class="mr-3 text-right hidden sm:block">
                            <p class="text-sm font-medium text-gray-900 dark:text-white">Dr. Sarah Matthews</p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Clinical Psychologist</p>
                        </div>
                        <button class="h-10 w-10 rounded-lg bg-white border border-gray-200 hover:bg-gray-50 flex items-center justify-center dark:bg-gray-700 dark:border-gray-600 dark:hover:bg-gray-600">
                            <img src="https://cdn.pixabay.com/photo/2018/01/15/07/51/woman-3083383_1280.jpg" alt="Profile" class="h-8 w-8 rounded-full object-cover">
                        </button>
                    </div>
                </div>
            </nav>
        </div>
    </header>
    
    <!-- Back to Dashboard link -->
    <div class="container mx-auto">
        <div class="p-4">
            <a href="therapist-dashboard.html" class="text-gray-600 hover:text-primary dark:text-gray-300">
                <i class="fas fa-arrow-left mr-2"></i>
                <span>Back to Dashboard</span>
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container mx-auto px-4 py-6">
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6">
            <div class="flex flex-col md:flex-row md:items-center justify-between">
                <div>
                    <h1 class="text-2xl font-semibold text-gray-800 dark:text-white mb-2">Voice Recordings</h1>
                    <p class="text-gray-600 dark:text-gray-400">Capture and manage your therapy session recordings</p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-3">
                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg dark:bg-gray-700 dark:text-gray-300" id="qrCodeBtn">
                        <i class="fas fa-qrcode mr-2"></i> QR Code
                    </button>
                    <button class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 shadow-sm" id="openRecordingModalBtn">
                        <i class="fas fa-microphone mr-2"></i> Record New
                    </button>
                </div>
            </div>
        </div>

        <!-- Success Alert (hidden by default) -->
        <div id="recordingSuccessAlert" class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md hidden animate-fade-in">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium">Recording saved successfully!</p>
                </div>
                <div class="ml-auto pl-3">
                    <div class="-mx-1.5 -my-1.5">
                        <button class="inline-flex bg-green-50 rounded-md p-1.5 text-green-500 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                onclick="document.getElementById('recordingSuccessAlert').classList.add('hidden')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recording Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm hover:shadow-md transition-all border border-gray-100 dark:border-gray-700">
                <div class="flex items-start">
                    <div class="p-3 rounded-full bg-blue-50 dark:bg-blue-900/30 mr-4">
                        <i class="fas fa-microphone text-primary text-lg"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Recordings</p>
                        <p class="text-2xl font-semibold text-gray-800 dark:text-white">24</p>
                        <div class="mt-1 flex items-center text-xs text-green-600 dark:text-green-400">
                            <i class="fas fa-arrow-up mr-1"></i>
                            <span>12% increase</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm hover:shadow-md transition-all border border-gray-100 dark:border-gray-700">
                <div class="flex items-start">
                    <div class="p-3 rounded-full bg-green-50 dark:bg-green-900/30 mr-4">
                        <i class="fas fa-clock text-green-500 text-lg"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Recording Time</p>
                        <p class="text-2xl font-semibold text-gray-800 dark:text-white">32h 45m</p>
                        <div class="mt-1 flex items-center text-xs text-gray-500 dark:text-gray-400">
                            <span>Last recording: 2 days ago</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm hover:shadow-md transition-all border border-gray-100 dark:border-gray-700">
                <div class="flex items-start">
                    <div class="p-3 rounded-full bg-purple-50 dark:bg-purple-900/30 mr-4 data-pulse">
                        <i class="fas fa-brain text-purple-500 text-lg"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">AI Processed</p>
                        <p class="text-2xl font-semibold text-gray-800 dark:text-white">22</p>
                        <div class="mt-1 flex items-center text-xs">
                            <span class="px-2 py-0.5 bg-purple-100 text-purple-800 rounded-full dark:bg-purple-900 dark:text-purple-200">
                                91.7% of recordings
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recordings Table -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden border border-gray-100 dark:border-gray-700">
            <div class="p-4 border-b border-gray-100 dark:border-gray-700 flex justify-between items-center">
                <h2 class="text-lg font-semibold text-gray-800 dark:text-white">Recent Recordings</h2>
                <div class="relative">
                    <input type="text" placeholder="Search recordings..." class="py-2 pl-10 pr-4 rounded-lg text-sm border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-900/30">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Recording Name</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Duration</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Size</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-400">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700" id="recordingsList">
                        <!-- Example recording row -->
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 recording-row cursor-pointer" data-id="1" data-name="Session with John D." data-date="2023-09-15" data-time="10:30 AM" data-duration="46:12" data-size="42.3 MB" data-transcribed="false">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8 rounded bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-3">
                                        <i class="fas fa-microphone text-primary"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">Session with John D.</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">2023-09-15 • 10:30 AM</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                46:12
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                42.3 MB
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                                <button class="play-recording-btn text-primary hover:text-primary/80 mx-1" onclick="event.stopPropagation();">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button class="download-recording-btn text-primary hover:text-primary/80 mx-1" onclick="event.stopPropagation();">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="delete-recording-btn text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 mx-1" onclick="event.stopPropagation();">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 recording-row cursor-pointer" data-id="2" data-name="Initial Assessment - Sarah M." data-date="2023-09-12" data-time="02:15 PM" data-duration="58:43" data-size="53.8 MB" data-transcribed="false">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8 rounded bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-3">
                                        <i class="fas fa-microphone text-primary"></i>
                                    </div>
                                    <div>
                                        <p class="font-medium text-gray-900 dark:text-white">Initial Assessment - Sarah M.</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">2023-09-12 • 02:15 PM</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                58:43
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                53.8 MB
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                                <button class="play-recording-btn text-primary hover:text-primary/80 mx-1" onclick="event.stopPropagation();">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button class="download-recording-btn text-primary hover:text-primary/80 mx-1" onclick="event.stopPropagation();">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="delete-recording-btn text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 mx-1" onclick="event.stopPropagation();">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 recording-row cursor-pointer" data-id="3" data-name="Therapy Session - Michael W." data-date="2023-09-10" data-time="11:00 AM" data-duration="42:18" data-size="38.9 MB" data-transcribed="true" data-ai-processed="true">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8 rounded bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-3">
                                        <i class="fas fa-microphone text-primary"></i>
                                    </div>
                                    <div>
                                        <div class="flex items-center">
                                            <p class="font-medium text-gray-900 dark:text-white">Therapy Session - Michael W.</p>
                                            <span class="ml-2 px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full dark:bg-green-900 dark:text-green-200">
                                                AI Processed
                                            </span>
                                        </div>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">2023-09-10 • 11:00 AM</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                42:18
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                38.9 MB
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                                <button class="play-recording-btn text-primary hover:text-primary/80 mx-1" onclick="event.stopPropagation();">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button class="download-recording-btn text-primary hover:text-primary/80 mx-1" onclick="event.stopPropagation();">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="delete-recording-btn text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 mx-1" onclick="event.stopPropagation();">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </td>
                        </tr>
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 recording-row cursor-pointer" data-id="4" data-name="Follow-up - Emily R." data-date="2023-09-05" data-time="03:45 PM" data-duration="38:27" data-size="35.2 MB" data-transcribed="true" data-ai-processed="true">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-8 w-8 rounded bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-3">
                                        <i class="fas fa-microphone text-primary"></i>
                                    </div>
                                    <div>
                                        <div class="flex items-center">
                                            <p class="font-medium text-gray-900 dark:text-white">Follow-up - Emily R.</p>
                                            <span class="ml-2 px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full dark:bg-green-900 dark:text-green-200">
                                                AI Processed
                                            </span>
                                        </div>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">2023-09-05 • 03:45 PM</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                38:27
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                35.2 MB
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                                <button class="play-recording-btn text-primary hover:text-primary/80 mx-1" onclick="event.stopPropagation();">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button class="download-recording-btn text-primary hover:text-primary/80 mx-1" onclick="event.stopPropagation();">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="delete-recording-btn text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 mx-1" onclick="event.stopPropagation();">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="bg-gray-50 dark:bg-gray-900/20 px-6 py-3 border-t border-gray-100 dark:border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            Showing <span class="font-medium">1</span> to <span class="font-medium">4</span> of <span class="font-medium">24</span> recordings
                        </p>
                    </div>
                    <div>
                        <nav class="flex items-center space-x-2">
                            <button class="px-3 py-1 rounded-md border border-gray-200 text-gray-500 hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-800" disabled>
                                <i class="fas fa-chevron-left text-xs"></i>
                            </button>
                            <button class="px-3 py-1 rounded-md bg-primary text-white hover:bg-primary/90">1</button>
                            <button class="px-3 py-1 rounded-md border border-gray-200 hover:bg-gray-100 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-800">2</button>
                            <button class="px-3 py-1 rounded-md border border-gray-200 hover:bg-gray-100 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-800">3</button>
                            <button class="px-3 py-1 rounded-md border border-gray-200 hover:bg-gray-100 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-800">4</button>
                            <button class="px-3 py-1 rounded-md border border-gray-200 text-gray-500 hover:bg-gray-100 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-800">
                                <i class="fas fa-chevron-right text-xs"></i>
                            </button>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- QR Code Modal (Hidden) -->
    <div id="qrCodeModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md w-full transform transition-all duration-300 scale-95 opacity-0 modal-content shadow-lg">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Generate QR Code for Recording</h3>
                <button id="closeQrModal" class="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="text-center p-4">
                <div id="qrCodeImage" class="mb-4 mx-auto w-48 h-48 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center">
                    <!-- QR code will be generated here -->
                    <i class="fas fa-qrcode text-6xl text-gray-300 dark:text-gray-500"></i>
                </div>
                <p class="mb-4 text-sm text-gray-600 dark:text-gray-300">Scan this QR code with the Wellora app to connect your external recording device.</p>
                <button id="generateQrCode" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90">
                    Generate QR Code
                </button>
            </div>
        </div>
    </div>

    <!-- AI Conversation Analysis Modal -->
    <div id="recordingModal" class="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-900 backdrop-blur-md rounded-2xl max-w-[95vw] w-full max-h-[95vh] transform transition-all scale-95 opacity-0 modal-content shadow-2xl border border-blue-100 dark:border-gray-700 overflow-hidden">
            <!-- Header -->
            <div class="flex justify-between items-center p-6 pb-4 border-b border-blue-100 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-gray-800 dark:to-gray-800">
                <div class="flex items-center">
                    <div class="mr-4 p-3 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center relative overflow-hidden shadow-lg">
                        <div class="absolute inset-0 bg-white/20 animate-pulse-slow rounded-xl"></div>
                        <i class="fas fa-brain text-white text-2xl relative z-10"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">
                            <span class="gradient-text">AI Gesprekanalyse</span>
                        </h2>
                        <p class="text-gray-600 dark:text-gray-300 text-sm mt-1">Geavanceerde AI-analyse voor patiëntgesprekken</p>
                    </div>
                </div>
                <button id="closeRecordingModal" class="text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors bg-white/80 dark:bg-gray-800/80 rounded-full w-10 h-10 flex items-center justify-center shadow-md hover:shadow-lg">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>

            <!-- Main Content Area -->
            <div class="flex h-[calc(95vh-120px)]">
                <!-- Left Panel - Audio Upload & Transcription -->
                <div class="w-2/3 p-6 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
                    <!-- Audio Upload Section -->
                    <div class="mb-6">
                        <div class="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-gray-800 dark:to-gray-700 rounded-xl p-6 border border-blue-100 dark:border-gray-600">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-upload text-blue-500 mr-2"></i>
                                <span>Audio Bestand Uploaden</span>
                            </h3>

                            <!-- File Upload Area -->
                            <div id="audioUploadArea" class="border-2 border-dashed border-blue-300 dark:border-gray-500 rounded-lg p-8 text-center bg-white/50 dark:bg-gray-800/50 hover:bg-white/80 dark:hover:bg-gray-700/80 transition-all cursor-pointer">
                                <div class="upload-content">
                                    <i class="fas fa-cloud-upload-alt text-4xl text-blue-400 mb-4"></i>
                                    <p class="text-gray-700 dark:text-gray-300 font-medium mb-2">Sleep uw MP3 bestand hier of klik om te uploaden</p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Ondersteunde formaten: MP3, WAV, M4A (max 100MB)</p>
                                    <input type="file" id="audioFileInput" accept=".mp3,.wav,.m4a" class="hidden">
                                </div>
                            </div>

                            <!-- Audio Player (Hidden initially) -->
                            <div id="audioPlayerSection" class="mt-4 hidden">
                                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                                    <div class="flex items-center justify-between mb-3">
                                        <span class="text-sm font-medium text-gray-700">Audio Bestand:</span>
                                        <span id="audioFileName" class="text-sm text-gray-600"></span>
                                    </div>
                                    <audio id="audioPlayer" controls class="w-full mb-3">
                                        <source id="audioSource" src="" type="audio/mpeg">
                                        Uw browser ondersteunt geen audio element.
                                    </audio>
                                    <div class="flex justify-between items-center">
                                        <button id="startAnalysisBtn" class="px-4 py-2 bg-gradient-to-r from-blue-500 to-cyan-500 text-white rounded-lg hover:from-blue-600 hover:to-cyan-600 transition-all flex items-center">
                                            <i class="fas fa-play mr-2"></i>
                                            <span>Start Analyse</span>
                                        </button>
                                        <div class="text-sm text-gray-500">
                                            <span>Duur:</span> <span id="audioDuration">--:--</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Transcription Section -->
                    <div id="transcriptionSection" class="hidden">
                        <div class="bg-gray-50 rounded-xl p-6 border border-gray-200">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                                    <i class="fas fa-comments text-green-500 mr-2"></i>
                                    <span>Gesprek Transcriptie</span>
                                </h3>
                                <div class="flex items-center space-x-2">
                                    <div id="analysisStatus" class="flex items-center text-sm">
                                        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"></div>
                                        <span class="text-green-600">Analyseren...</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Chat-like Transcription Display -->
                            <div id="transcriptionChat" class="space-y-4 max-h-96 overflow-y-auto bg-white rounded-lg p-4 border border-gray-200">
                                <!-- Transcription messages will be added here dynamically -->
                            </div>

                            <!-- Progress Bar -->
                            <div class="mt-4">
                                <div class="flex justify-between text-sm text-gray-600 mb-1">
                                    <span>Voortgang</span>
                                    <span id="progressPercentage">0%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div id="progressBar" class="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Panel - AI Insights -->
                <div class="w-1/3 p-6 bg-gray-50 dark:bg-gray-800 overflow-y-auto">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                        <span>AI Inzichten</span>
                    </h3>

                    <!-- Insights Cards Container -->
                    <div id="insightsContainer" class="space-y-4">
                        <!-- Initial state -->
                        <div id="noInsightsMessage" class="text-center py-8">
                            <i class="fas fa-brain text-4xl text-gray-300 mb-3"></i>
                            <p class="text-gray-500 dark:text-gray-400">Upload een audio bestand om AI-analyse te starten</p>
                        </div>
                    </div>

                    <!-- Summary Button (Hidden initially) -->
                    <div id="summarySection" class="mt-6 hidden">
                        <button id="generateSummaryBtn" class="w-full px-4 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all flex items-center justify-center">
                            <i class="fas fa-file-medical-alt mr-2"></i>
                            <span>Genereer Samenvatting & Behandelplan</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="p-6 pt-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 flex justify-between items-center">
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    <i class="fas fa-shield-alt mr-1"></i>
                    <span>HIPAA-conform & Veilig</span>
                </div>
                <div class="flex space-x-3">
                    <button id="closeRecordingModalBtn" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 dark:bg-gray-600 dark:hover:bg-gray-700 text-white rounded-lg transition-all">
                        <span>Sluiten</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recording Analysis Modal -->
    <div id="recordingAnalysisModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-6xl w-full h-[950px] transform transition-all duration-300 scale-95 opacity-0 modal-content shadow-lg">
            <div class="flex justify-between items-center mb-4 border-b border-gray-200 dark:border-gray-700 pb-4">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-50 dark:bg-blue-900/30 rounded-full mr-4">
                        <i class="fas fa-microphone text-primary text-xl"></i>
                    </div>
                    <div>
                        <h2 id="analysisRecordingTitle" class="text-xl font-bold text-gray-800 dark:text-white">Recording Analysis</h2>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Analyze audio recording for insights and transcription</p>
                    </div>
                </div>
                <button id="closeAnalysisModal" class="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- Transcription Status -->
            <div id="transcribeButton" class="mb-6 p-8 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-800/50 text-center">
                <i class="fas fa-microphone-alt text-4xl text-gray-400 dark:text-gray-500 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-2">This recording has not been transcribed</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">Transcribing this recording allows you to analyze the conversation, detect health issues, identify medications, and add follow-up tasks.</p>
                <button onclick="startTranscription(1)" class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors flex items-center justify-center mx-auto">
                    <i class="fas fa-play-circle mr-2"></i> Transcribe with OpenAI
                </button>
            </div>
            
            <!-- Transcription Loading State -->
            <div id="transcriptionLoading" class="hidden mb-6 p-8 border-2 border-gray-300 dark:border-gray-600 rounded-xl bg-gray-50 dark:bg-gray-800/50 text-center">
                <div class="animate-pulse">
                    <div class="flex justify-center mb-4">
                        <i class="fas fa-spinner fa-spin text-4xl text-primary"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-2">Transcribing your recording...</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">This may take a few minutes. OpenAI is processing the audio and analyzing the conversation.</p>
                </div>
                <div class="mt-6 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                    <div class="bg-primary h-2.5 rounded-full animate-pulse-slow" style="width: 75%"></div>
                </div>
            </div>
            
            <!-- Analysis Content (hidden by default) -->
            <div id="analysisContent" class="hidden">
                <!-- Download report button and options -->
                <div class="mb-6 flex justify-end">
                    <div class="relative">
                        <button id="reportOptionsBtn" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 flex items-center">
                            <i class="fas fa-download mr-2"></i> Download Report <i class="fas fa-caret-down ml-2"></i>
                        </button>
                        <div id="reportOptionsDropdown" class="hidden absolute right-0 mt-2 w-72 bg-white dark:bg-gray-700 rounded-lg shadow-lg z-10 p-4 border border-gray-200 dark:border-gray-600">
                            <h4 class="font-medium text-gray-800 dark:text-white mb-3">Report Options</h4>
                            <div class="space-y-2">
                                <div class="flex items-center">
                                    <input type="checkbox" id="includeTranscript" class="mr-2" checked>
                                    <label for="includeTranscript" class="text-gray-700 dark:text-gray-300 text-sm">Full Transcript</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="includeSummary" class="mr-2" checked>
                                    <label for="includeSummary" class="text-gray-700 dark:text-gray-300 text-sm">Summary</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="includeAnalysis" class="mr-2" checked>
                                    <label for="includeAnalysis" class="text-gray-700 dark:text-gray-300 text-sm">AI Analysis</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="includeNotes" class="mr-2" checked>
                                    <label for="includeNotes" class="text-gray-700 dark:text-gray-300 text-sm">Caretaker Notes</label>
                                </div>
                                <div class="flex items-center">
                                    <input type="checkbox" id="includeFollowUp" class="mr-2" checked>
                                    <label for="includeFollowUp" class="text-gray-700 dark:text-gray-300 text-sm">Follow-up Tasks</label>
                                </div>
                            </div>
                            <button id="generateReportBtn" class="mt-4 w-full px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 text-sm">
                                Generate Report
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Tabs -->
                <div class="border-b border-gray-200 dark:border-gray-700 mb-6">
                    <ul class="flex flex-wrap -mb-px">
                        <li class="mr-2">
                            <button class="inline-block p-4 border-b-2 border-primary text-primary dark:text-primary font-medium" onclick="showTab('transcriptionTab')">
                                <i class="fas fa-file-alt mr-2"></i>Transcription
                            </button>
                        </li>
                        <li class="mr-2">
                            <button class="inline-block p-4 border-b-2 border-transparent hover:border-blue-300 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 font-medium" onclick="showTab('analysisTab')">
                                <i class="fas fa-brain mr-2"></i>AI Analysis
                            </button>
                        </li>
                        <li class="mr-2">
                            <button class="inline-block p-4 border-b-2 border-transparent hover:border-blue-300 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 font-medium" onclick="showTab('notesTab')">
                                <i class="fas fa-sticky-note mr-2"></i>Caretaker Notes
                            </button>
                        </li>
                        <li>
                            <button class="inline-block p-4 border-b-2 border-transparent hover:border-blue-300 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 font-medium" onclick="showTab('followUpTab')">
                                <i class="fas fa-tasks mr-2"></i>Follow-up Tasks
                            </button>
                        </li>
                    </ul>
                </div>
                
                <!-- Tab Content -->
                <div id="transcriptionTab" class="tab-pane">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div class="lg:col-span-2">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-file-alt text-blue-500 mr-2"></i>
                                Full Transcription
                            </h3>
                            <div class="bg-white dark:bg-gray-700 rounded-xl p-6 shadow-sm overflow-auto h-fit border border-gray-100 dark:border-gray-600">
                                <div id="transcriptionText" class="text-gray-800 dark:text-gray-200 searchable-content"></div>
                                <div class="mt-4 flex">
                                    <div class="relative flex-grow">
                                        <input type="text" id="searchTranscript" placeholder="Search in transcript..." class="w-full p-2 pl-8 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-800 dark:text-white text-sm">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <i class="fas fa-search text-gray-400"></i>
                                        </div>
                                    </div>
                                    <button id="clearSearch" class="ml-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 text-sm">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-chart-pie text-purple-500 mr-2"></i>
                                Summary
                            </h3>
                            <div class="bg-white dark:bg-gray-700 rounded-xl p-6 shadow-sm overflow-auto h-fit border border-gray-100 dark:border-gray-600">
                                <div id="transcriptionSummary" class="text-gray-800 dark:text-gray-200"></div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="analysisTab" class="tab-pane hidden">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                        <i class="fas fa-brain text-purple-500 mr-2"></i>
                        AI Analysis Results
                    </h3>
                    <div class="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-6 pb-[56px] overflow-auto h-[650px] border border-gray-200 dark:border-gray-600">
                        <div id="analysisResults" class="text-gray-800 dark:text-gray-200"></div>
                    </div>
                </div>
                
                <div id="notesTab" class="tab-pane hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-sticky-note text-amber-500 mr-2"></i>
                                Add Notes
                            </h3>
                            <div class="bg-white dark:bg-gray-700 rounded-xl p-6 shadow-sm border border-gray-100 dark:border-gray-600">
                                <textarea id="caretakerNotes" rows="5" class="w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200" placeholder="Add your notes about this session..."></textarea>
                                <div class="mt-4 flex justify-end">
                                    <button onclick="saveCaretakerNotes()" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90">
                                        <i class="fas fa-save mr-2"></i> Save Notes
                                    </button>
                                </div>
                                <div id="notesSavedFeedback" class="mt-3 text-green-600 dark:text-green-400 text-sm hidden">
                                    <i class="fas fa-check-circle mr-1"></i> Note saved successfully!
                                </div>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-history text-green-500 mr-2"></i>
                                Saved Notes
                            </h3>
                            <div class="bg-white dark:bg-gray-700 rounded-xl p-6 shadow-sm overflow-auto max-h-[300px] border border-gray-100 dark:border-gray-600">
                                <div id="savedCaretakerNotes" class="text-gray-800 dark:text-gray-200">
                                    <p class="text-gray-500 dark:text-gray-400 text-center italic">No notes have been added yet.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div id="followUpTab" class="tab-pane hidden">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-clipboard-list text-blue-500 mr-2"></i>
                                Add Follow-up Task
                            </h3>
                            <div class="bg-white dark:bg-gray-700 rounded-xl p-6 shadow-sm border border-gray-100 dark:border-gray-600">
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Task Description</label>
                                    <input type="text" id="followUpTaskInput" class="w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200" placeholder="Enter follow-up task...">
                                </div>
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Due Date</label>
                                    <input type="date" id="followUpDateInput" class="w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200">
                                </div>
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Priority</label>
                                    <select id="taskPrioritySelect" class="w-full p-3 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-gray-800 text-gray-800 dark:text-gray-200">
                                        <option value="low">Low</option>
                                        <option value="medium" selected>Medium</option>
                                        <option value="high">High</option>
                                    </select>
                                </div>
                                <div class="mt-4 flex justify-end">
                                    <button onclick="addFollowUpTask()" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90">
                                        <i class="fas fa-plus mr-2"></i> Add Task
                                    </button>
                                </div>
                                <div id="taskAddedFeedback" class="mt-3 text-green-600 dark:text-green-400 text-sm hidden">
                                    <i class="fas fa-check-circle mr-1"></i> Task added successfully!
                                </div>
                            </div>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
                                <i class="fas fa-tasks text-green-500 mr-2"></i>
                                Follow-up Tasks
                            </h3>
                            <div class="bg-white dark:bg-gray-700 rounded-xl p-6 shadow-sm overflow-auto max-h-[400px] border border-gray-100 dark:border-gray-600">
                                <div id="followUpTasksList" class="text-gray-800 dark:text-gray-200">
                                    <p class="text-gray-500 dark:text-gray-400 text-center italic">No tasks have been added yet.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Medication Information Modal -->
    <div id="medicationInfoModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 scale-95 opacity-0 modal-content shadow-lg">
            <div class="flex justify-between items-center mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-full mr-4 flex-shrink-0">
                        <i class="fas fa-pills text-blue-600 dark:text-blue-400 text-xl"></i>
                    </div>
                    <div>
                        <h2 id="medicationName" class="text-xl font-bold text-gray-800 dark:text-white">Medication Information</h2>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Detailed information and side effects</p>
                    </div>
                </div>
                <button id="closeMedicationModal" class="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div id="medicationContent" class="text-gray-800 dark:text-gray-200 overflow-auto">
                <!-- Content will be populated by JS -->
            </div>
            
            <div class="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700 flex justify-between">
                <div>
                    <button id="downloadReportBtn" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90">
                        <i class="fas fa-download mr-2"></i> Download Report
                    </button>
                </div>
                <button id="closeMedicationBtn" onclick="document.getElementById('medicationInfoModal').classList.add('hidden')" class="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600">
                    Close
                </button>
            </div>
        </div>
    </div>
    
    <!-- Load Chart.js from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script>
        // Initialize modal functionality
        document.addEventListener('DOMContentLoaded', function() {
            // QR Code Modal
            const qrCodeBtn = document.getElementById('qrCodeBtn');
            const qrCodeModal = document.getElementById('qrCodeModal');
            const closeQrModal = document.getElementById('closeQrModal');
            const generateQrCode = document.getElementById('generateQrCode');
            
            if (qrCodeBtn && qrCodeModal) {
                qrCodeBtn.addEventListener('click', () => {
                    qrCodeModal.classList.remove('hidden');
                    setTimeout(() => {
                        const content = qrCodeModal.querySelector('.modal-content');
                        if (content) {
                            content.classList.remove('scale-95', 'opacity-0');
                            content.classList.add('scale-100', 'opacity-100');
                        }
                    }, 10);
                });
            }
            
            if (closeQrModal && qrCodeModal) {
                closeQrModal.addEventListener('click', () => {
                    const content = qrCodeModal.querySelector('.modal-content');
                    if (content) {
                        content.classList.remove('scale-100', 'opacity-100');
                        content.classList.add('scale-95', 'opacity-0');
                    }
                    
                    setTimeout(() => {
                        qrCodeModal.classList.add('hidden');
                    }, 300);
                });
            }
            
            if (generateQrCode) {
                generateQrCode.addEventListener('click', () => {
                    const qrCodeImage = document.getElementById('qrCodeImage');
                    if (qrCodeImage) {
                        qrCodeImage.innerHTML = `
                            <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=wellora-recording-session-${Date.now()}" alt="QR Code" class="w-full h-full">
                        `;
                    }
                });
            }
            
            // Search functionality
            const searchInput = document.getElementById('searchTranscript');
            const clearSearchBtn = document.getElementById('clearSearch');
            
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    searchTranscript(this.value);
                });
            }
            
            if (clearSearchBtn) {
                clearSearchBtn.addEventListener('click', function() {
                    if (searchInput) {
                        searchInput.value = '';
                        clearHighlights();
                    }
                });
            }
            
            function searchTranscript(query) {
                if (!query) {
                    clearHighlights();
                    return;
                }
                
                const container = document.getElementById('transcriptionText');
                if (!container) return;
                
                // Clear previous highlights
                clearHighlights();
                
                const text = container.innerHTML;
                const queryRegex = new RegExp(`(${escapeRegExp(query)})`, 'gi');
                
                // Highlight matches
                container.innerHTML = text.replace(queryRegex, '<span class="highlight-search audio-segment" onclick="playAudioSegment(this)">$1</span>');
            }
            
            function clearHighlights() {
                const container = document.getElementById('transcriptionText');
                if (!container) return;
                
                const highlightedElements = container.querySelectorAll('.highlight-search');
                if (highlightedElements.length === 0) return;
                
                // Replace highlighted elements with their text content
                const originalHTML = container.innerHTML;
                container.innerHTML = originalHTML.replace(/<span class="highlight-search audio-segment"[^>]*>(.*?)<\/span>/gi, '$1');
            }
            
            function escapeRegExp(string) {
                return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            }
            
            // Toggle report options dropdown
            const reportOptionsBtn = document.getElementById('reportOptionsBtn');
            const reportOptionsDropdown = document.getElementById('reportOptionsDropdown');
            
            if (reportOptionsBtn && reportOptionsDropdown) {
                reportOptionsBtn.addEventListener('click', function() {
                    reportOptionsDropdown.classList.toggle('hidden');
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', function(event) {
                    if (!reportOptionsBtn.contains(event.target) && !reportOptionsDropdown.contains(event.target)) {
                        reportOptionsDropdown.classList.add('hidden');
                    }
                });
            }
            
            // Generate report button
            const generateReportBtn = document.getElementById('generateReportBtn');
            if (generateReportBtn) {
                generateReportBtn.addEventListener('click', function() {
                    // Show generation in progress notification
                    showNotification('Generating PDF report...', 'info');
                    
                    // Generate a simple PDF directly with current analysis data
                    generateSimplePDFReport();
                    
                    // Hide the dropdown
                    reportOptionsDropdown.classList.add('hidden');
                });
            }
            
            // Setup Issue Tracking button
            const openIssueTrackingBtn = document.getElementById('openIssueTrackingBtn');
            if (openIssueTrackingBtn) {
                openIssueTrackingBtn.addEventListener('click', function() {
                    // Get recording info from the current modal
                    const recordingId = this.dataset.recordingId || '0';
                    const recordingName = document.getElementById('analysisRecordingTitle').textContent;
                    
                    // Open the issue tracking modal
                    openIssueTrackingModal(recordingId, recordingName);
                });
            }
            
            // Function to create a clean, simple PDF report directly
            function generateSimplePDFReport() {
                try {
                    // Show notification that we're opening the report
                    showNotification('Opening report in new window...', 'info');
                    
                    // Get patient info for the report
                    const recordingTitle = document.getElementById('analysisRecordingTitle')?.textContent || 'Medical Recording';
                    const reportId = `WLR-${Date.now().toString().substr(-6)}`;
                    const patientName = 'Emily R.';
                    const currentDate = new Date().toLocaleDateString('en-US', { 
                        year: 'numeric', month: 'long', day: 'numeric' 
                    });
                    
                    // Create a simple HTML report that can be printed to PDF
                    const reportHtml = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="UTF-8">
                        <title>Wellora Medical Report</title>
                        <style>
                            body {
                                font-family: 'Segoe UI', Arial, sans-serif;
                                line-height: 1.6;
                                color: #333;
                                background-color: white;
                                padding: 30px;
                                max-width: 900px;
                                margin: 0 auto;
                            }
                            .report-header {
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                margin-bottom: 25px;
                                padding-bottom: 15px;
                                border-bottom: 3px solid #0F6FFF;
                            }
                            .logo-area {
                                display: flex;
                                align-items: center;
                            }
                            .logo-circle {
                                width: 50px;
                                height: 50px;
                                border-radius: 50%;
                                background-color: #0F6FFF;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                margin-right: 15px;
                                color: white;
                                font-weight: bold;
                                font-size: 28px;
                                box-shadow: 0 2px 8px rgba(15, 111, 255, 0.3);
                            }
                            .title-area {
                                text-align: right;
                            }
                            h1 {
                                color: #0F6FFF;
                                margin: 0;
                                font-size: 32px;
                                font-weight: 600;
                            }
                            h2 {
                                color: #0066cc;
                                margin-top: 30px;
                                padding-bottom: 8px;
                                border-bottom: 1px solid #e5e7eb;
                                font-weight: 600;
                            }
                            h3 {
                                color: #2563eb;
                                margin-top: 20px;
                                font-weight: 600;
                            }
                            .info-box {
                                background-color: #f9fafb;
                                border: 1px solid #e5e7eb;
                                border-radius: 8px;
                                padding: 20px;
                                margin: 25px 0;
                                box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                                display: flex;
                                flex-wrap: wrap;
                            }
                            .info-column {
                                flex: 1;
                                min-width: 250px;
                                padding-right: 20px;
                            }
                            .info-item {
                                margin-bottom: 8px;
                            }
                            .finding-item {
                                margin-bottom: 25px;
                                padding-bottom: 20px;
                                border-bottom: 1px solid #e5e7eb;
                            }
                            .data-card {
                                background-color: #f9fafb;
                                border-radius: 8px;
                                padding: 20px;
                                margin-top: 15px;
                                border-left: 4px solid #0F6FFF;
                            }
                            .footer {
                                margin-top: 40px;
                                padding-top: 15px;
                                border-top: 1px solid #e5e7eb;
                                font-size: 12px;
                                color: #666;
                                text-align: center;
                            }
                            .print-button {
                                position: fixed;
                                top: 20px;
                                right: 20px;
                                background: #0F6FFF;
                                color: white;
                                padding: 12px 24px;
                                border: none;
                                border-radius: 6px;
                                cursor: pointer;
                                font-size: 16px;
                                font-weight: 600;
                                box-shadow: 0 2px 10px rgba(15, 111, 255, 0.3);
                                transition: all 0.2s ease;
                            }
                            .print-button:hover {
                                background: #0051cc;
                                transform: translateY(-2px);
                                box-shadow: 0 4px 12px rgba(15, 111, 255, 0.4);
                            }
                            @media print {
                                .print-button {
                                    display: none;
                                }
                                body {
                                    padding: 0;
                                }
                                @page {
                                    margin: 25mm 20mm 25mm 30mm;
                                }
                            }
                            .improved {
                                color: #059669;
                                font-weight: 600;
                            }
                            .worsened {
                                color: #dc2626;
                                font-weight: 600;
                            }
                            .stable {
                                color: #9ca3af;
                                font-weight: 600;
                            }
                            .grid-2 {
                                display: grid;
                                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                                gap: 20px;
                                margin: 20px 0;
                            }
                            .card {
                                background-color: #fff;
                                border-radius: 8px;
                                box-shadow: 0 2px 4px rgba(0,0,0,0.05);
                                padding: 15px;
                                border: 1px solid #e5e7eb;
                                display: flex;
                                flex-direction: column;
                            }
                            .card-header {
                                font-weight: 600;
                                color: #1f2937;
                                margin-bottom: 10px;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                            }
                            .card-value {
                                font-size: 24px;
                                font-weight: 700;
                                margin: 8px 0;
                            }
                            .badge {
                                display: inline-block;
                                padding: 2px 8px;
                                border-radius: 12px;
                                font-size: 12px;
                                font-weight: 600;
                            }
                            .badge-improved {
                                background-color: rgba(5, 150, 105, 0.1);
                                color: #059669;
                            }
                            .badge-worsened {
                                background-color: rgba(220, 38, 38, 0.1);
                                color: #dc2626;
                            }
                            .badge-stable {
                                background-color: rgba(156, 163, 175, 0.1);
                                color: #4b5563;
                            }
                            .progress-container {
                                width: 100%;
                                height: 8px;
                                background-color: #e5e7eb;
                                border-radius: 4px;
                                margin: 10px 0;
                            }
                            .progress-bar {
                                height: 100%;
                                border-radius: 4px;
                            }
                            .progress-improved {
                                background-color: #059669;
                            }
                            .progress-worsened {
                                background-color: #dc2626;
                            }
                            .progress-stable {
                                background-color: #9ca3af;
                            }
                            .trend-indicator {
                                margin-left: 5px;
                            }
                            .bar-chart {
                                width: 100%;
                                display: flex;
                                flex-direction: column;
                                margin-top: 20px;
                            }
                            .chart-title {
                                font-weight: 600;
                                margin-bottom: 10px;
                            }
                            .chart-container {
                                display: flex;
                                height: 250px;
                                align-items: flex-end;
                                border-left: 1px solid #e5e7eb;
                                border-bottom: 1px solid #e5e7eb;
                                padding-top: 20px;
                                position: relative;
                            }
                            .chart-column {
                                flex: 1;
                                margin: 0 10px;
                                position: relative;
                                display: flex;
                                flex-direction: column;
                                align-items: center;
                            }
                            .chart-bar {
                                width: 30px;
                                background-color: #0F6FFF;
                                border-radius: 4px 4px 0 0;
                                position: relative;
                            }
                            .chart-bar-label {
                                position: absolute;
                                top: -25px;
                                font-size: 12px;
                                font-weight: 600;
                                color: #333;
                            }
                            .chart-date {
                                font-size: 12px;
                                color: #666;
                                margin-top: 8px;
                                text-align: center;
                            }
                            .y-axis {
                                position: absolute;
                                left: -40px;
                                top: 0;
                                bottom: 0;
                                display: flex;
                                flex-direction: column;
                                justify-content: space-between;
                                font-size: 12px;
                                color: #666;
                            }
                            table {
                                width: 100%;
                                border-collapse: collapse;
                                margin: 20px 0;
                            }
                            th, td {
                                padding: 12px 15px;
                                text-align: left;
                                border-bottom: 1px solid #e5e7eb;
                            }
                            th {
                                background-color: #f9fafb;
                                font-weight: 600;
                            }
                            tr:nth-child(even) {
                                background-color: #f9fafb;
                            }
                            .section-image {
                                display: block;
                                margin: 20px auto;
                                max-width: 100%;
                                height: auto;
                                border-radius: 8px;
                            }
                            .quote {
                                background-color: #f0f7ff;
                                border-left: 4px solid #0F6FFF;
                                padding: 15px;
                                margin: 20px 0;
                                font-style: italic;
                                color: #1e3a8a;
                            }
                            .recommendation-card {
                                background-color: #f0f9ff;
                                border: 1px solid #bae6fd;
                                border-radius: 8px;
                                padding: 15px;
                                margin-bottom: 15px;
                            }
                            .recommendation-title {
                                font-weight: 600;
                                color: #0284c7;
                                margin-bottom: 8px;
                            }
                        </style>
                    </head>
                    <body>
                        <button onclick="window.print()" class="print-button">Print / Save as PDF</button>
                        
                        <div class="report-header">
                            <div class="logo-area">
                                <div class="logo-circle">W</div>
                                <div>
                                    <div style="font-weight: bold; font-size: 28px; color: #0F6FFF;">Wellora</div>
                                    <div style="font-size: 14px; color: #666;">AI Medical Insights</div>
                                </div>
                            </div>
                            <div class="title-area">
                                <h1>Comprehensive Medical Report</h1>
                                <div style="color: #666; font-size: 16px;">${currentDate}</div>
                            </div>
                        </div>
                        
                        <div class="info-box">
                            <div class="info-column">
                                <div style="font-size: 20px; font-weight: bold; margin-bottom: 15px; color: #0F6FFF;">${patientName}</div>
                                <div class="info-item"><strong>Report ID:</strong> ${reportId}</div>
                                <div class="info-item"><strong>Session Date:</strong> ${currentDate}</div>
                                <div class="info-item"><strong>Session Type:</strong> Follow-Up Assessment</div>
                            </div>
                            <div class="info-column">
                                <div class="info-item"><strong>Provider:</strong> Dr. Sarah Matthews</div>
                                <div class="info-item"><strong>Recording:</strong> ${recordingTitle}</div>
                                <div class="info-item"><strong>Session Duration:</strong> 45 minutes</div>
                                <div class="info-item"><strong>Primary Concern:</strong> Anxiety Management</div>
                            </div>
                            <div class="info-column">
                                <div class="info-item"><strong>Age:</strong> 34</div>
                                <div class="info-item"><strong>Gender:</strong> Female</div>
                                <div class="info-item"><strong>Occupation:</strong> Marketing Manager</div>
                                <div class="info-item"><strong>Insurance:</strong> Blue Cross (#BC938475)</div>
                            </div>
                        </div>
                        
                        <h2>Executive Summary</h2>
                        <p>
                            This follow-up assessment indicates mixed progress in the patient's treatment journey. There has been 
                            significant improvement in sleep quality (133% improvement) and headache frequency (50% reduction), 
                            primarily attributed to the successful response to Amitriptyline therapy initiated 6 weeks ago.
                            However, workplace-related anxiety has intensified (33% increase), requiring targeted 
                            intervention strategies. The patient demonstrates excellent medication adherence but requires additional 
                            support for developing effective coping mechanisms for workplace stressors.
                        </p>
                        
                        <div class="quote">
                            "My boss criticized my work in front of everyone last week. I've been having trouble concentrating because 
                            I'm constantly thinking about it and worried about the next meeting." - Patient statement during session
                        </div>
                        
                        <!-- Visual dashboard at the top -->
                        <div class="grid-2">
                            <div class="card">
                                <div class="card-header">
                                    Sleep Quality
                                    <span class="badge badge-improved">Significantly Improved</span>
                                </div>
                                <div class="card-value">7.2 <span style="font-size: 16px;">/ 10</span> <span class="trend-indicator improved">▲</span></div>
                                <div style="margin-bottom: 5px; font-size: 12px; color: #666;">Previous: 3.1 / 10</div>
                                <div class="progress-container">
                                    <div class="progress-bar progress-improved" style="width: 72%;"></div>
                                </div>
                                <div style="margin-top: 10px; font-size: 13px;">
                                    Averaging 7+ hours nightly, with fewer awakenings
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">
                                    Headache Severity
                                    <span class="badge badge-improved">Improved</span>
                                </div>
                                <div class="card-value">4.0 <span style="font-size: 16px;">/ 10</span> <span class="trend-indicator improved">▼</span></div>
                                <div style="margin-bottom: 5px; font-size: 12px; color: #666;">Previous: 8.0 / 10</div>
                                <div class="progress-container">
                                    <div class="progress-bar progress-improved" style="width: 40%;"></div>
                                </div>
                                <div style="margin-top: 10px; font-size: 13px;">
                                    Reduced from daily to weekly occurrences
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">
                                    Anxiety Level
                                    <span class="badge badge-worsened">Worsened</span>
                                </div>
                                <div class="card-value">8.2 <span style="font-size: 16px;">/ 10</span> <span class="trend-indicator worsened">▲</span></div>
                                <div style="margin-bottom: 5px; font-size: 12px; color: #666;">Previous: 6.1 / 10</div>
                                <div class="progress-container">
                                    <div class="progress-bar progress-worsened" style="width: 82%;"></div>
                                </div>
                                <div style="margin-top: 10px; font-size: 13px;">
                                    Increased due to workplace stressors
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">
                                    Medication Adherence
                                    <span class="badge badge-improved">Excellent</span>
                                </div>
                                <div class="card-value">98% <span class="trend-indicator improved">▲</span></div>
                                <div style="margin-bottom: 5px; font-size: 12px; color: #666;">Previous: 92%</div>
                                <div class="progress-container">
                                    <div class="progress-bar progress-improved" style="width: 98%;"></div>
                                </div>
                                <div style="margin-top: 10px; font-size: 13px;">
                                    Taking Amitriptyline as prescribed
                                </div>
                            </div>
                        </div>
                        
                        <h2>Detailed Assessment Findings</h2>
                        
                        <div class="finding-item">
                            <h3>Sleep Quality & Patterns</h3>
                            <div class="data-card">
                                <p>
                                    <strong>Current:</strong> 7+ hours per night (7.2/10 quality)<br>
                                    <strong>Previous:</strong> 5 hours per night (3.1/10 quality)<br>
                                    <strong>Change:</strong> <span class="improved">▲ 133% Improved</span>
                                </p>
                                <p><strong>Details:</strong> Patient reports significant improvement in both sleep duration and quality 
                                since starting Amitriptyline. Night awakenings have decreased from 3-4 times nightly to 0-1, and morning 
                                grogginess has largely resolved. Patient reports feeling refreshed upon waking for the first time in "several years."</p>
                                <p><strong>Sleep Diary Highlights:</strong> Consistent bedtime (10:30pm ± 30min), wake time (6:30am ± 15min). 
                                Sleep latency reduced from 45-60 minutes to 15-20 minutes. REM sleep (estimated by dream recall) appears to have normalized.</p>
                            </div>
                        </div>
                        
                        <div class="finding-item">
                            <h3>Headache Assessment</h3>
                            <div class="data-card">
                                <p>
                                    <strong>Current:</strong> Weekly occurrences (4.0/10 severity)<br>
                                    <strong>Previous:</strong> Daily occurrences (8.0/10 severity)<br>
                                    <strong>Change:</strong> <span class="improved">▼ 50% Improved</span>
                                </p>
                                <p><strong>Headache Characteristics:</strong> Primarily tension-type headaches, bilateral, non-pulsating, 
                                mild to moderate intensity. Duration has decreased from 6-8 hours to 2-3 hours per episode. No photophobia or 
                                phonophobia reported. No aura or neurological symptoms.</p>
                                <p><strong>Triggers:</strong> Consistently associated with workplace stress (meetings, deadlines), 
                                poor sleep nights, and prolonged screen time. Recent exacerbations correlate with increased workplace anxiety.</p>
                                <p><strong>Response to Treatment:</strong> Good response to prophylactic Amitriptyline. Breakthrough headaches 
                                respond well to OTC analgesics (ibuprofen 400mg). No need for prescription abortive medications at this time.</p>
                            </div>
                        </div>
                        
                        <div class="finding-item">
                            <h3>Anxiety Symptoms & Stressors</h3>
                            <div class="data-card">
                                <p>
                                    <strong>Current:</strong> High (8.2/10 severity)<br>
                                    <strong>Previous:</strong> Moderate (6.1/10 severity)<br>
                                    <strong>Change:</strong> <span class="worsened">▲ 33% Worsened</span>
                                </p>
                                <p><strong>Primary Symptoms:</strong> Persistent worry, difficulty concentrating, rumination on past interactions, 
                                sleep disturbance (despite medication), muscle tension, irritability, and avoidance behaviors at work.</p>
                                <p><strong>Specific Workplace Triggers:</strong> Recent public criticism from supervisor, upcoming 
                                project deadline (March 25), team presentation scheduled for next week, and perceived lack of support from colleagues.</p>
                                <p><strong>Impact on Functioning:</strong> Productivity decreased by approximately 30% over past two weeks. 
                                Patient reports checking and re-checking work excessively, avoiding team meetings when possible, 
                                and experiencing physical symptoms (racing heart, shallow breathing) when anticipating interactions with supervisor.</p>
                                <p><strong>Coping Attempts:</strong> Patient has tried breathing exercises but reports difficulty implementing 
                                these during acute episodes. No consistent mindfulness practice established yet.</p>
                            </div>
                        </div>
                        
                        <div class="finding-item">
                            <h3>Energy & Concentration</h3>
                            <div class="data-card">
                                <p>
                                    <strong>Current:</strong> Moderate (6.0/10 level)<br>
                                    <strong>Previous:</strong> Low (4.0/10 level)<br>
                                    <strong>Change:</strong> <span class="improved">▲ 50% Improved</span>
                                </p>
                                <p><strong>Pattern:</strong> Energy levels highest in morning (7/10), declining significantly after workplace 
                                interactions (4/10), with some recovery in evening (5/10). Weekend energy levels consistently higher (7-8/10).</p>
                                <p><strong>Concentration:</strong> Improved for routine tasks but significantly impaired during anxiety episodes. 
                                Patient reports "mind going blank" during high-pressure situations and difficulty remembering details from meetings.</p>
                            </div>
                        </div>
                        
                        <h2>Symptom Progression Chart</h2>
                        <div class="bar-chart">
                            <p class="chart-title">Symptom Severity Tracking (0-10 scale)</p>
                            <div class="chart-container">
                                <div class="y-axis">
                                    <div>10</div>
                                    <div>8</div>
                                    <div>6</div>
                                    <div>4</div>
                                    <div>2</div>
                                    <div>0</div>
                                </div>
                                
                                <!-- Headaches (Blue bars) -->
                                <div class="chart-column">
                                    <div class="chart-bar" style="height: 80%; background-color: #0F6FFF;">
                                        <div class="chart-bar-label">8.0</div>
                                    </div>
                                    <div class="chart-date">Jan 5</div>
                                    <div style="font-size: 10px; color: #0F6FFF;">Headaches</div>
                                </div>
                                <div class="chart-column">
                                    <div class="chart-bar" style="height: 70%; background-color: #0F6FFF;">
                                        <div class="chart-bar-label">7.0</div>
                                    </div>
                                    <div class="chart-date">Jan 19</div>
                                    <div style="font-size: 10px; color: #0F6FFF;">Headaches</div>
                                </div>
                                <div class="chart-column">
                                    <div class="chart-bar" style="height: 60%; background-color: #0F6FFF;">
                                        <div class="chart-bar-label">6.0</div>
                                    </div>
                                    <div class="chart-date">Feb 2</div>
                                    <div style="font-size: 10px; color: #0F6FFF;">Headaches</div>
                                </div>
                                <div class="chart-column">
                                    <div class="chart-bar" style="height: 50%; background-color: #0F6FFF;">
                                        <div class="chart-bar-label">5.0</div>
                                    </div>
                                    <div class="chart-date">Feb 16</div>
                                    <div style="font-size: 10px; color: #0F6FFF;">Headaches</div>
                                </div>
                                <div class="chart-column">
                                    <div class="chart-bar" style="height: 40%; background-color: #0F6FFF;">
                                        <div class="chart-bar-label">4.0</div>
                                    </div>
                                    <div class="chart-date">Mar 1</div>
                                    <div style="font-size: 10px; color: #0F6FFF;">Headaches</div>
                                </div>
                                
                                <!-- Anxiety (Red bars) -->
                                <div class="chart-column">
                                    <div class="chart-bar" style="height: 55%; background-color: #dc2626;">
                                        <div class="chart-bar-label">5.5</div>
                                    </div>
                                    <div class="chart-date">Jan 5</div>
                                    <div style="font-size: 10px; color: #dc2626;">Anxiety</div>
                                </div>
                                <div class="chart-column">
                                    <div class="chart-bar" style="height: 60%; background-color: #dc2626;">
                                        <div class="chart-bar-label">6.0</div>
                                    </div>
                                    <div class="chart-date">Jan 19</div>
                                    <div style="font-size: 10px; color: #dc2626;">Anxiety</div>
                                </div>
                                <div class="chart-column">
                                    <div class="chart-bar" style="height: 58%; background-color: #dc2626;">
                                        <div class="chart-bar-label">5.8</div>
                                    </div>
                                    <div class="chart-date">Feb 2</div>
                                    <div style="font-size: 10px; color: #dc2626;">Anxiety</div>
                                </div>
                                <div class="chart-column">
                                    <div class="chart-bar" style="height: 70%; background-color: #dc2626;">
                                        <div class="chart-bar-label">7.0</div>
                                    </div>
                                    <div class="chart-date">Feb 16</div>
                                    <div style="font-size: 10px; color: #dc2626;">Anxiety</div>
                                </div>
                                <div class="chart-column">
                                    <div class="chart-bar" style="height: 82%; background-color: #dc2626;">
                                        <div class="chart-bar-label">8.2</div>
                                    </div>
                                    <div class="chart-date">Mar 1</div>
                                    <div style="font-size: 10px; color: #dc2626;">Anxiety</div>
                                </div>
                                
                                <!-- Sleep Quality (Green bars) -->
                                <div class="chart-column">
                                    <div class="chart-bar" style="height: 30%; background-color: #059669;">
                                        <div class="chart-bar-label">3.0</div>
                                    </div>
                                    <div class="chart-date">Jan 5</div>
                                    <div style="font-size: 10px; color: #059669;">Sleep</div>
                                </div>
                                <div class="chart-column">
                                    <div class="chart-bar" style="height: 35%; background-color: #059669;">
                                        <div class="chart-bar-label">3.5</div>
                                    </div>
                                    <div class="chart-date">Jan 19</div>
                                    <div style="font-size: 10px; color: #059669;">Sleep</div>
                                </div>
                                <div class="chart-column">
                                    <div class="chart-bar" style="height: 50%; background-color: #059669;">
                                        <div class="chart-bar-label">5.0</div>
                                    </div>
                                    <div class="chart-date">Feb 2</div>
                                    <div style="font-size: 10px; color: #059669;">Sleep</div>
                                </div>
                                <div class="chart-column">
                                    <div class="chart-bar" style="height: 62%; background-color: #059669;">
                                        <div class="chart-bar-label">6.2</div>
                                    </div>
                                    <div class="chart-date">Feb 16</div>
                                    <div style="font-size: 10px; color: #059669;">Sleep</div>
                                </div>
                                <div class="chart-column">
                                    <div class="chart-bar" style="height: 72%; background-color: #059669;">
                                        <div class="chart-bar-label">7.2</div>
                                    </div>
                                    <div class="chart-date">Mar 1</div>
                                    <div style="font-size: 10px; color: #059669;">Sleep</div>
                                </div>
                            </div>
                        </div>
                        <div style="font-size: 12px; color: #666; margin-top: 5px; text-align: center;">
                            Chart shows trends in primary symptoms over time. For headaches and anxiety, lower values indicate improvement.
                            For sleep quality, higher values indicate improvement.
                        </div>
                        
                        <h2>Medication Assessment</h2>
                        <table>
                            <tr>
                                <th>Medication</th>
                                <th>Dosage</th>
                                <th>Duration</th>
                                <th>Purpose</th>
                                <th>Effectiveness</th>
                                <th>Side Effects</th>
                            </tr>
                            <tr>
                                <td>Amitriptyline</td>
                                <td>25mg once daily at bedtime</td>
                                <td>Started 6 weeks ago</td>
                                <td>Headache prevention, sleep improvement</td>
                                <td>High (8/10)</td>
                                <td>Initial morning drowsiness (resolved)</td>
                            </tr>
                            <tr>
                                <td>Ibuprofen</td>
                                <td>400mg as needed</td>
                                <td>PRN for breakthrough headaches</td>
                                <td>Acute headache treatment</td>
                                <td>Moderate (6/10)</td>
                                <td>None reported</td>
                            </tr>
                            <tr>
                                <td>Magnesium Glycinate</td>
                                <td>400mg daily</td>
                                <td>Started 3 weeks ago</td>
                                <td>Muscle tension, headache support</td>
                                <td>Mild (3/10)</td>
                                <td>None reported</td>
                            </tr>
                        </table>
                        <div style="font-size: 14px; margin-top: 10px; color: #666;">
                            <strong>Medication Adherence:</strong> Patient reports 98% compliance with Amitriptyline (missed one dose during travel).
                            No medication interactions or contraindications identified.
                        </div>
                        
                        <h2>Comprehensive Treatment Plan</h2>
                        
                        <div class="recommendation-card">
                            <div class="recommendation-title">Medication Recommendations</div>
                            <ol>
                                <li><strong>Continue Amitriptyline 25mg</strong> at bedtime for headache prevention and sleep improvement</li>
                                <li>Maintain current OTC regimen with <strong>Ibuprofen 400mg PRN</strong> for breakthrough headaches</li>
                                <li>Continue <strong>Magnesium Glycinate 400mg</strong> daily for muscle tension support</li>
                                <li>Consider adding <strong>as-needed anxiolytic</strong> if workplace anxiety continues to increase (to be evaluated at next visit)</li>
                            </ol>
                        </div>
                        
                        <div class="recommendation-card">
                            <div class="recommendation-title">Behavioral Interventions</div>
                            <ol>
                                <li><strong>Structured Workplace Anxiety Management Plan</strong>
                                    <ul style="margin-top: 5px;">
                                        <li>Implementation of 5-minute grounding exercises before challenging meetings</li>
                                        <li>Practice cognitive restructuring for workplace criticism using provided worksheets</li>
                                        <li>Maintain "success journal" to document daily workplace accomplishments</li>
                                    </ul>
                                </li>
                                <li><strong>Daily Mindfulness Practice</strong> (10 minutes morning, 10 minutes evening)
                                    <ul style="margin-top: 5px;">
                                        <li>Recommended guided audio: "Stress Reduction Series" on Wellora app</li>
                                        <li>Focus on body scan technique when anxiety symptoms first appear</li>
                                    </ul>
                                </li>
                                <li><strong>Progressive Muscle Relaxation</strong> during anxiety episodes
                                    <ul style="margin-top: 5px;">
                                        <li>Start with 5-minute abbreviated version for workplace settings</li>
                                        <li>Complete 15-minute full sequence before bed on high-stress days</li>
                                    </ul>
                                </li>
                                <li><strong>Sleep Hygiene Maintenance</strong>
                                    <ul style="margin-top: 5px;">
                                        <li>Continue consistent sleep schedule (10:30pm - 6:30am)</li>
                                        <li>Maintain bedroom temperature between 65-68°F</li>
                                        <li>Limit screen time to 30 minutes before bed</li>
                                    </ul>
                                </li>
                            </ol>
                        </div>
                        
                        <div class="recommendation-card">
                            <div class="recommendation-title">Follow-Up Plan</div>
                            <ul>
                                <li><strong>Next appointment:</strong> March 15, 2024 (2 weeks)</li>
                                <li><strong>Priority focus:</strong> Reassess anxiety levels following implementation of workplace strategies</li>
                                <li><strong>Monitoring tools:</strong> Daily anxiety tracking log and workplace stressor diary</li>
                                <li><strong>Consider workplace accommodations</strong> if anxiety remains elevated despite interventions</li>
                                <li><strong>Immediate contact recommended</strong> if anxiety interferes with daily functioning or sleep patterns revert</li>
                            </ul>
                        </div>
                        
                        <h2>Patient Resources</h2>
                        <div class="grid-2">
                            <div class="card">
                                <div class="card-header">Wellora App Resources</div>
                                <ul style="margin-top: 5px; padding-left: 20px;">
                                    <li>"Workplace Anxiety" guided meditation series</li>
                                    <li>"Managing Criticism" CBT module</li>
                                    <li>Headache tracking and analysis tool</li>
                                    <li>Sleep quality assessment questionnaire</li>
                                </ul>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">Recommended Reading</div>
                                <ul style="margin-top: 5px; padding-left: 20px;">
                                    <li>"The Anxiety and Phobia Workbook" by Edmund J. Bourne</li>
                                    <li>"The Headache Healer's Handbook" by Jan Mundo</li>
                                    <li>"Difficult Conversations" by Douglas Stone et al.</li>
                                </ul>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">Support Services</div>
                                <ul style="margin-top: 5px; padding-left: 20px;">
                                    <li>Wellora Support Line: (800) 555-8925</li>
                                    <li>Crisis Text Line: Text HOME to 741741</li>
                                    <li>Local Support Group: Anxiety & Stress Management Group (Thursdays, 7pm)</li>
                                </ul>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">Online Resources</div>
                                <ul style="margin-top: 5px; padding-left: 20px;">
                                    <li>www.wellora.com/patient-resources</li>
                                    <li>www.anxietycanada.com</li>
                                    <li>www.headacheresourcecenter.org</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="footer">
                            <p>© ${new Date().getFullYear()} Wellora Health Technologies | Report ID: ${reportId}</p>
                            <p>Generated by Wellora AI Medical Insight Technology | HIPAA Compliant</p>
                            <p>This document is confidential and intended for healthcare professional use only.</p>
                        </div>
                    </body>
                    </html>
                    `;
                    
                    // Open a new window and write the HTML directly to it
                    const reportWindow = window.open('', '_blank');
                    
                    if (!reportWindow) {
                        throw new Error('Pop-up was blocked. Please allow pop-ups for this site.');
                    }
                    
                    reportWindow.document.open();
                    reportWindow.document.write(reportHtml);
                    reportWindow.document.close();
                    
                    // Success notification
                    showNotification('Report opened in new window. Click "Print / Save as PDF" to download.', 'success');
                    
                } catch (error) {
                    console.error('Error generating report:', error);
                    showNotification('Error creating report: ' + error.message, 'error');
                }
            }
            
            // Recording Modal
            const openRecordingModalBtn = document.getElementById('openRecordingModalBtn');
            const recordingModal = document.getElementById('recordingModal');
            const closeRecordingModal = document.getElementById('closeRecordingModal');
            
            if (openRecordingModalBtn && recordingModal) {
                openRecordingModalBtn.addEventListener('click', () => {
                    recordingModal.classList.remove('hidden');
                    setTimeout(() => {
                        const content = recordingModal.querySelector('.modal-content');
                        if (content) {
                            content.classList.remove('scale-95', 'opacity-0');
                            content.classList.add('scale-100', 'opacity-100');
                        }

                        // Initialize AI conversation analysis functionality
                        initializeAIConversationAnalysis();
                    }, 10);
                });
            }
            
            if (closeRecordingModal && recordingModal) {
                closeRecordingModal.addEventListener('click', () => {
                    stopRecording(); // Stop any active recording
                    
                    const content = recordingModal.querySelector('.modal-content');
                    if (content) {
                        content.classList.remove('scale-100', 'opacity-100');
                        content.classList.add('scale-95', 'opacity-0');
                    }
                    
                    setTimeout(() => {
                        recordingModal.classList.add('hidden');
                    }, 300);
                });
            }
            
            // Device selection
            const changeDeviceBtn = document.getElementById('changeRecordingDevice');
            if (changeDeviceBtn) {
                changeDeviceBtn.addEventListener('click', () => {
                    const deviceElement = document.getElementById('recordingDevice');
                    const devices = ['Internal Microphone', 'Bluetooth Headset', 'USB Microphone', 'External Microphone'];
                    const currentDevice = deviceElement.textContent;
                    const currentIndex = devices.indexOf(currentDevice);
                    const nextIndex = (currentIndex + 1) % devices.length;
                    
                    deviceElement.textContent = devices[nextIndex];
                });
            }
            
            // Handle play buttons for existing recordings in the table
            document.querySelectorAll('.play-recording-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    alert('Playing recording...');
                });
            });
            
            // Handle download buttons for existing recordings in the table
            document.querySelectorAll('.download-recording-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    alert('Downloading recording...');
                });
            });
            
            // Handle delete buttons for existing recordings in the table
            document.querySelectorAll('.delete-recording-btn').forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.stopPropagation();
                    if (confirm('Are you sure you want to delete this recording?')) {
                        const row = this.closest('tr');
                        if (row) {
                            row.remove();
                        }
                    }
                });
            });
            
            // Add click handler for recording rows
            document.querySelectorAll('.recording-row').forEach(row => {
                row.addEventListener('click', function() {
                    const id = this.dataset.id;
                    const name = this.dataset.name;
                    const isTranscribed = this.dataset.transcribed === 'true';
                    
                    openRecordingAnalysisModal(id, name, isTranscribed);
                });
            });
            
            // Close modal when clicking outside content
            recordingModal && recordingModal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeRecordingModal.click();
                }
            });
            
            qrCodeModal && qrCodeModal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeQrModal.click();
                }
            });
            
            const analysisModal = document.getElementById('recordingAnalysisModal');
            if (analysisModal) {
                const closeAnalysisBtn = document.getElementById('closeAnalysisModal');
                if (closeAnalysisBtn) {
                    closeAnalysisBtn.addEventListener('click', function() {
                        const modalContent = analysisModal.querySelector('.modal-content');
                        if (modalContent) {
                            modalContent.classList.remove('scale-100', 'opacity-100');
                            modalContent.classList.add('scale-95', 'opacity-0');
                        }
                        
                        setTimeout(() => {
                            analysisModal.classList.add('hidden');
                        }, 300);
                    });
                }
                
                analysisModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeAnalysisBtn.click();
                    }
                });
            }
            
            // Setup medication info modal
            const medicationModal = document.getElementById('medicationInfoModal');
            if (medicationModal) {
                const closeMedicationBtn = document.getElementById('closeMedicationModal');
                if (closeMedicationBtn) {
                    closeMedicationBtn.addEventListener('click', function() {
                        const modalContent = medicationModal.querySelector('.modal-content');
                        if (modalContent) {
                            modalContent.classList.remove('scale-100', 'opacity-100');
                            modalContent.classList.add('scale-95', 'opacity-0');
                        }
                        
                        setTimeout(() => {
                            medicationModal.classList.add('hidden');
                        }, 300);
                    });
                }
                
                medicationModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeMedicationBtn.click();
                    }
                });
            }
        });

        // AI Conversation Analysis Functions
        function initializeAIConversationAnalysis() {
            // File upload functionality
            const uploadArea = document.getElementById('audioUploadArea');
            const fileInput = document.getElementById('audioFileInput');
            const audioPlayer = document.getElementById('audioPlayer');
            const audioSource = document.getElementById('audioSource');
            const audioPlayerSection = document.getElementById('audioPlayerSection');
            const audioFileName = document.getElementById('audioFileName');
            const audioDuration = document.getElementById('audioDuration');
            const startAnalysisBtn = document.getElementById('startAnalysisBtn');

            if (!uploadArea || !fileInput) return; // Exit if elements don't exist

            // Drag and drop functionality
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('border-blue-500', 'bg-blue-50');
            });
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('border-blue-500', 'bg-blue-50');
            });
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('border-blue-500', 'bg-blue-50');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload(files[0]);
                }
            });

            // File input change
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFileUpload(e.target.files[0]);
                }
            });

            // Start analysis button
            if (startAnalysisBtn) {
                startAnalysisBtn.addEventListener('click', () => {
                    startAIAnalysis();
                });
            }
        }

        // Handle file upload
        function handleFileUpload(file) {
            // Validate file type
            const allowedTypes = ['audio/mp3', 'audio/mpeg', 'audio/wav', 'audio/m4a'];
            if (!allowedTypes.includes(file.type)) {
                alert('Alleen MP3, WAV en M4A bestanden zijn toegestaan.');
                return;
            }

            // Validate file size (100MB max)
            if (file.size > 100 * 1024 * 1024) {
                alert('Bestand is te groot. Maximum grootte is 100MB.');
                return;
            }

            // Create object URL for the file
            const fileURL = URL.createObjectURL(file);

            // Update UI
            const audioFileName = document.getElementById('audioFileName');
            const audioSource = document.getElementById('audioSource');
            const audioPlayer = document.getElementById('audioPlayer');
            const audioPlayerSection = document.getElementById('audioPlayerSection');

            if (audioFileName) audioFileName.textContent = file.name;
            if (audioSource) audioSource.src = fileURL;
            if (audioPlayer) audioPlayer.load();
            if (audioPlayerSection) audioPlayerSection.classList.remove('hidden');

            // Update upload area
            const uploadContent = document.querySelector('.upload-content');
            if (uploadContent) {
                uploadContent.innerHTML = `
                    <i class="fas fa-check-circle text-4xl text-green-500 mb-4"></i>
                    <p class="text-gray-700 font-medium mb-2">Bestand succesvol geüpload</p>
                    <p class="text-sm text-gray-500">${file.name} (${formatFileSize(file.size)})</p>
                `;
            }

            // Get audio duration when metadata loads
            if (audioPlayer) {
                audioPlayer.addEventListener('loadedmetadata', () => {
                    const duration = formatTime(audioPlayer.duration);
                    const audioDuration = document.getElementById('audioDuration');
                    if (audioDuration) audioDuration.textContent = duration;
                });
            }
        }

        // Start AI Analysis
        function startAIAnalysis() {
            // Show transcription section
            const transcriptionSection = document.getElementById('transcriptionSection');
            if (transcriptionSection) transcriptionSection.classList.remove('hidden');

            // Hide no insights message
            const noInsightsMessage = document.getElementById('noInsightsMessage');
            if (noInsightsMessage) noInsightsMessage.classList.add('hidden');

            // Start the analysis process
            simulateAIAnalysis();
        }

        // Simulate AI Analysis Process
        function simulateAIAnalysis() {
            const transcriptionChat = document.getElementById('transcriptionChat');
            const progressBar = document.getElementById('progressBar');
            const progressPercentage = document.getElementById('progressPercentage');
            const insightsContainer = document.getElementById('insightsContainer');

            if (!transcriptionChat) return;

            // Clear previous content
            transcriptionChat.innerHTML = '';

            // Simulate conversation transcription with chat bubbles
            const conversation = [
                { speaker: 'patient', time: '00:15', text: 'Ik neem Coveram voor mijn bloeddruk, maar de laatste tijd voel ik me duizelig.' },
                { speaker: 'patient', time: '00:28', text: 'Het is alsof de kamer begint te draaien en ik ben bang dat ik ga vallen.' },
                { speaker: 'therapist', time: '00:45', text: 'Ik begrijp je bezorgdheid over de duizeligheid. Hoe vaak ervaar je deze duizelige momenten?' },
                { speaker: 'patient', time: '01:02', text: 'Vooral \'s ochtends als ik opstaan. Die ademhalingstechniek die je vorige keer voorstelde helpt wel een beetje.' },
                { speaker: 'therapist', time: '01:20', text: 'Dat is goed om te horen. Heb je ook andere bijwerkingen opgemerkt sinds je Coveram gebruikt?' },
                { speaker: 'patient', time: '01:35', text: 'Soms voel ik me ook angstig, vooral als de duizeligheid optreedt. Ik maak me zorgen over mijn gezondheid.' }
            ];

            let currentIndex = 0;
            const totalMessages = conversation.length;

            // Add messages progressively
            const addMessage = () => {
                if (currentIndex < conversation.length) {
                    const message = conversation[currentIndex];
                    addChatMessage(message.speaker, message.text, message.time);

                    // Update progress
                    const progress = ((currentIndex + 1) / totalMessages) * 100;
                    if (progressBar) progressBar.style.width = progress + '%';
                    if (progressPercentage) progressPercentage.textContent = Math.round(progress) + '%';

                    // Add insights based on conversation content
                    if (currentIndex === 1) {
                        addInsightCard('dizziness', 'Duizeligheid', 'Patiënt meldt duizeligheidsklachten, mogelijk gerelateerd aan medicatie', 'warning');
                    }
                    if (currentIndex === 3) {
                        addInsightCard('medication', 'Medicatie: Coveram', 'Bloeddrukverlagende medicatie - mogelijke bijwerking duizeligheid', 'info');
                    }
                    if (currentIndex === 5) {
                        addInsightCard('anxiety', 'Angst & Bezorgdheid', 'Secundaire angst door fysieke symptomen - behandeling nodig', 'danger');
                    }

                    currentIndex++;
                    setTimeout(addMessage, 2000); // Add next message after 2 seconds
                } else {
                    // Analysis complete
                    const analysisStatus = document.getElementById('analysisStatus');
                    if (analysisStatus) {
                        analysisStatus.innerHTML = `
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                            <span class="text-green-600">Analyse voltooid</span>
                        `;
                    }

                    // Show summary section
                    const summarySection = document.getElementById('summarySection');
                    if (summarySection) summarySection.classList.remove('hidden');

                    // Add final insights
                    setTimeout(() => {
                        addInsightCard('breathing', 'Ademhalingstechnieken', 'Positieve respons op eerder geleerde technieken', 'success');
                        addInsightCard('sleep', 'Slaapkwaliteit', 'Mogelijk beïnvloed door medicatie en angst', 'warning');
                    }, 1000);
                }
            };

            // Start adding messages
            setTimeout(addMessage, 1000);
        }

        // Add chat message to transcription
        function addChatMessage(speaker, text, time) {
            const transcriptionChat = document.getElementById('transcriptionChat');
            if (!transcriptionChat) return;

            const isPatient = speaker === 'patient';

            const messageDiv = document.createElement('div');
            messageDiv.className = `flex ${isPatient ? 'justify-start' : 'justify-end'} mb-4`;

            messageDiv.innerHTML = `
                <div class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${isPatient ? 'bg-blue-100 text-blue-900' : 'bg-green-100 text-green-900'}">
                    <div class="flex items-center mb-1">
                        <i class="fas ${isPatient ? 'fa-user' : 'fa-user-md'} text-xs mr-2"></i>
                        <span class="text-xs font-medium">${isPatient ? 'Patiënt' : 'Therapeut'}</span>
                        <span class="text-xs text-gray-500 ml-auto">${time}</span>
                    </div>
                    <p class="text-sm">${text}</p>
                </div>
            `;

            transcriptionChat.appendChild(messageDiv);
            transcriptionChat.scrollTop = transcriptionChat.scrollHeight;
        }

        // Add insight card to the right panel
        function addInsightCard(type, title, description, severity) {
            const insightsContainer = document.getElementById('insightsContainer');
            if (!insightsContainer) return;

            const colors = {
                'info': 'border-blue-200 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200',
                'warning': 'border-yellow-200 dark:border-yellow-700 bg-yellow-50 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200',
                'danger': 'border-red-200 dark:border-red-700 bg-red-50 dark:bg-red-900/30 text-red-800 dark:text-red-200',
                'success': 'border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/30 text-green-800 dark:text-green-200'
            };

            const icons = {
                'dizziness': 'fa-dizzy',
                'medication': 'fa-pills',
                'anxiety': 'fa-heart',
                'breathing': 'fa-lungs',
                'sleep': 'fa-bed'
            };

            const cardDiv = document.createElement('div');
            cardDiv.className = `border rounded-lg p-4 ${colors[severity]} animate-fade-in`;

            cardDiv.innerHTML = `
                <div class="flex items-start justify-between">
                    <div class="flex items-start">
                        <i class="fas ${icons[type]} text-lg mr-3 mt-1"></i>
                        <div>
                            <h4 class="font-semibold text-sm mb-1">${title}</h4>
                            <p class="text-xs">${description}</p>
                            ${type === 'medication' ? `
                                <button onclick="showMedicationInfo('${title}')" class="mt-2 text-xs bg-white/50 dark:bg-gray-700/50 hover:bg-white/80 dark:hover:bg-gray-600/80 px-2 py-1 rounded border border-gray-300 dark:border-gray-600 transition-all">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    Bijsluiter
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            `;

            insightsContainer.appendChild(cardDiv);
        }

        // Show medication information
        function showMedicationInfo(medicationName) {
            alert(`Bijsluiter informatie voor ${medicationName}:\n\nMogelijke bijwerkingen:\n- Duizeligheid\n- Hoofdpijn\n- Vermoeidheid\n- Hoest\n\nNeem contact op met uw arts als bijwerkingen aanhouden.`);
        }

        // Format file size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Format time
        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        // Add event listener for summary button
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listener when DOM is ready
            setTimeout(() => {
                const summaryBtn = document.getElementById('generateSummaryBtn');
                if (summaryBtn) {
                    summaryBtn.addEventListener('click', generateSummary);
                }
            }, 1000);
        });

        // Generate summary and treatment plan
        function generateSummary() {
            // Create summary modal
            const summaryModal = document.createElement('div');
            summaryModal.className = 'fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-[60]';
            summaryModal.innerHTML = `
                <div class="bg-white dark:bg-gray-900 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto m-4">
                    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex justify-between items-center">
                            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Samenvatting & Behandelplan</h2>
                            <button onclick="this.closest('.fixed').remove()" class="text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Summary -->
                            <div>
                                <h3 class="text-lg font-semibold mb-4 text-blue-600 dark:text-blue-400">Gesprek Samenvatting</h3>
                                <div class="space-y-3 text-sm text-gray-700 dark:text-gray-300">
                                    <p><strong>Hoofdklachten:</strong> Duizeligheid, angst, zorgen over medicatie</p>
                                    <p><strong>Medicatie:</strong> Coveram (bloeddrukverlagende medicatie)</p>
                                    <p><strong>Symptomen:</strong> Duizeligheid vooral 's ochtends, angstgevoelens</p>
                                    <p><strong>Positieve aspecten:</strong> Ademhalingstechnieken helpen</p>
                                </div>
                            </div>

                            <!-- Treatment Plan -->
                            <div>
                                <h3 class="text-lg font-semibold mb-4 text-green-600 dark:text-green-400">Behandelplan</h3>
                                <div class="space-y-3 text-sm text-gray-700 dark:text-gray-300">
                                    <div class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                                        <span>Medicatie evaluatie met huisarts - mogelijke dosisaanpassing</span>
                                    </div>
                                    <div class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                                        <span>Voortzetten ademhalingstechnieken - 2x daags oefenen</span>
                                    </div>
                                    <div class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                                        <span>Angstmanagement technieken introduceren</span>
                                    </div>
                                    <div class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                                        <span>Follow-up afspraak binnen 2 weken</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700 flex justify-between">
                            <button onclick="sendToN8N()" class="px-4 py-2 bg-blue-500 hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 text-white rounded-lg transition-all">
                                <i class="fas fa-cloud-upload-alt mr-2"></i>
                                Verstuur naar N8N
                            </button>
                            <button onclick="exportToPDF()" class="px-4 py-2 bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 text-white rounded-lg transition-all">
                                <i class="fas fa-file-pdf mr-2"></i>
                                Exporteer PDF
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(summaryModal);
        }

        // Send data to N8N webhook
        async function sendToN8N() {
            const webhookUrl = 'https://wellora.app.n8n.cloud/webhook-test/webhook';

            // Get the uploaded audio file
            const audioFileInput = document.getElementById('audioFileInput');
            const file = audioFileInput?.files[0];

            if (!file) {
                alert('Geen audio bestand gevonden om te versturen.');
                return;
            }

            // Create FormData to send the file
            const formData = new FormData();
            formData.append('audio', file);
            formData.append('analysis', JSON.stringify({
                timestamp: new Date().toISOString(),
                insights: ['duizeligheid', 'medicatie: Coveram', 'angst', 'ademhalingstechnieken'],
                summary: 'Patiënt meldt duizeligheidsklachten mogelijk gerelateerd aan Coveram medicatie',
                treatment_plan: [
                    'Medicatie evaluatie met huisarts',
                    'Voortzetten ademhalingstechnieken',
                    'Angstmanagement technieken',
                    'Follow-up binnen 2 weken'
                ]
            }));

            try {
                const response = await fetch(webhookUrl, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    alert('Data succesvol verzonden naar N8N!');
                } else {
                    alert('Fout bij verzenden naar N8N. Probeer opnieuw.');
                }
            } catch (error) {
                console.error('Error sending to N8N:', error);
                alert('Fout bij verzenden naar N8N. Controleer uw internetverbinding.');
            }
        }

        // Export to PDF (placeholder)
        function exportToPDF() {
            alert('PDF export functionaliteit wordt geïmplementeerd...');
        }
    </script>
</body>
</html>