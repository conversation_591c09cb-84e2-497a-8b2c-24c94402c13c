<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wellora SOAP Notities</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #0F6FFF;
            --secondary: #00B0B6;
            --tertiary: #00D695;
            --dark: #0A2540;
            --light: #F7F9FC;
            --gray-900: #111827;
            --gray-800: #1F2937;
            --gray-700: #374151;
            --gray-600: #4B5563;
            --gray-500: #6B7280;
            --gray-400: #9CA3AF;
            --gray-300: #D1D5DB;
            --gray-200: #E5E7EB;
            --gray-100: #F3F4F6;
            --blue-100: #EBF5FF;
            --blue-500: #3B82F6;
            --green-100: #ECFDF5;
            --green-500: #10B981;
            --red-100: #FEF2F2;
            --red-500: #EF4444;
            --amber-100: #FFFBEB;
            --amber-500: #F59E0B;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            background-color: var(--light);
            color: var(--dark);
            line-height: 1.6;
            font-size: 14px;
            padding-bottom: 40px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            padding: 20px 0;
            border-bottom: 1px solid var(--gray-200);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo-container {
            display: flex;
            align-items: center;
        }
        
        .logo {
            height: 50px;
            margin-right: 15px;
        }
        
        h1 {
            font-size: 28px;
            font-weight: 600;
            color: var(--dark);
        }
        
        .subtitle {
            color: var(--gray-500);
            font-size: 16px;
            margin-top: 5px;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
        }
        
        button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.2s ease;
        }
        
        .primary-button {
            background-color: var(--primary);
            color: white;
        }
        
        .secondary-button {
            background-color: var(--light);
            color: var(--dark);
            border: 1px solid var(--gray-300);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .patient-info {
            display: flex;
            flex-wrap: wrap;
            padding: 20px;
            background-color: var(--blue-100);
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 4px solid var(--blue-500);
        }
        
        .patient-profile {
            display: flex;
            align-items: center;
            margin-right: 50px;
        }
        
        .patient-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background-color: var(--primary);
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-weight: bold;
            font-size: 24px;
            margin-right: 15px;
        }
        
        .patient-basics h2 {
            font-size: 18px;
            font-weight: 600;
        }
        
        .patient-meta {
            color: var(--gray-600);
            font-size: 14px;
            margin-top: 3px;
        }
        
        .patient-vitals {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .vital-item {
            display: flex;
            align-items: center;
        }
        
        .vital-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
            color: var(--primary);
        }
        
        .vital-data h3 {
            font-size: 14px;
            color: var(--gray-600);
            font-weight: 400;
        }
        
        .vital-data p {
            font-size: 16px;
            font-weight: 600;
            color: var(--dark);
        }
        
        .soap-container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
        }
        
        .soap-section {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .soap-section:hover {
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .soap-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .soap-title {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .soap-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .s-icon {
            background-color: var(--amber-100);
            color: var(--amber-500);
        }
        
        .o-icon {
            background-color: var(--blue-100);
            color: var(--blue-500);
        }
        
        .a-icon {
            background-color: var(--green-100);
            color: var(--green-500);
        }
        
        .p-icon {
            background-color: var(--red-100);
            color: var(--red-500);
        }
        
        .soap-title h3 {
            font-size: 18px;
            font-weight: 600;
        }
        
        .soap-tools {
            display: flex;
            gap: 10px;
        }
        
        .tool-button {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background-color: var(--light);
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--gray-600);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .tool-button:hover {
            background-color: var(--gray-200);
            color: var(--dark);
        }
        
        .soap-content {
            padding: 20px;
        }
        
        .soap-content p {
            color: var(--gray-700);
            line-height: 1.7;
        }
        
        .tag-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 15px;
        }
        
        .tag {
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .symptom-tag {
            background-color: var(--amber-100);
            color: var(--amber-500);
        }
        
        .observation-tag {
            background-color: var(--blue-100);
            color: var(--blue-500);
        }
        
        .assessment-tag {
            background-color: var(--green-100);
            color: var(--green-500);
        }
        
        .treatment-tag {
            background-color: var(--red-100);
            color: var(--red-500);
        }
        
        .checklist {
            list-style-type: none;
            margin-top: 15px;
        }
        
        .checklist li {
            display: flex;
            align-items: flex-start;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--gray-200);
        }
        
        .checklist li:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .check-icon {
            color: var(--green-500);
            margin-right: 10px;
            flex-shrink: 0;
            margin-top: 3px;
        }
        
        .footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 0;
            margin-top: 30px;
            border-top: 1px solid var(--gray-200);
            color: var(--gray-500);
            font-size: 12px;
        }
        
        .signature-section {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid var(--gray-200);
            display: flex;
            justify-content: space-between;
        }
        
        .signature-box {
            width: 45%;
        }
        
        .signature-line {
            margin-top: 40px;
            border-top: 1px solid var(--gray-400);
            padding-top: 5px;
            color: var(--gray-700);
            font-weight: 500;
        }
        
        .meta-info {
            margin-top: 2px;
            color: var(--gray-500);
            font-size: 12px;
        }
        
        .print-actions {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
        }
        
        .floating-button {
            width: 50px;
            height: 50px;
            border-radius: 25px;
            background-color: var(--primary);
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }
        
        .floating-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
        }

        @media print {
            .print-actions,
            .header-actions {
                display: none;
            }
            
            body {
                padding-bottom: 0;
            }
            
            .soap-container {
                gap: 20px;
            }
            
            .soap-section {
                break-inside: avoid;
            }
            
            .container {
                max-width: 100%;
                padding: 0;
            }
        }
        
        @media (max-width: 768px) {
            .patient-profile {
                margin-right: 0;
                margin-bottom: 20px;
                width: 100%;
            }
            
            .patient-vitals {
                width: 100%;
            }
            
            .vital-item {
                width: 45%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div>
                <div class="logo-container">
                    <img src="../../elpscpv2rd.png" alt="Wellora Logo" class="logo">
                    <div>
                        <h1>SOAP Notities</h1>
                        <div class="subtitle">Uitgebreide Sessiedocumentatie</div>
                    </div>
                </div>
            </div>
            <div class="header-actions">
                <button class="secondary-button">
                    <i class="fas fa-share-alt"></i>
                    Delen
                </button>
                <button class="primary-button" onclick="window.print()">
                    <i class="fas fa-print"></i>
                    Afdrukken
                </button>
            </div>
        </header>
        
        <div class="patient-info">
            <div class="patient-profile">
                <div class="patient-avatar">EW</div>
                <div class="patient-basics">
                    <h2>Emma Wilson</h2>
                    <div class="patient-meta">34 jaar oud • Vrouw • Sessie #12 • 1 mei 2024</div>
                </div>
            </div>
            <div class="patient-vitals">
                <div class="vital-item">
                    <div class="vital-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="vital-data">
                        <h3>Risiconiveau</h3>
                        <p>Gemiddeld</p>
                    </div>
                </div>
                <div class="vital-item">
                    <div class="vital-icon">
                        <i class="fas fa-heartbeat"></i>
                    </div>
                    <div class="vital-data">
                        <h3>Angstscore</h3>
                        <p>12/21 (GAD-7)</p>
                    </div>
                </div>
                <div class="vital-item">
                    <div class="vital-icon">
                        <i class="fas fa-moon"></i>
                    </div>
                    <div class="vital-data">
                        <h3>Slaapefficiëntie</h3>
                        <p>72%</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="soap-container">
            <!-- Subjective Section -->
            <div class="soap-section">
                <div class="soap-header">
                    <div class="soap-title">
                        <div class="soap-icon s-icon">
                            <i class="fas fa-comment"></i>
                        </div>
                        <h3>Subjectief</h3>
                    </div>
                    <div class="soap-tools">
                        <div class="tool-button">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="tool-button">
                            <i class="fas fa-ellipsis-h"></i>
                        </div>
                    </div>
                </div>
                <div class="soap-content">
                    <p>Patiënt meldt aanhoudende angstsymptomen, maar merkt deze week een lichte verbetering op. Ze beschrijft voornamelijk angstgevoelens tijdens werkuren (9:00-17:00) met piekangstniveaus tijdens teamvergaderingen en deadlinebesprekingen. Slaap is licht verbeterd met de implementatie van de avondmindfulnessroutine, hoewel ze nog steeds ongeveer 45 minuten nodig heeft om in slaap te vallen (verbeterd van 65 minuten). Patiënt meldt dat ze de mindfulness-oefening 4 van de 7 aanbevolen dagen heeft voltooid.</p>
                    
                    <p>Patiënt verklaart: "Ik heb nog steeds piekergedachten bij het slapengaan, maar ze zijn enigszins minder intens wanneer ik de ademhalingsoefeningen doe. Werk triggert nog steeds mijn angst, vooral wanneer mijn baas last-minute vergaderingen plant."</p>
                    
                    <div class="tag-list">
                        <div class="tag symptom-tag">Angst</div>
                        <div class="tag symptom-tag">Slaapstoornis</div>
                        <div class="tag symptom-tag">Werkstress</div>
                        <div class="tag symptom-tag">Piekergedachten</div>
                    </div>
                </div>
            </div>
            
            <!-- Objective Section -->
            <div class="soap-section">
                <div class="soap-header">
                    <div class="soap-title">
                        <div class="soap-icon o-icon">
                            <i class="fas fa-eye"></i>
                        </div>
                        <h3>Objectief</h3>
                    </div>
                    <div class="soap-tools">
                        <div class="tool-button">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="tool-button">
                            <i class="fas fa-ellipsis-h"></i>
                        </div>
                    </div>
                </div>
                <div class="soap-content">
                    <p>Patiënt verscheen met passende uitstraling en hygiëne. Spraak was in normaal tempo en volume. Affect was angstig maar verbeterd ten opzichte van de vorige sessie. Behield goed oogcontact gedurende de sessie. GAD-7 score van 12/21 (verlaging van 14/21 in de vorige sessie). Slaapdagboek geeft een gemiddelde van 6,2 uur slaap per nacht aan (voorheen 5,8) met een slaapefficiëntie van 72%.</p>
                    
                    <p>Taalkundige analyse van de sessie-opname toonde verminderde aarzeling bij het bespreken van copingstrategieën (15% minder pauzes dan in de vorige sessie). Niet-verbale observaties omvatten verminderde psychomotorische agitatie, hoewel nog steeds friemelend met de handen bij het bespreken van werkstressoren.</p>
                    
                    <div class="tag-list">
                        <div class="tag observation-tag">GAD-7 Score: 12/21</div>
                        <div class="tag observation-tag">Slaapefficiëntie: 72%</div>
                        <div class="tag observation-tag">Oefening Naleving: 4/7 dagen</div>
                        <div class="tag observation-tag">Verminderde Aarzeling</div>
                    </div>
                </div>
            </div>
            
            <!-- Assessment Section -->
            <div class="soap-section">
                <div class="soap-header">
                    <div class="soap-title">
                        <div class="soap-icon a-icon">
                            <i class="fas fa-stethoscope"></i>
                        </div>
                        <h3>Analyse</h3>
                    </div>
                    <div class="soap-tools">
                        <div class="tool-button">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="tool-button">
                            <i class="fas fa-ellipsis-h"></i>
                        </div>
                    </div>
                </div>
                <div class="soap-content">
                    <p>Emma blijft voldoen aan de criteria voor Gegeneraliseerde Angststoornis (F41.1) met opmerkelijke symptomen van overmatig piekeren, slaapstoornis en moeite met het beheersen van angstige gedachten. Comorbide Insomniestoornis (G47.00) gekenmerkt door moeilijkheden bij het inslapen. Werkomgeving blijft een significante trigger voor angstsymptomen, met bijzondere stress rond prestatieverwachtingen en sociale evaluatie.</p>
                    
                    <p>Patiënt toont positieve reactie op behandeling, zoals blijkt uit bescheiden verbeteringen in GAD-7 scores, slaapefficiëntie en verminderde aarzeling bij het bespreken van copingmechanismen. Gedeeltelijke naleving van de aanbevolen mindfulness-praktijk heeft waarschijnlijk bijgedragen aan de waargenomen verbeteringen. Perfectionistische gedachtenpatronen blijven angstsymptomen verergeren, met name in werkcontexten.</p>
                    
                    <div class="tag-list">
                        <div class="tag assessment-tag">Gegeneraliseerde Angststoornis (F41.1)</div>
                        <div class="tag assessment-tag">Insomniestoornis (G47.00)</div>
                        <div class="tag assessment-tag">Perfectionisme</div>
                        <div class="tag assessment-tag">Bescheiden Verbetering</div>
                    </div>
                </div>
            </div>
            
            <!-- Plan Section -->
            <div class="soap-section">
                <div class="soap-header">
                    <div class="soap-title">
                        <div class="soap-icon p-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <h3>Plan</h3>
                    </div>
                    <div class="soap-tools">
                        <div class="tool-button">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="tool-button">
                            <i class="fas fa-ellipsis-h"></i>
                        </div>
                    </div>
                </div>
                <div class="soap-content">
                    <p>Vervolg wekelijkse CGT-sessies met verhoogde focus op perfectionistische gedachtenpatronen en het stellen van werkplekgrenzen. Handhaaf het huidige medicatieregime (Sertraline 50mg dagelijks) met evaluatie van effectiviteit bij de volgende medicatie-evaluatie afspraak. Overweeg aanpassing van Melatonine dosering (momenteel 3mg) op basis van voortdurende slaapmonitoring.</p>
                    
                    <ul class="checklist">
                        <li>
                            <i class="fas fa-check-circle check-icon"></i>
                            <div>Vervolg geleide mindfulness-praktijk met nadruk op consistente dagelijkse implementatie (verhoog van 4/7 dagen naar 6/7 dagen). Gebruik Calm app's "Sleep Stories" programma om specifiek inslaapproblemen aan te pakken.</div>
                        </li>
                        <li>
                            <i class="fas fa-check-circle check-icon"></i>
                            <div>Implementeer werkplekgrenzentaak: gestructureerd protocol voor het beperken van e-mailcontrole en werkactiviteit buiten werktijd. Begin met een werkvrije buffer van 30 minuten voor het slapengaan, geleidelijk uitbreidend naar 90 minuten over drie weken.</div>
                        </li>
                        <li>
                            <i class="fas fa-check-circle check-icon"></i>
                            <div>Cognitieve herstructureringsopdracht gericht op perfectionistische gedachtenpatronen en catastroferen over werkprestaties, met dagelijks gedachtenlogboek gericht op alternatieve perspectieven.</div>
                        </li>
                        <li>
                            <i class="fas fa-check-circle check-icon"></i>
                            <div>Vervolg slaapdagboek voor de komende twee weken. Plan volgende afspraak voor 15 mei 2024, 10:00 uur.</div>
                        </li>
                    </ul>
                    
                    <div class="tag-list">
                        <div class="tag treatment-tag">CGT</div>
                        <div class="tag treatment-tag">Medicatiebeheer</div>
                        <div class="tag treatment-tag">Mindfulness</div>
                        <div class="tag treatment-tag">Cognitieve Herstructurering</div>
                        <div class="tag treatment-tag">Werkplekgrenzen</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-line">Dr. Sarah Matthews, MD</div>
                <div class="meta-info">Gediplomeerd Psychiater • NPI: **********</div>
            </div>
            <div class="signature-box">
                <div class="signature-line">Gegenereerd: 1 mei 2024, 11:32 uur</div>
                <div class="meta-info">Sessieduur: 50 minuten • Document ID: SOAP-EW-202405011032</div>
            </div>
        </div>
        
        <div class="footer">
            <div>© 2024 Wellora Health Technologies, Inc. Alle rechten voorbehouden.</div>
            <div>VERTROUWELIJKE MEDISCHE INFORMATIE</div>
        </div>
    </div>
    
    <div class="print-actions">
        <div class="floating-button" onclick="window.print()">
            <i class="fas fa-print"></i>
        </div>
    </div>
</body>
</html>